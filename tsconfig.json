{
	"compilerOptions": {
		"module": "CommonJS",
		"target": "ESNext",
		"moduleResolution": "node",
		"declaration": true,
		"sourceMap": false,
		"strict": true,
		"esModuleInterop": true,
		"experimentalDecorators": true,
		"emitDecoratorMetadata": true,
		"preserveConstEnums": true,
		/* Type Checking */
		"noImplicitAny": true,
		"strictNullChecks": true,
		"strictFunctionTypes": true,
		"noImplicitThis": true,
		"noUnusedLocals": true,
		"noUnusedParameters": true,
		"noImplicitReturns": true,
		"noFallthroughCasesInSwitch": true,
		"skipLibCheck": true,
		"noErrorTruncation": true,
		"outDir": "./lib",
		"stripInternal": true,
		"strictBindCallApply": true,
		"strictBuiltinIteratorReturn": true,
		"strictPropertyInitialization": true,
		"alwaysStrict": true,
		"types": [
			"node"
		]
	},
	"exclude": [
		"**/lib",
		"**/__test__",
		"**/node_modules",
		"**/.*/"
	],
	"include": [
		"src"
	]
}