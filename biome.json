{"$schema": "https://biomejs.dev/schemas/2.0.0/schema.json", "vcs": {"enabled": true, "clientKind": "git", "useIgnoreFile": true, "defaultBranch": "main"}, "files": {"ignoreUnknown": true, "includes": ["**/src/**", "!/node_modules/", "!/build", "!/lib", "!/__test__", "!/package.json", "!/tsconfig.json", "!/.vscode"]}, "formatter": {"enabled": true, "indentStyle": "tab", "indentWidth": 2, "lineWidth": 120, "lineEnding": "crlf", "formatWithErrors": true}, "assist": {"actions": {"source": {"organizeImports": "on"}}}, "linter": {"enabled": true, "rules": {"recommended": false, "security": {"noGlobalEval": "off"}, "suspicious": {"noExplicitAny": "off", "noAssignInExpressions": "off", "useAwait": "off", "noConfusingVoidType": "off", "noAsyncPromiseExecutor": "off", "noUnsafeDeclarationMerging": "off", "noEmptyInterface": "off", "noThenProperty": "off"}, "correctness": {"noNodejsModules": "off", "useImportExtensions": "off"}, "style": {"noDefaultExport": "off", "useBlockStatements": "off", "noParameterProperties": "off", "useNamingConvention": "off", "noNonNullAssertion": "off", "useForOf": "off", "useDefaultSwitchClause": "off", "noParameterAssign": "off", "useFilenamingConvention": "off", "useEnumInitializers": "off", "useExplicitLengthCheck": "off"}, "complexity": {"noForEach": "off", "noExcessiveCognitiveComplexity": "off", "noUselessConstructor": "off", "noBannedTypes": "off"}, "performance": {"noBarrelFile": "off", "noDelete": "off", "noReExportAll": "off", "useTopLevelRegex": "off", "noNamespaceImport": "off"}}}, "javascript": {"formatter": {"quoteStyle": "single", "semicolons": "always", "arrowParentheses": "asNeeded", "bracketSameLine": true}}}