name: Bug report
description: Report incorrect or unexpected behavior of a package
labels: [bug]
body:
  - type: markdown
    attributes:
      value: |
        Use Discord for questions: https://discord.com/invite/XNw2RZFzaP
  - type: textarea
    id: description
    attributes:
      label: Issue description
      description: |
        Describe the issue in as much detail as possible.

        Tip: You can attach images or log files by clicking this area to highlight it and then dragging files into it.
      placeholder: |
        Steps to reproduce with below code sample:
        1. do thing
        2. do thing in Discord client
        3. observe behavior
        4. see error logs below
    validations:
      required: true
  - type: textarea
    id: codesample
    attributes:
      label: Code sample
      description: Include a reproducible, minimal code sample.
      render: typescript
      placeholder: |
        Your code sample should be...
        ... Minimal - Use as little code as possible that still produces the same problem (and is understandable)
        ... Complete - Provide all parts someone else needs to reproduce your problem
        ... Reproducible - Test the code you're about to provide to make sure it reproduces the problem
  - type: input
    id: seyfert-version
    attributes:
      label: Package version
      description: Which version of are you using? Run `npm list <package>` in your project directory and paste the output.
    validations:
      required: true
  - type: input
    id: runtime-version
    attributes:
      label: What runtime and its version
      description: |
        Which version of the runtime are you using?
        Please include its version (`tsc --version`) as well.
      placeholder: Node.js version 18.+ or LTS
    validations:
      required: true
  - type: input
    id: os
    attributes:
      label: Operating system
      description: Which OS does your application run on?
  - type: dropdown
    id: priority
    attributes:
      label: Priority this issue should have
      description: Please be realistic. If you need to elaborate on your reasoning, please use the Issue description field above.
      options:
        - Low (slightly annoying)
        - Medium (should be fixed soon)
        - High (immediate attention needed)
    validations:
      required: true
  - type: dropdown
    id: intents
    attributes:
      label: Which gateway intents are you subscribing to?
      description: |
        Check your Client constructor options for the `intents` key.

        Tip: you can select multiple items
      options:
        - Not applicable
        - No Intents
        - Guilds
        - GuildMembers
        - GuildBans
        - GuildEmojisAndStickers
        - GuildIntegrations
        - GuildWebhooks
        - GuildInvites
        - GuildVoiceStates
        - GuildPresences
        - GuildMessages
        - GuildMessageReactions
        - GuildMessageTyping
        - DirectMessages
        - DirectMessageReactions
        - DirectMessageTyping
        - MessageContent
        - GuildScheduledEvents
      multiple: true
    validations:
      required: true
  - type: input
    id: dev-release
    attributes:
      label: I have tested this issue on a development release
      placeholder: commit hash
      description: |
        The issue might already be fixed in a development release or main.
