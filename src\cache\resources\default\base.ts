import type { UsingClient } from '../../../commands';
import { fakePromise } from '../../../common';
import type { GatewayIntentBits } from '../../../types';
import type { Cache, CacheFrom, ReturnCache } from '../../index';

export class BaseResource<T = any, S = any> {
	namespace = 'base';

	constructor(
		protected cache: Cache,
		readonly client: UsingClient,
	) {}

	//@ts-expect-error
	filter(data: any, id: string, from: CacheFrom) {
		return true;
	}

	get adapter() {
		return this.cache.adapter;
	}

	removeIfNI(intent: keyof typeof GatewayIntentBits, id: string) {
		if (!this.cache.hasIntent(intent)) {
			return this.remove(id);
		}
		return;
	}

	setIfNI(from: CacheFrom, intent: keyof typeof GatewayIntentBits, id: string, data: S) {
		if (!this.cache.hasIntent(intent)) {
			return this.set(from, id, data);
		}
	}

	get(id: string): ReturnCache<T | undefined> {
		return this.adapter.get(this.hashId(id));
	}

	bulk(ids: string[]): ReturnCache<T[]> {
		return fakePromise(this.adapter.bulkGet(ids.map(id => this.hashId(id)))).then(x => x.filter(y => y));
	}

	set(from: CacheFrom, id: string, data: S) {
		if (!this.filter(data, id, from)) return;
		return fakePromise(this.addToRelationship(id)).then(() => this.adapter.set(this.hashId(id), data));
	}

	patch(from: CacheFrom, id: string, data: S) {
		if (!this.filter(data, id, from)) return;
		return fakePromise(this.addToRelationship(id)).then(() => this.adapter.patch(this.hashId(id), data));
	}

	remove(id: string) {
		return fakePromise(this.removeToRelationship(id)).then(() => this.adapter.remove(this.hashId(id)));
	}

	keys(): ReturnCache<string[]> {
		return this.adapter.keys(this.namespace) as string[];
	}

	values(): ReturnCache<T[]> {
		return this.adapter.values(this.namespace) as T[];
	}

	count(): ReturnCache<number> {
		return this.adapter.count(this.namespace) as number;
	}

	contains(id: string): ReturnCache<boolean> {
		return this.adapter.contains(this.namespace, id) as boolean;
	}

	getToRelationship() {
		return this.adapter.getToRelationship(this.namespace);
	}

	addToRelationship(id: string | string[]) {
		return this.adapter.addToRelationship(this.namespace, id);
	}

	removeToRelationship(id: string | string[]) {
		return this.adapter.removeToRelationship(this.namespace, id);
	}

	flush() {
		return fakePromise(this.adapter.keys(this.namespace)).then(keys => {
			return fakePromise(this.adapter.bulkRemove(keys)).then(() => {
				return this.adapter.removeRelationship(this.namespace);
			});
		});
	}

	hashId(id: string) {
		return id.startsWith(this.namespace) ? id : `${this.namespace}.${id}`;
	}
}
