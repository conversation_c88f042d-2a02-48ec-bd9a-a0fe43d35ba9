<!DOCTYPE html><html class="default" lang="en" data-base="../"><head><meta charset="utf-8"/><meta http-equiv="x-ua-compatible" content="IE=edge"/><title>Transformers | seyfert</title><meta name="description" content="Documentation for seyfert"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="stylesheet" href="../assets/style.css"/><link rel="stylesheet" href="../assets/highlight.css"/><script defer src="../assets/main.js"></script><script async src="../assets/icons.js" id="tsd-icons-script"></script><script async src="../assets/search.js" id="tsd-search-script"></script><script async src="../assets/navigation.js" id="tsd-nav-script"></script><script async src="../assets/hierarchy.js" id="tsd-hierarchy-script"></script></head><body><script>document.documentElement.dataset.theme = localStorage.getItem("tsd-theme") || "os";document.body.style.display="none";setTimeout(() => window.app?app.showPage():document.body.style.removeProperty("display"),500)</script><header class="tsd-page-toolbar"><div class="tsd-toolbar-contents container"><a href="../index.html" class="title">seyfert</a><div id="tsd-toolbar-links"></div><button id="tsd-search-trigger" class="tsd-widget" aria-label="Search"><svg width="16" height="16" viewBox="0 0 16 16" fill="none" aria-hidden="true"><use href="../assets/icons.svg#icon-search"></use></svg></button><dialog id="tsd-search" aria-label="Search"><input role="combobox" id="tsd-search-input" aria-controls="tsd-search-results" aria-autocomplete="list" aria-expanded="true" autocapitalize="off" autocomplete="off" placeholder="Search the docs" maxLength="100"/><ul role="listbox" id="tsd-search-results"></ul><div id="tsd-search-status" aria-live="polite" aria-atomic="true"><div>Preparing search index...</div></div></dialog><a href="#" class="tsd-widget menu" id="tsd-toolbar-menu-trigger" data-toggle="menu" aria-label="Menu"><svg width="16" height="16" viewBox="0 0 16 16" fill="none" aria-hidden="true"><use href="../assets/icons.svg#icon-menu"></use></svg></a></div></header><div class="container container-main"><div class="col-content"><div class="tsd-page-title"><ul class="tsd-breadcrumb" aria-label="Breadcrumb"><li><a href="" aria-current="page">Transformers</a></li></ul><h1>Variable Transformers<code class="tsd-tag">Const</code></h1></div><div class="tsd-signature"><span class="tsd-kind-variable">Transformers</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>    <span class="tsd-kind-call-signature">AnonymousGuild</span><span class="tsd-signature-symbol">(</span><br/>        <span class="tsd-signature-symbol">...</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">[</span>client<span class="tsd-signature-symbol">:</span> <a href="../interfaces/UsingClient.html" class="tsd-signature-type tsd-kind-interface">UsingClient</a><span class="tsd-signature-symbol">,</span> data<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">APIPartialGuild</span><span class="tsd-signature-symbol">]</span><span class="tsd-signature-symbol">,</span><br/>    <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <a href="../classes/AnonymousGuild.html" class="tsd-signature-type tsd-kind-class">AnonymousGuild</a><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-call-signature">Application</span><span class="tsd-signature-symbol">(</span><br/>        <span class="tsd-signature-symbol">...</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">[</span>client<span class="tsd-signature-symbol">:</span> <a href="../interfaces/UsingClient.html" class="tsd-signature-type tsd-kind-interface">UsingClient</a><span class="tsd-signature-symbol">,</span> data<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">APIApplication</span><span class="tsd-signature-symbol">]</span><span class="tsd-signature-symbol">,</span><br/>    <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Application</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-call-signature">ApplicationEmoji</span><span class="tsd-signature-symbol">(</span><br/>        <span class="tsd-signature-symbol">...</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">[</span>client<span class="tsd-signature-symbol">:</span> <a href="../interfaces/UsingClient.html" class="tsd-signature-type tsd-kind-interface">UsingClient</a><span class="tsd-signature-symbol">,</span> data<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">APIApplicationEmoji</span><span class="tsd-signature-symbol">]</span><span class="tsd-signature-symbol">,</span><br/>    <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <a href="../classes/ApplicationEmoji.html" class="tsd-signature-type tsd-kind-class">ApplicationEmoji</a><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-call-signature">AutoModerationRule</span><span class="tsd-signature-symbol">(</span><br/>        <span class="tsd-signature-symbol">...</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">[</span>client<span class="tsd-signature-symbol">:</span> <a href="../interfaces/UsingClient.html" class="tsd-signature-type tsd-kind-interface">UsingClient</a><span class="tsd-signature-symbol">,</span> data<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">APIAutoModerationRule</span><span class="tsd-signature-symbol">]</span><span class="tsd-signature-symbol">,</span><br/>    <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <a href="../classes/AutoModerationRule.html" class="tsd-signature-type tsd-kind-class">AutoModerationRule</a><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-call-signature">BaseChannel</span><span class="tsd-signature-symbol">(</span><br/>        <span class="tsd-signature-symbol">...</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">[</span>client<span class="tsd-signature-symbol">:</span> <a href="../interfaces/UsingClient.html" class="tsd-signature-type tsd-kind-interface">UsingClient</a><span class="tsd-signature-symbol">,</span> data<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">APIChannelBase</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">ChannelType</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">]</span><span class="tsd-signature-symbol">,</span><br/>    <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <a href="../classes/BaseChannel.html" class="tsd-signature-type tsd-kind-class">BaseChannel</a><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-call-signature">BaseGuildChannel</span><span class="tsd-signature-symbol">(</span><br/>        <span class="tsd-signature-symbol">...</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">[</span>client<span class="tsd-signature-symbol">:</span> <a href="../interfaces/UsingClient.html" class="tsd-signature-type tsd-kind-interface">UsingClient</a><span class="tsd-signature-symbol">,</span> data<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">APIGuildChannel</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">ChannelType</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">]</span><span class="tsd-signature-symbol">,</span><br/>    <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <a href="../classes/BaseGuildChannel.html" class="tsd-signature-type tsd-kind-class">BaseGuildChannel</a><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-call-signature">CategoryChannel</span><span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">...</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">any</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <a href="../classes/CategoryChannel.html" class="tsd-signature-type tsd-kind-class">CategoryChannel</a><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-call-signature">ClientUser</span><span class="tsd-signature-symbol">(</span><br/>        <span class="tsd-signature-symbol">...</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">[</span><br/>            client<span class="tsd-signature-symbol">:</span> <a href="../interfaces/UsingClient.html" class="tsd-signature-type tsd-kind-interface">UsingClient</a><span class="tsd-signature-symbol">,</span><br/>            data<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">APIUser</span><span class="tsd-signature-symbol">,</span><br/>            application<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Pick</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIApplication</span><span class="tsd-signature-symbol">,</span> <span class="tsd-signature-type">&quot;id&quot;</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;flags&quot;</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>        <span class="tsd-signature-symbol">]</span><span class="tsd-signature-symbol">,</span><br/>    <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <a href="../classes/ClientUser.html" class="tsd-signature-type tsd-kind-class">ClientUser</a><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-call-signature">DirectoryChannel</span><span class="tsd-signature-symbol">(</span><br/>        <span class="tsd-signature-symbol">...</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">[</span>client<span class="tsd-signature-symbol">:</span> <a href="../interfaces/UsingClient.html" class="tsd-signature-type tsd-kind-interface">UsingClient</a><span class="tsd-signature-symbol">,</span> data<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">APIChannelBase</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">ChannelType</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">]</span><span class="tsd-signature-symbol">,</span><br/>    <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <a href="../classes/DirectoryChannel.html" class="tsd-signature-type tsd-kind-class">DirectoryChannel</a><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-call-signature">DMChannel</span><span class="tsd-signature-symbol">(</span><br/>        <span class="tsd-signature-symbol">...</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">[</span>client<span class="tsd-signature-symbol">:</span> <a href="../interfaces/UsingClient.html" class="tsd-signature-type tsd-kind-interface">UsingClient</a><span class="tsd-signature-symbol">,</span> data<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">APIChannelBase</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">ChannelType</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">]</span><span class="tsd-signature-symbol">,</span><br/>    <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <a href="../classes/DMChannel.html" class="tsd-signature-type tsd-kind-class">DMChannel</a><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-call-signature">Emoji</span><span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">...</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">[</span>client<span class="tsd-signature-symbol">:</span> <a href="../interfaces/UsingClient.html" class="tsd-signature-type tsd-kind-interface">UsingClient</a><span class="tsd-signature-symbol">,</span> data<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">APIEmoji</span><span class="tsd-signature-symbol">]</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <a href="../classes/Emoji.html" class="tsd-signature-type tsd-kind-class">Emoji</a><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-call-signature">Entitlement</span><span class="tsd-signature-symbol">(</span><br/>        <span class="tsd-signature-symbol">...</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">[</span>client<span class="tsd-signature-symbol">:</span> <a href="../interfaces/UsingClient.html" class="tsd-signature-type tsd-kind-interface">UsingClient</a><span class="tsd-signature-symbol">,</span> data<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">APIEntitlement</span><span class="tsd-signature-symbol">]</span><span class="tsd-signature-symbol">,</span><br/>    <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <a href="../classes/Entitlement.html" class="tsd-signature-type tsd-kind-class">Entitlement</a><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-call-signature">ForumChannel</span><span class="tsd-signature-symbol">(</span><br/>        <span class="tsd-signature-symbol">...</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">[</span>client<span class="tsd-signature-symbol">:</span> <a href="../interfaces/UsingClient.html" class="tsd-signature-type tsd-kind-interface">UsingClient</a><span class="tsd-signature-symbol">,</span> data<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">APIGuildChannel</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">ChannelType</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">]</span><span class="tsd-signature-symbol">,</span><br/>    <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <a href="../classes/ForumChannel.html" class="tsd-signature-type tsd-kind-class">ForumChannel</a><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-call-signature">Guild</span><span class="tsd-signature-symbol">&lt;</span><a class="tsd-signature-type tsd-kind-type-parameter" href="#guildstate">State</a> <span class="tsd-signature-keyword">extends</span> <span class="tsd-signature-keyword">keyof</span> <span class="tsd-signature-type">StructWhen</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">any</span><span class="tsd-signature-symbol">,</span> <span class="tsd-signature-type">&quot;cached&quot;</span><span class="tsd-signature-symbol">&gt;</span> <span class="tsd-signature-symbol">=</span> <span class="tsd-signature-type">&quot;api&quot;</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">(</span><br/>        <span class="tsd-signature-symbol">...</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">[</span><br/>            client<span class="tsd-signature-symbol">:</span> <a href="../interfaces/UsingClient.html" class="tsd-signature-type tsd-kind-interface">UsingClient</a><span class="tsd-signature-symbol">,</span><br/>            data<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">APIGuild</span><br/>            <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">GatewayGuildCreateDispatchData</span><span class="tsd-signature-symbol">,</span><br/>        <span class="tsd-signature-symbol">]</span><span class="tsd-signature-symbol">,</span><br/>    <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <a href="../classes/Guild.html" class="tsd-signature-type tsd-kind-class">Guild</a><span class="tsd-signature-symbol">&lt;</span><a class="tsd-signature-type tsd-kind-type-parameter" href="#guildstate">State</a><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-call-signature">GuildBan</span><span class="tsd-signature-symbol">(</span><br/>        <span class="tsd-signature-symbol">...</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">[</span><br/>            client<span class="tsd-signature-symbol">:</span> <a href="../interfaces/UsingClient.html" class="tsd-signature-type tsd-kind-interface">UsingClient</a><span class="tsd-signature-symbol">,</span><br/>            data<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">APIBan</span><br/>            <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">ActuallyBan</span><span class="tsd-signature-symbol">,</span><br/>            guildId<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">,</span><br/>        <span class="tsd-signature-symbol">]</span><span class="tsd-signature-symbol">,</span><br/>    <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <a href="../classes/GuildBan.html" class="tsd-signature-type tsd-kind-class">GuildBan</a><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-call-signature">GuildEmoji</span><span class="tsd-signature-symbol">(</span><br/>        <span class="tsd-signature-symbol">...</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">[</span>client<span class="tsd-signature-symbol">:</span> <a href="../interfaces/UsingClient.html" class="tsd-signature-type tsd-kind-interface">UsingClient</a><span class="tsd-signature-symbol">,</span> data<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">APIEmoji</span><span class="tsd-signature-symbol">,</span> guildId<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">]</span><span class="tsd-signature-symbol">,</span><br/>    <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <a href="../classes/GuildEmoji.html" class="tsd-signature-type tsd-kind-class">GuildEmoji</a><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-call-signature">GuildMember</span><span class="tsd-signature-symbol">(</span><br/>        <span class="tsd-signature-symbol">...</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">[</span><br/>            client<span class="tsd-signature-symbol">:</span> <a href="../interfaces/UsingClient.html" class="tsd-signature-type tsd-kind-interface">UsingClient</a><span class="tsd-signature-symbol">,</span><br/>            data<span class="tsd-signature-symbol">:</span> <a href="../types/GuildMemberData.html" class="tsd-signature-type tsd-kind-type-alias">GuildMemberData</a><span class="tsd-signature-symbol">,</span><br/>            user<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">APIUser</span><span class="tsd-signature-symbol">,</span><br/>            guildId<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">,</span><br/>        <span class="tsd-signature-symbol">]</span><span class="tsd-signature-symbol">,</span><br/>    <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <a href="../classes/GuildMember.html" class="tsd-signature-type tsd-kind-class">GuildMember</a><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-call-signature">GuildRole</span><span class="tsd-signature-symbol">(</span><br/>        <span class="tsd-signature-symbol">...</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">[</span>client<span class="tsd-signature-symbol">:</span> <a href="../interfaces/UsingClient.html" class="tsd-signature-type tsd-kind-interface">UsingClient</a><span class="tsd-signature-symbol">,</span> data<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">APIRole</span><span class="tsd-signature-symbol">,</span> guildId<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">]</span><span class="tsd-signature-symbol">,</span><br/>    <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <a href="../classes/GuildRole.html" class="tsd-signature-type tsd-kind-class">GuildRole</a><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-call-signature">GuildTemplate</span><span class="tsd-signature-symbol">(</span><br/>        <span class="tsd-signature-symbol">...</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">[</span>client<span class="tsd-signature-symbol">:</span> <a href="../interfaces/UsingClient.html" class="tsd-signature-type tsd-kind-interface">UsingClient</a><span class="tsd-signature-symbol">,</span> data<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">APITemplate</span><span class="tsd-signature-symbol">]</span><span class="tsd-signature-symbol">,</span><br/>    <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <a href="../classes/GuildTemplate.html" class="tsd-signature-type tsd-kind-class">GuildTemplate</a><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-call-signature">InteractionGuildMember</span><span class="tsd-signature-symbol">(</span><br/>        <span class="tsd-signature-symbol">...</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">[</span><br/>            client<span class="tsd-signature-symbol">:</span> <a href="../interfaces/UsingClient.html" class="tsd-signature-type tsd-kind-interface">UsingClient</a><span class="tsd-signature-symbol">,</span><br/>            data<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">APIInteractionDataResolvedGuildMember</span><span class="tsd-signature-symbol">,</span><br/>            user<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">APIUser</span><span class="tsd-signature-symbol">,</span><br/>            guildId<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">,</span><br/>        <span class="tsd-signature-symbol">]</span><span class="tsd-signature-symbol">,</span><br/>    <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <a href="../classes/InteractionGuildMember.html" class="tsd-signature-type tsd-kind-class">InteractionGuildMember</a><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-call-signature">MediaChannel</span><span class="tsd-signature-symbol">(</span><br/>        <span class="tsd-signature-symbol">...</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">[</span>client<span class="tsd-signature-symbol">:</span> <a href="../interfaces/UsingClient.html" class="tsd-signature-type tsd-kind-interface">UsingClient</a><span class="tsd-signature-symbol">,</span> data<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">APIGuildChannel</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">ChannelType</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">]</span><span class="tsd-signature-symbol">,</span><br/>    <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <a href="../classes/MediaChannel.html" class="tsd-signature-type tsd-kind-class">MediaChannel</a><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-call-signature">Message</span><span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">...</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">[</span>client<span class="tsd-signature-symbol">:</span> <a href="../interfaces/UsingClient.html" class="tsd-signature-type tsd-kind-interface">UsingClient</a><span class="tsd-signature-symbol">,</span> data<span class="tsd-signature-symbol">:</span> <a href="../types/MessageData.html" class="tsd-signature-type tsd-kind-type-alias">MessageData</a><span class="tsd-signature-symbol">]</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <a href="../classes/Message.html" class="tsd-signature-type tsd-kind-class">Message</a><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-call-signature">NewsChannel</span><span class="tsd-signature-symbol">(</span><br/>        <span class="tsd-signature-symbol">...</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">[</span>client<span class="tsd-signature-symbol">:</span> <a href="../interfaces/UsingClient.html" class="tsd-signature-type tsd-kind-interface">UsingClient</a><span class="tsd-signature-symbol">,</span> data<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">APIGuildChannel</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">ChannelType</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">]</span><span class="tsd-signature-symbol">,</span><br/>    <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <a href="../classes/NewsChannel.html" class="tsd-signature-type tsd-kind-class">NewsChannel</a><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-call-signature">OptionResolver</span><span class="tsd-signature-symbol">(</span><br/>        <span class="tsd-signature-symbol">...</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">[</span><br/>            client<span class="tsd-signature-symbol">:</span> <a href="../interfaces/UsingClient.html" class="tsd-signature-type tsd-kind-interface">UsingClient</a><span class="tsd-signature-symbol">,</span><br/>            options<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">APIApplicationCommandInteractionDataOption</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">,</span><br/>            parent<span class="tsd-signature-symbol">?:</span> <a href="../classes/Command.html" class="tsd-signature-type tsd-kind-class">Command</a><span class="tsd-signature-symbol">,</span><br/>            guildId<span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">,</span><br/>            resolved<span class="tsd-signature-symbol">?:</span> <a href="../types/ContextOptionsResolved.html" class="tsd-signature-type tsd-kind-type-alias">ContextOptionsResolved</a><span class="tsd-signature-symbol">,</span><br/>        <span class="tsd-signature-symbol">]</span><span class="tsd-signature-symbol">,</span><br/>    <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <a href="../classes/OptionResolver.html" class="tsd-signature-type tsd-kind-class">OptionResolver</a><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-call-signature">Poll</span><span class="tsd-signature-symbol">(</span><br/>        <span class="tsd-signature-symbol">...</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">[</span><br/>            client<span class="tsd-signature-symbol">:</span> <a href="../interfaces/UsingClient.html" class="tsd-signature-type tsd-kind-interface">UsingClient</a><span class="tsd-signature-symbol">,</span><br/>            data<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">APIPoll</span><span class="tsd-signature-symbol">,</span><br/>            channelId<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">,</span><br/>            messageId<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">,</span><br/>        <span class="tsd-signature-symbol">]</span><span class="tsd-signature-symbol">,</span><br/>    <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <a href="../classes/Poll.html" class="tsd-signature-type tsd-kind-class">Poll</a><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-call-signature">StageChannel</span><span class="tsd-signature-symbol">(</span><br/>        <span class="tsd-signature-symbol">...</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">[</span>client<span class="tsd-signature-symbol">:</span> <a href="../interfaces/UsingClient.html" class="tsd-signature-type tsd-kind-interface">UsingClient</a><span class="tsd-signature-symbol">,</span> data<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">APIGuildChannel</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">ChannelType</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">]</span><span class="tsd-signature-symbol">,</span><br/>    <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <a href="../classes/StageChannel.html" class="tsd-signature-type tsd-kind-class">StageChannel</a><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-call-signature">Sticker</span><span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">...</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">[</span>client<span class="tsd-signature-symbol">:</span> <a href="../interfaces/UsingClient.html" class="tsd-signature-type tsd-kind-interface">UsingClient</a><span class="tsd-signature-symbol">,</span> data<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">APISticker</span><span class="tsd-signature-symbol">]</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <a href="../classes/Sticker.html" class="tsd-signature-type tsd-kind-class">Sticker</a><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-call-signature">TextGuildChannel</span><span class="tsd-signature-symbol">(</span><br/>        <span class="tsd-signature-symbol">...</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">[</span>client<span class="tsd-signature-symbol">:</span> <a href="../interfaces/UsingClient.html" class="tsd-signature-type tsd-kind-interface">UsingClient</a><span class="tsd-signature-symbol">,</span> data<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">APIGuildChannel</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">ChannelType</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">]</span><span class="tsd-signature-symbol">,</span><br/>    <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <a href="../classes/TextGuildChannel.html" class="tsd-signature-type tsd-kind-class">TextGuildChannel</a><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-call-signature">ThreadChannel</span><span class="tsd-signature-symbol">(</span><br/>        <span class="tsd-signature-symbol">...</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">[</span>client<span class="tsd-signature-symbol">:</span> <a href="../interfaces/UsingClient.html" class="tsd-signature-type tsd-kind-interface">UsingClient</a><span class="tsd-signature-symbol">,</span> data<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">APIChannelBase</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">ChannelType</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">]</span><span class="tsd-signature-symbol">,</span><br/>    <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <a href="../classes/ThreadChannel.html" class="tsd-signature-type tsd-kind-class">ThreadChannel</a><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-call-signature">User</span><span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">...</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">[</span>client<span class="tsd-signature-symbol">:</span> <a href="../interfaces/UsingClient.html" class="tsd-signature-type tsd-kind-interface">UsingClient</a><span class="tsd-signature-symbol">,</span> data<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">APIUser</span><span class="tsd-signature-symbol">]</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <a href="../classes/User.html" class="tsd-signature-type tsd-kind-class">User</a><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-call-signature">VoiceChannel</span><span class="tsd-signature-symbol">(</span><br/>        <span class="tsd-signature-symbol">...</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">[</span>client<span class="tsd-signature-symbol">:</span> <a href="../interfaces/UsingClient.html" class="tsd-signature-type tsd-kind-interface">UsingClient</a><span class="tsd-signature-symbol">,</span> data<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">APIGuildChannel</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">ChannelType</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">]</span><span class="tsd-signature-symbol">,</span><br/>    <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <a href="../classes/VoiceChannel.html" class="tsd-signature-type tsd-kind-class">VoiceChannel</a><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-call-signature">VoiceState</span><span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">...</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">[</span>client<span class="tsd-signature-symbol">:</span> <a href="../interfaces/UsingClient.html" class="tsd-signature-type tsd-kind-interface">UsingClient</a><span class="tsd-signature-symbol">,</span> data<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">APIVoiceState</span><span class="tsd-signature-symbol">]</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <a href="../classes/VoiceState.html" class="tsd-signature-type tsd-kind-class">VoiceState</a><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-call-signature">Webhook</span><span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">...</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">[</span>client<span class="tsd-signature-symbol">:</span> <a href="../interfaces/UsingClient.html" class="tsd-signature-type tsd-kind-interface">UsingClient</a><span class="tsd-signature-symbol">,</span> data<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">APIWebhook</span><span class="tsd-signature-symbol">]</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <a href="../classes/Webhook.html" class="tsd-signature-type tsd-kind-class">Webhook</a><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-call-signature">WebhookMessage</span><span class="tsd-signature-symbol">(</span><br/>        <span class="tsd-signature-symbol">...</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">[</span><br/>            client<span class="tsd-signature-symbol">:</span> <a href="../interfaces/UsingClient.html" class="tsd-signature-type tsd-kind-interface">UsingClient</a><span class="tsd-signature-symbol">,</span><br/>            data<span class="tsd-signature-symbol">:</span> <a href="../types/MessageData.html" class="tsd-signature-type tsd-kind-type-alias">MessageData</a><span class="tsd-signature-symbol">,</span><br/>            webhookId<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">,</span><br/>            webhookToken<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">,</span><br/>        <span class="tsd-signature-symbol">]</span><span class="tsd-signature-symbol">,</span><br/>    <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <a href="../classes/WebhookMessage.html" class="tsd-signature-type tsd-kind-class">WebhookMessage</a><span class="tsd-signature-symbol">;</span><br/><span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol"> = ...</span></div><div class="tsd-type-declaration"><h4>Type declaration</h4><ul class="tsd-parameters"><li class="tsd-parameter"><h5 id="anonymousguild"><span class="tsd-kind-method">AnonymousGuild</span><span class="tsd-signature-symbol">:</span> function</h5><ul class="tsd-signatures"><li class=""><div class="tsd-signature tsd-anchor-link" id="anonymousguild-1"><span class="tsd-kind-call-signature">AnonymousGuild</span><span class="tsd-signature-symbol">(</span><br/>    <span class="tsd-signature-symbol">...</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">[</span>client<span class="tsd-signature-symbol">:</span> <a href="../interfaces/UsingClient.html" class="tsd-signature-type tsd-kind-interface">UsingClient</a><span class="tsd-signature-symbol">,</span> data<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">APIPartialGuild</span><span class="tsd-signature-symbol">]</span><span class="tsd-signature-symbol">,</span><br/><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <a href="../classes/AnonymousGuild.html" class="tsd-signature-type tsd-kind-class">AnonymousGuild</a><a href="#anonymousguild-1" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></div><div class="tsd-description"><div class="tsd-parameters"><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameter-list"><li><span><span class="tsd-signature-symbol">...</span><span class="tsd-kind-parameter">args</span>: <span class="tsd-signature-symbol">[</span>client<span class="tsd-signature-symbol">:</span> <a href="../interfaces/UsingClient.html" class="tsd-signature-type tsd-kind-interface">UsingClient</a><span class="tsd-signature-symbol">,</span> data<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">APIPartialGuild</span><span class="tsd-signature-symbol">]</span></span></li></ul></div><h4 class="tsd-returns-title">Returns <a href="../classes/AnonymousGuild.html" class="tsd-signature-type tsd-kind-class">AnonymousGuild</a></h4><aside class="tsd-sources"><ul><li>Defined in <a href="https://github.com/tiramisulabs/seyfert//blob/9ae4290ef220ffc96449d469d16d76b2031eca69/src/client/transformers.ts#L82">src/client/transformers.ts:82</a></li></ul></aside></div></li></ul></li><li class="tsd-parameter"><h5 id="application"><span class="tsd-kind-method">Application</span><span class="tsd-signature-symbol">:</span> function</h5><ul class="tsd-signatures"><li class=""><div class="tsd-signature tsd-anchor-link" id="application-1"><span class="tsd-kind-call-signature">Application</span><span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">...</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">[</span>client<span class="tsd-signature-symbol">:</span> <a href="../interfaces/UsingClient.html" class="tsd-signature-type tsd-kind-interface">UsingClient</a><span class="tsd-signature-symbol">,</span> data<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">APIApplication</span><span class="tsd-signature-symbol">]</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Application</span><a href="#application-1" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></div><div class="tsd-description"><div class="tsd-parameters"><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameter-list"><li><span><span class="tsd-signature-symbol">...</span><span class="tsd-kind-parameter">args</span>: <span class="tsd-signature-symbol">[</span>client<span class="tsd-signature-symbol">:</span> <a href="../interfaces/UsingClient.html" class="tsd-signature-type tsd-kind-interface">UsingClient</a><span class="tsd-signature-symbol">,</span> data<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">APIApplication</span><span class="tsd-signature-symbol">]</span></span></li></ul></div><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Application</span></h4><aside class="tsd-sources"><ul><li>Defined in <a href="https://github.com/tiramisulabs/seyfert//blob/9ae4290ef220ffc96449d469d16d76b2031eca69/src/client/transformers.ts#L76">src/client/transformers.ts:76</a></li></ul></aside></div></li></ul></li><li class="tsd-parameter"><h5 id="applicationemoji"><span class="tsd-kind-method">ApplicationEmoji</span><span class="tsd-signature-symbol">:</span> function</h5><ul class="tsd-signatures"><li class=""><div class="tsd-signature tsd-anchor-link" id="applicationemoji-1"><span class="tsd-kind-call-signature">ApplicationEmoji</span><span class="tsd-signature-symbol">(</span><br/>    <span class="tsd-signature-symbol">...</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">[</span>client<span class="tsd-signature-symbol">:</span> <a href="../interfaces/UsingClient.html" class="tsd-signature-type tsd-kind-interface">UsingClient</a><span class="tsd-signature-symbol">,</span> data<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">APIApplicationEmoji</span><span class="tsd-signature-symbol">]</span><span class="tsd-signature-symbol">,</span><br/><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <a href="../classes/ApplicationEmoji.html" class="tsd-signature-type tsd-kind-class">ApplicationEmoji</a><a href="#applicationemoji-1" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></div><div class="tsd-description"><div class="tsd-parameters"><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameter-list"><li><span><span class="tsd-signature-symbol">...</span><span class="tsd-kind-parameter">args</span>: <span class="tsd-signature-symbol">[</span>client<span class="tsd-signature-symbol">:</span> <a href="../interfaces/UsingClient.html" class="tsd-signature-type tsd-kind-interface">UsingClient</a><span class="tsd-signature-symbol">,</span> data<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">APIApplicationEmoji</span><span class="tsd-signature-symbol">]</span></span></li></ul></div><h4 class="tsd-returns-title">Returns <a href="../classes/ApplicationEmoji.html" class="tsd-signature-type tsd-kind-class">ApplicationEmoji</a></h4><aside class="tsd-sources"><ul><li>Defined in <a href="https://github.com/tiramisulabs/seyfert//blob/9ae4290ef220ffc96449d469d16d76b2031eca69/src/client/transformers.ts#L79">src/client/transformers.ts:79</a></li></ul></aside></div></li></ul></li><li class="tsd-parameter"><h5 id="automoderationrule"><span class="tsd-kind-method">AutoModerationRule</span><span class="tsd-signature-symbol">:</span> function</h5><ul class="tsd-signatures"><li class=""><div class="tsd-signature tsd-anchor-link" id="automoderationrule-1"><span class="tsd-kind-call-signature">AutoModerationRule</span><span class="tsd-signature-symbol">(</span><br/>    <span class="tsd-signature-symbol">...</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">[</span>client<span class="tsd-signature-symbol">:</span> <a href="../interfaces/UsingClient.html" class="tsd-signature-type tsd-kind-interface">UsingClient</a><span class="tsd-signature-symbol">,</span> data<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">APIAutoModerationRule</span><span class="tsd-signature-symbol">]</span><span class="tsd-signature-symbol">,</span><br/><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <a href="../classes/AutoModerationRule.html" class="tsd-signature-type tsd-kind-class">AutoModerationRule</a><a href="#automoderationrule-1" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></div><div class="tsd-description"><div class="tsd-parameters"><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameter-list"><li><span><span class="tsd-signature-symbol">...</span><span class="tsd-kind-parameter">args</span>: <span class="tsd-signature-symbol">[</span>client<span class="tsd-signature-symbol">:</span> <a href="../interfaces/UsingClient.html" class="tsd-signature-type tsd-kind-interface">UsingClient</a><span class="tsd-signature-symbol">,</span> data<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">APIAutoModerationRule</span><span class="tsd-signature-symbol">]</span></span></li></ul></div><h4 class="tsd-returns-title">Returns <a href="../classes/AutoModerationRule.html" class="tsd-signature-type tsd-kind-class">AutoModerationRule</a></h4><aside class="tsd-sources"><ul><li>Defined in <a href="https://github.com/tiramisulabs/seyfert//blob/9ae4290ef220ffc96449d469d16d76b2031eca69/src/client/transformers.ts#L85">src/client/transformers.ts:85</a></li></ul></aside></div></li></ul></li><li class="tsd-parameter"><h5 id="basechannel"><span class="tsd-kind-method">BaseChannel</span><span class="tsd-signature-symbol">:</span> function</h5><ul class="tsd-signatures"><li class=""><div class="tsd-signature tsd-anchor-link" id="basechannel-1"><span class="tsd-kind-call-signature">BaseChannel</span><span class="tsd-signature-symbol">(</span><br/>    <span class="tsd-signature-symbol">...</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">[</span>client<span class="tsd-signature-symbol">:</span> <a href="../interfaces/UsingClient.html" class="tsd-signature-type tsd-kind-interface">UsingClient</a><span class="tsd-signature-symbol">,</span> data<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">APIChannelBase</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">ChannelType</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">]</span><span class="tsd-signature-symbol">,</span><br/><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <a href="../classes/BaseChannel.html" class="tsd-signature-type tsd-kind-class">BaseChannel</a><a href="#basechannel-1" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></div><div class="tsd-description"><div class="tsd-parameters"><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameter-list"><li><span><span class="tsd-signature-symbol">...</span><span class="tsd-kind-parameter">args</span>: <span class="tsd-signature-symbol">[</span>client<span class="tsd-signature-symbol">:</span> <a href="../interfaces/UsingClient.html" class="tsd-signature-type tsd-kind-interface">UsingClient</a><span class="tsd-signature-symbol">,</span> data<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">APIChannelBase</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">ChannelType</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">]</span></span></li></ul></div><h4 class="tsd-returns-title">Returns <a href="../classes/BaseChannel.html" class="tsd-signature-type tsd-kind-class">BaseChannel</a></h4><aside class="tsd-sources"><ul><li>Defined in <a href="https://github.com/tiramisulabs/seyfert//blob/9ae4290ef220ffc96449d469d16d76b2031eca69/src/client/transformers.ts#L88">src/client/transformers.ts:88</a></li></ul></aside></div></li></ul></li><li class="tsd-parameter"><h5 id="baseguildchannel"><span class="tsd-kind-method">BaseGuildChannel</span><span class="tsd-signature-symbol">:</span> function</h5><ul class="tsd-signatures"><li class=""><div class="tsd-signature tsd-anchor-link" id="baseguildchannel-1"><span class="tsd-kind-call-signature">BaseGuildChannel</span><span class="tsd-signature-symbol">(</span><br/>    <span class="tsd-signature-symbol">...</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">[</span>client<span class="tsd-signature-symbol">:</span> <a href="../interfaces/UsingClient.html" class="tsd-signature-type tsd-kind-interface">UsingClient</a><span class="tsd-signature-symbol">,</span> data<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">APIGuildChannel</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">ChannelType</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">]</span><span class="tsd-signature-symbol">,</span><br/><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <a href="../classes/BaseGuildChannel.html" class="tsd-signature-type tsd-kind-class">BaseGuildChannel</a><a href="#baseguildchannel-1" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></div><div class="tsd-description"><div class="tsd-parameters"><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameter-list"><li><span><span class="tsd-signature-symbol">...</span><span class="tsd-kind-parameter">args</span>: <span class="tsd-signature-symbol">[</span>client<span class="tsd-signature-symbol">:</span> <a href="../interfaces/UsingClient.html" class="tsd-signature-type tsd-kind-interface">UsingClient</a><span class="tsd-signature-symbol">,</span> data<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">APIGuildChannel</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">ChannelType</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">]</span></span></li></ul></div><h4 class="tsd-returns-title">Returns <a href="../classes/BaseGuildChannel.html" class="tsd-signature-type tsd-kind-class">BaseGuildChannel</a></h4><aside class="tsd-sources"><ul><li>Defined in <a href="https://github.com/tiramisulabs/seyfert//blob/9ae4290ef220ffc96449d469d16d76b2031eca69/src/client/transformers.ts#L91">src/client/transformers.ts:91</a></li></ul></aside></div></li></ul></li><li class="tsd-parameter"><h5 id="categorychannel"><span class="tsd-kind-method">CategoryChannel</span><span class="tsd-signature-symbol">:</span> function</h5><ul class="tsd-signatures"><li class=""><div class="tsd-signature tsd-anchor-link" id="categorychannel-1"><span class="tsd-kind-call-signature">CategoryChannel</span><span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">...</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">any</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <a href="../classes/CategoryChannel.html" class="tsd-signature-type tsd-kind-class">CategoryChannel</a><a href="#categorychannel-1" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></div><div class="tsd-description"><div class="tsd-parameters"><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameter-list"><li><span><span class="tsd-signature-symbol">...</span><span class="tsd-kind-parameter">args</span>: <span class="tsd-signature-type">any</span><span class="tsd-signature-symbol">[]</span></span></li></ul></div><h4 class="tsd-returns-title">Returns <a href="../classes/CategoryChannel.html" class="tsd-signature-type tsd-kind-class">CategoryChannel</a></h4><aside class="tsd-sources"><ul><li>Defined in <a href="https://github.com/tiramisulabs/seyfert//blob/9ae4290ef220ffc96449d469d16d76b2031eca69/src/client/transformers.ts#L115">src/client/transformers.ts:115</a></li></ul></aside></div></li></ul></li><li class="tsd-parameter"><h5 id="clientuser"><span class="tsd-kind-method">ClientUser</span><span class="tsd-signature-symbol">:</span> function</h5><ul class="tsd-signatures"><li class=""><div class="tsd-signature tsd-anchor-link" id="clientuser-1"><span class="tsd-kind-call-signature">ClientUser</span><span class="tsd-signature-symbol">(</span><br/>    <span class="tsd-signature-symbol">...</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">[</span><br/>        client<span class="tsd-signature-symbol">:</span> <a href="../interfaces/UsingClient.html" class="tsd-signature-type tsd-kind-interface">UsingClient</a><span class="tsd-signature-symbol">,</span><br/>        data<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">APIUser</span><span class="tsd-signature-symbol">,</span><br/>        application<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Pick</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIApplication</span><span class="tsd-signature-symbol">,</span> <span class="tsd-signature-type">&quot;id&quot;</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;flags&quot;</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>    <span class="tsd-signature-symbol">]</span><span class="tsd-signature-symbol">,</span><br/><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <a href="../classes/ClientUser.html" class="tsd-signature-type tsd-kind-class">ClientUser</a><a href="#clientuser-1" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></div><div class="tsd-description"><div class="tsd-parameters"><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameter-list"><li><span><span class="tsd-signature-symbol">...</span><span class="tsd-kind-parameter">args</span>: <span class="tsd-signature-symbol">[</span><br/>    client<span class="tsd-signature-symbol">:</span> <a href="../interfaces/UsingClient.html" class="tsd-signature-type tsd-kind-interface">UsingClient</a><span class="tsd-signature-symbol">,</span><br/>    data<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">APIUser</span><span class="tsd-signature-symbol">,</span><br/>    application<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Pick</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIApplication</span><span class="tsd-signature-symbol">,</span> <span class="tsd-signature-type">&quot;id&quot;</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;flags&quot;</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/><span class="tsd-signature-symbol">]</span></span></li></ul></div><h4 class="tsd-returns-title">Returns <a href="../classes/ClientUser.html" class="tsd-signature-type tsd-kind-class">ClientUser</a></h4><aside class="tsd-sources"><ul><li>Defined in <a href="https://github.com/tiramisulabs/seyfert//blob/9ae4290ef220ffc96449d469d16d76b2031eca69/src/client/transformers.ts#L124">src/client/transformers.ts:124</a></li></ul></aside></div></li></ul></li><li class="tsd-parameter"><h5 id="directorychannel"><span class="tsd-kind-method">DirectoryChannel</span><span class="tsd-signature-symbol">:</span> function</h5><ul class="tsd-signatures"><li class=""><div class="tsd-signature tsd-anchor-link" id="directorychannel-1"><span class="tsd-kind-call-signature">DirectoryChannel</span><span class="tsd-signature-symbol">(</span><br/>    <span class="tsd-signature-symbol">...</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">[</span>client<span class="tsd-signature-symbol">:</span> <a href="../interfaces/UsingClient.html" class="tsd-signature-type tsd-kind-interface">UsingClient</a><span class="tsd-signature-symbol">,</span> data<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">APIChannelBase</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">ChannelType</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">]</span><span class="tsd-signature-symbol">,</span><br/><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <a href="../classes/DirectoryChannel.html" class="tsd-signature-type tsd-kind-class">DirectoryChannel</a><a href="#directorychannel-1" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></div><div class="tsd-description"><div class="tsd-parameters"><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameter-list"><li><span><span class="tsd-signature-symbol">...</span><span class="tsd-kind-parameter">args</span>: <span class="tsd-signature-symbol">[</span>client<span class="tsd-signature-symbol">:</span> <a href="../interfaces/UsingClient.html" class="tsd-signature-type tsd-kind-interface">UsingClient</a><span class="tsd-signature-symbol">,</span> data<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">APIChannelBase</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">ChannelType</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">]</span></span></li></ul></div><h4 class="tsd-returns-title">Returns <a href="../classes/DirectoryChannel.html" class="tsd-signature-type tsd-kind-class">DirectoryChannel</a></h4><aside class="tsd-sources"><ul><li>Defined in <a href="https://github.com/tiramisulabs/seyfert//blob/9ae4290ef220ffc96449d469d16d76b2031eca69/src/client/transformers.ts#L121">src/client/transformers.ts:121</a></li></ul></aside></div></li></ul></li><li class="tsd-parameter"><h5 id="dmchannel"><span class="tsd-kind-method">DMChannel</span><span class="tsd-signature-symbol">:</span> function</h5><ul class="tsd-signatures"><li class=""><div class="tsd-signature tsd-anchor-link" id="dmchannel-1"><span class="tsd-kind-call-signature">DMChannel</span><span class="tsd-signature-symbol">(</span><br/>    <span class="tsd-signature-symbol">...</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">[</span>client<span class="tsd-signature-symbol">:</span> <a href="../interfaces/UsingClient.html" class="tsd-signature-type tsd-kind-interface">UsingClient</a><span class="tsd-signature-symbol">,</span> data<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">APIChannelBase</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">ChannelType</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">]</span><span class="tsd-signature-symbol">,</span><br/><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <a href="../classes/DMChannel.html" class="tsd-signature-type tsd-kind-class">DMChannel</a><a href="#dmchannel-1" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></div><div class="tsd-description"><div class="tsd-parameters"><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameter-list"><li><span><span class="tsd-signature-symbol">...</span><span class="tsd-kind-parameter">args</span>: <span class="tsd-signature-symbol">[</span>client<span class="tsd-signature-symbol">:</span> <a href="../interfaces/UsingClient.html" class="tsd-signature-type tsd-kind-interface">UsingClient</a><span class="tsd-signature-symbol">,</span> data<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">APIChannelBase</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">ChannelType</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">]</span></span></li></ul></div><h4 class="tsd-returns-title">Returns <a href="../classes/DMChannel.html" class="tsd-signature-type tsd-kind-class">DMChannel</a></h4><aside class="tsd-sources"><ul><li>Defined in <a href="https://github.com/tiramisulabs/seyfert//blob/9ae4290ef220ffc96449d469d16d76b2031eca69/src/client/transformers.ts#L97">src/client/transformers.ts:97</a></li></ul></aside></div></li></ul></li><li class="tsd-parameter"><h5 id="emoji"><span class="tsd-kind-method">Emoji</span><span class="tsd-signature-symbol">:</span> function</h5><ul class="tsd-signatures"><li class=""><div class="tsd-signature tsd-anchor-link" id="emoji-1"><span class="tsd-kind-call-signature">Emoji</span><span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">...</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">[</span>client<span class="tsd-signature-symbol">:</span> <a href="../interfaces/UsingClient.html" class="tsd-signature-type tsd-kind-interface">UsingClient</a><span class="tsd-signature-symbol">,</span> data<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">APIEmoji</span><span class="tsd-signature-symbol">]</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <a href="../classes/Emoji.html" class="tsd-signature-type tsd-kind-class">Emoji</a><a href="#emoji-1" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></div><div class="tsd-description"><div class="tsd-parameters"><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameter-list"><li><span><span class="tsd-signature-symbol">...</span><span class="tsd-kind-parameter">args</span>: <span class="tsd-signature-symbol">[</span>client<span class="tsd-signature-symbol">:</span> <a href="../interfaces/UsingClient.html" class="tsd-signature-type tsd-kind-interface">UsingClient</a><span class="tsd-signature-symbol">,</span> data<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">APIEmoji</span><span class="tsd-signature-symbol">]</span></span></li></ul></div><h4 class="tsd-returns-title">Returns <a href="../classes/Emoji.html" class="tsd-signature-type tsd-kind-class">Emoji</a></h4><aside class="tsd-sources"><ul><li>Defined in <a href="https://github.com/tiramisulabs/seyfert//blob/9ae4290ef220ffc96449d469d16d76b2031eca69/src/client/transformers.ts#L133">src/client/transformers.ts:133</a></li></ul></aside></div></li></ul></li><li class="tsd-parameter"><h5 id="entitlement"><span class="tsd-kind-method">Entitlement</span><span class="tsd-signature-symbol">:</span> function</h5><ul class="tsd-signatures"><li class=""><div class="tsd-signature tsd-anchor-link" id="entitlement-1"><span class="tsd-kind-call-signature">Entitlement</span><span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">...</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">[</span>client<span class="tsd-signature-symbol">:</span> <a href="../interfaces/UsingClient.html" class="tsd-signature-type tsd-kind-interface">UsingClient</a><span class="tsd-signature-symbol">,</span> data<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">APIEntitlement</span><span class="tsd-signature-symbol">]</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <a href="../classes/Entitlement.html" class="tsd-signature-type tsd-kind-class">Entitlement</a><a href="#entitlement-1" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></div><div class="tsd-description"><div class="tsd-parameters"><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameter-list"><li><span><span class="tsd-signature-symbol">...</span><span class="tsd-kind-parameter">args</span>: <span class="tsd-signature-symbol">[</span>client<span class="tsd-signature-symbol">:</span> <a href="../interfaces/UsingClient.html" class="tsd-signature-type tsd-kind-interface">UsingClient</a><span class="tsd-signature-symbol">,</span> data<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">APIEntitlement</span><span class="tsd-signature-symbol">]</span></span></li></ul></div><h4 class="tsd-returns-title">Returns <a href="../classes/Entitlement.html" class="tsd-signature-type tsd-kind-class">Entitlement</a></h4><aside class="tsd-sources"><ul><li>Defined in <a href="https://github.com/tiramisulabs/seyfert//blob/9ae4290ef220ffc96449d469d16d76b2031eca69/src/client/transformers.ts#L177">src/client/transformers.ts:177</a></li></ul></aside></div></li></ul></li><li class="tsd-parameter"><h5 id="forumchannel"><span class="tsd-kind-method">ForumChannel</span><span class="tsd-signature-symbol">:</span> function</h5><ul class="tsd-signatures"><li class=""><div class="tsd-signature tsd-anchor-link" id="forumchannel-1"><span class="tsd-kind-call-signature">ForumChannel</span><span class="tsd-signature-symbol">(</span><br/>    <span class="tsd-signature-symbol">...</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">[</span>client<span class="tsd-signature-symbol">:</span> <a href="../interfaces/UsingClient.html" class="tsd-signature-type tsd-kind-interface">UsingClient</a><span class="tsd-signature-symbol">,</span> data<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">APIGuildChannel</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">ChannelType</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">]</span><span class="tsd-signature-symbol">,</span><br/><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <a href="../classes/ForumChannel.html" class="tsd-signature-type tsd-kind-class">ForumChannel</a><a href="#forumchannel-1" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></div><div class="tsd-description"><div class="tsd-parameters"><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameter-list"><li><span><span class="tsd-signature-symbol">...</span><span class="tsd-kind-parameter">args</span>: <span class="tsd-signature-symbol">[</span>client<span class="tsd-signature-symbol">:</span> <a href="../interfaces/UsingClient.html" class="tsd-signature-type tsd-kind-interface">UsingClient</a><span class="tsd-signature-symbol">,</span> data<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">APIGuildChannel</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">ChannelType</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">]</span></span></li></ul></div><h4 class="tsd-returns-title">Returns <a href="../classes/ForumChannel.html" class="tsd-signature-type tsd-kind-class">ForumChannel</a></h4><aside class="tsd-sources"><ul><li>Defined in <a href="https://github.com/tiramisulabs/seyfert//blob/9ae4290ef220ffc96449d469d16d76b2031eca69/src/client/transformers.ts#L109">src/client/transformers.ts:109</a></li></ul></aside></div></li></ul></li><li class="tsd-parameter"><h5 id="guild"><span class="tsd-kind-method">Guild</span><span class="tsd-signature-symbol">:</span> function</h5><ul class="tsd-signatures"><li class=""><div class="tsd-signature tsd-anchor-link" id="guild-1"><span class="tsd-kind-call-signature">Guild</span><span class="tsd-signature-symbol">&lt;</span><a class="tsd-signature-type tsd-kind-type-parameter" href="#guildstate">State</a> <span class="tsd-signature-keyword">extends</span> <span class="tsd-signature-keyword">keyof</span> <span class="tsd-signature-type">StructWhen</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">any</span><span class="tsd-signature-symbol">,</span> <span class="tsd-signature-type">&quot;cached&quot;</span><span class="tsd-signature-symbol">&gt;</span> <span class="tsd-signature-symbol">=</span> <span class="tsd-signature-type">&quot;api&quot;</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">(</span><br/>    <span class="tsd-signature-symbol">...</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">[</span><br/>        client<span class="tsd-signature-symbol">:</span> <a href="../interfaces/UsingClient.html" class="tsd-signature-type tsd-kind-interface">UsingClient</a><span class="tsd-signature-symbol">,</span><br/>        data<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">APIGuild</span><br/>        <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">GatewayGuildCreateDispatchData</span><span class="tsd-signature-symbol">,</span><br/>    <span class="tsd-signature-symbol">]</span><span class="tsd-signature-symbol">,</span><br/><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <a href="../classes/Guild.html" class="tsd-signature-type tsd-kind-class">Guild</a><span class="tsd-signature-symbol">&lt;</span><a class="tsd-signature-type tsd-kind-type-parameter" href="#guildstate">State</a><span class="tsd-signature-symbol">&gt;</span><a href="#guild-1" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></div><div class="tsd-description"><section class="tsd-panel"><h4>Type Parameters</h4><ul class="tsd-type-parameter-list"><li><span id="guildstate"><span class="tsd-kind-type-parameter">State</span> <span class="tsd-signature-keyword">extends</span> <span class="tsd-signature-keyword">keyof</span> <span class="tsd-signature-type">StructWhen</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">any</span><span class="tsd-signature-symbol">,</span> <span class="tsd-signature-type">&quot;cached&quot;</span><span class="tsd-signature-symbol">&gt;</span> = <span class="tsd-signature-type">&quot;api&quot;</span></span></li></ul></section><div class="tsd-parameters"><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameter-list"><li><span><span class="tsd-signature-symbol">...</span><span class="tsd-kind-parameter">args</span>: <span class="tsd-signature-symbol">[</span>client<span class="tsd-signature-symbol">:</span> <a href="../interfaces/UsingClient.html" class="tsd-signature-type tsd-kind-interface">UsingClient</a><span class="tsd-signature-symbol">,</span> data<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">APIGuild</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">GatewayGuildCreateDispatchData</span><span class="tsd-signature-symbol">]</span></span></li></ul></div><h4 class="tsd-returns-title">Returns <a href="../classes/Guild.html" class="tsd-signature-type tsd-kind-class">Guild</a><span class="tsd-signature-symbol">&lt;</span><a class="tsd-signature-type tsd-kind-type-parameter" href="#guildstate">State</a><span class="tsd-signature-symbol">&gt;</span></h4><aside class="tsd-sources"><ul><li>Defined in <a href="https://github.com/tiramisulabs/seyfert//blob/9ae4290ef220ffc96449d469d16d76b2031eca69/src/client/transformers.ts#L127">src/client/transformers.ts:127</a></li></ul></aside></div></li></ul></li><li class="tsd-parameter"><h5 id="guildban"><span class="tsd-kind-method">GuildBan</span><span class="tsd-signature-symbol">:</span> function</h5><ul class="tsd-signatures"><li class=""><div class="tsd-signature tsd-anchor-link" id="guildban-1"><span class="tsd-kind-call-signature">GuildBan</span><span class="tsd-signature-symbol">(</span><br/>    <span class="tsd-signature-symbol">...</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">[</span><br/>        client<span class="tsd-signature-symbol">:</span> <a href="../interfaces/UsingClient.html" class="tsd-signature-type tsd-kind-interface">UsingClient</a><span class="tsd-signature-symbol">,</span><br/>        data<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">APIBan</span><br/>        <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">ActuallyBan</span><span class="tsd-signature-symbol">,</span><br/>        guildId<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">,</span><br/>    <span class="tsd-signature-symbol">]</span><span class="tsd-signature-symbol">,</span><br/><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <a href="../classes/GuildBan.html" class="tsd-signature-type tsd-kind-class">GuildBan</a><a href="#guildban-1" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></div><div class="tsd-description"><div class="tsd-parameters"><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameter-list"><li><span><span class="tsd-signature-symbol">...</span><span class="tsd-kind-parameter">args</span>: <span class="tsd-signature-symbol">[</span>client<span class="tsd-signature-symbol">:</span> <a href="../interfaces/UsingClient.html" class="tsd-signature-type tsd-kind-interface">UsingClient</a><span class="tsd-signature-symbol">,</span> data<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">APIBan</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">ActuallyBan</span><span class="tsd-signature-symbol">,</span> guildId<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">]</span></span></li></ul></div><h4 class="tsd-returns-title">Returns <a href="../classes/GuildBan.html" class="tsd-signature-type tsd-kind-class">GuildBan</a></h4><aside class="tsd-sources"><ul><li>Defined in <a href="https://github.com/tiramisulabs/seyfert//blob/9ae4290ef220ffc96449d469d16d76b2031eca69/src/client/transformers.ts#L130">src/client/transformers.ts:130</a></li></ul></aside></div></li></ul></li><li class="tsd-parameter"><h5 id="guildemoji"><span class="tsd-kind-method">GuildEmoji</span><span class="tsd-signature-symbol">:</span> function</h5><ul class="tsd-signatures"><li class=""><div class="tsd-signature tsd-anchor-link" id="guildemoji-1"><span class="tsd-kind-call-signature">GuildEmoji</span><span class="tsd-signature-symbol">(</span><br/>    <span class="tsd-signature-symbol">...</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">[</span>client<span class="tsd-signature-symbol">:</span> <a href="../interfaces/UsingClient.html" class="tsd-signature-type tsd-kind-interface">UsingClient</a><span class="tsd-signature-symbol">,</span> data<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">APIEmoji</span><span class="tsd-signature-symbol">,</span> guildId<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">]</span><span class="tsd-signature-symbol">,</span><br/><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <a href="../classes/GuildEmoji.html" class="tsd-signature-type tsd-kind-class">GuildEmoji</a><a href="#guildemoji-1" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></div><div class="tsd-description"><div class="tsd-parameters"><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameter-list"><li><span><span class="tsd-signature-symbol">...</span><span class="tsd-kind-parameter">args</span>: <span class="tsd-signature-symbol">[</span>client<span class="tsd-signature-symbol">:</span> <a href="../interfaces/UsingClient.html" class="tsd-signature-type tsd-kind-interface">UsingClient</a><span class="tsd-signature-symbol">,</span> data<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">APIEmoji</span><span class="tsd-signature-symbol">,</span> guildId<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">]</span></span></li></ul></div><h4 class="tsd-returns-title">Returns <a href="../classes/GuildEmoji.html" class="tsd-signature-type tsd-kind-class">GuildEmoji</a></h4><aside class="tsd-sources"><ul><li>Defined in <a href="https://github.com/tiramisulabs/seyfert//blob/9ae4290ef220ffc96449d469d16d76b2031eca69/src/client/transformers.ts#L136">src/client/transformers.ts:136</a></li></ul></aside></div></li></ul></li><li class="tsd-parameter"><h5 id="guildmember"><span class="tsd-kind-method">GuildMember</span><span class="tsd-signature-symbol">:</span> function</h5><ul class="tsd-signatures"><li class=""><div class="tsd-signature tsd-anchor-link" id="guildmember-1"><span class="tsd-kind-call-signature">GuildMember</span><span class="tsd-signature-symbol">(</span><br/>    <span class="tsd-signature-symbol">...</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">[</span><br/>        client<span class="tsd-signature-symbol">:</span> <a href="../interfaces/UsingClient.html" class="tsd-signature-type tsd-kind-interface">UsingClient</a><span class="tsd-signature-symbol">,</span><br/>        data<span class="tsd-signature-symbol">:</span> <a href="../types/GuildMemberData.html" class="tsd-signature-type tsd-kind-type-alias">GuildMemberData</a><span class="tsd-signature-symbol">,</span><br/>        user<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">APIUser</span><span class="tsd-signature-symbol">,</span><br/>        guildId<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">,</span><br/>    <span class="tsd-signature-symbol">]</span><span class="tsd-signature-symbol">,</span><br/><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <a href="../classes/GuildMember.html" class="tsd-signature-type tsd-kind-class">GuildMember</a><a href="#guildmember-1" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></div><div class="tsd-description"><div class="tsd-parameters"><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameter-list"><li><span><span class="tsd-signature-symbol">...</span><span class="tsd-kind-parameter">args</span>: <span class="tsd-signature-symbol">[</span>client<span class="tsd-signature-symbol">:</span> <a href="../interfaces/UsingClient.html" class="tsd-signature-type tsd-kind-interface">UsingClient</a><span class="tsd-signature-symbol">,</span> data<span class="tsd-signature-symbol">:</span> <a href="../types/GuildMemberData.html" class="tsd-signature-type tsd-kind-type-alias">GuildMemberData</a><span class="tsd-signature-symbol">,</span> user<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">APIUser</span><span class="tsd-signature-symbol">,</span> guildId<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">]</span></span></li></ul></div><h4 class="tsd-returns-title">Returns <a href="../classes/GuildMember.html" class="tsd-signature-type tsd-kind-class">GuildMember</a></h4><aside class="tsd-sources"><ul><li>Defined in <a href="https://github.com/tiramisulabs/seyfert//blob/9ae4290ef220ffc96449d469d16d76b2031eca69/src/client/transformers.ts#L139">src/client/transformers.ts:139</a></li></ul></aside></div></li></ul></li><li class="tsd-parameter"><h5 id="guildrole"><span class="tsd-kind-method">GuildRole</span><span class="tsd-signature-symbol">:</span> function</h5><ul class="tsd-signatures"><li class=""><div class="tsd-signature tsd-anchor-link" id="guildrole-1"><span class="tsd-kind-call-signature">GuildRole</span><span class="tsd-signature-symbol">(</span><br/>    <span class="tsd-signature-symbol">...</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">[</span>client<span class="tsd-signature-symbol">:</span> <a href="../interfaces/UsingClient.html" class="tsd-signature-type tsd-kind-interface">UsingClient</a><span class="tsd-signature-symbol">,</span> data<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">APIRole</span><span class="tsd-signature-symbol">,</span> guildId<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">]</span><span class="tsd-signature-symbol">,</span><br/><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <a href="../classes/GuildRole.html" class="tsd-signature-type tsd-kind-class">GuildRole</a><a href="#guildrole-1" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></div><div class="tsd-description"><div class="tsd-parameters"><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameter-list"><li><span><span class="tsd-signature-symbol">...</span><span class="tsd-kind-parameter">args</span>: <span class="tsd-signature-symbol">[</span>client<span class="tsd-signature-symbol">:</span> <a href="../interfaces/UsingClient.html" class="tsd-signature-type tsd-kind-interface">UsingClient</a><span class="tsd-signature-symbol">,</span> data<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">APIRole</span><span class="tsd-signature-symbol">,</span> guildId<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">]</span></span></li></ul></div><h4 class="tsd-returns-title">Returns <a href="../classes/GuildRole.html" class="tsd-signature-type tsd-kind-class">GuildRole</a></h4><aside class="tsd-sources"><ul><li>Defined in <a href="https://github.com/tiramisulabs/seyfert//blob/9ae4290ef220ffc96449d469d16d76b2031eca69/src/client/transformers.ts#L147">src/client/transformers.ts:147</a></li></ul></aside></div></li></ul></li><li class="tsd-parameter"><h5 id="guildtemplate"><span class="tsd-kind-method">GuildTemplate</span><span class="tsd-signature-symbol">:</span> function</h5><ul class="tsd-signatures"><li class=""><div class="tsd-signature tsd-anchor-link" id="guildtemplate-1"><span class="tsd-kind-call-signature">GuildTemplate</span><span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">...</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">[</span>client<span class="tsd-signature-symbol">:</span> <a href="../interfaces/UsingClient.html" class="tsd-signature-type tsd-kind-interface">UsingClient</a><span class="tsd-signature-symbol">,</span> data<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">APITemplate</span><span class="tsd-signature-symbol">]</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <a href="../classes/GuildTemplate.html" class="tsd-signature-type tsd-kind-class">GuildTemplate</a><a href="#guildtemplate-1" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></div><div class="tsd-description"><div class="tsd-parameters"><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameter-list"><li><span><span class="tsd-signature-symbol">...</span><span class="tsd-kind-parameter">args</span>: <span class="tsd-signature-symbol">[</span>client<span class="tsd-signature-symbol">:</span> <a href="../interfaces/UsingClient.html" class="tsd-signature-type tsd-kind-interface">UsingClient</a><span class="tsd-signature-symbol">,</span> data<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">APITemplate</span><span class="tsd-signature-symbol">]</span></span></li></ul></div><h4 class="tsd-returns-title">Returns <a href="../classes/GuildTemplate.html" class="tsd-signature-type tsd-kind-class">GuildTemplate</a></h4><aside class="tsd-sources"><ul><li>Defined in <a href="https://github.com/tiramisulabs/seyfert//blob/9ae4290ef220ffc96449d469d16d76b2031eca69/src/client/transformers.ts#L150">src/client/transformers.ts:150</a></li></ul></aside></div></li></ul></li><li class="tsd-parameter"><h5 id="interactionguildmember"><span class="tsd-kind-method">InteractionGuildMember</span><span class="tsd-signature-symbol">:</span> function</h5><ul class="tsd-signatures"><li class=""><div class="tsd-signature tsd-anchor-link" id="interactionguildmember-1"><span class="tsd-kind-call-signature">InteractionGuildMember</span><span class="tsd-signature-symbol">(</span><br/>    <span class="tsd-signature-symbol">...</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">[</span><br/>        client<span class="tsd-signature-symbol">:</span> <a href="../interfaces/UsingClient.html" class="tsd-signature-type tsd-kind-interface">UsingClient</a><span class="tsd-signature-symbol">,</span><br/>        data<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">APIInteractionDataResolvedGuildMember</span><span class="tsd-signature-symbol">,</span><br/>        user<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">APIUser</span><span class="tsd-signature-symbol">,</span><br/>        guildId<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">,</span><br/>    <span class="tsd-signature-symbol">]</span><span class="tsd-signature-symbol">,</span><br/><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <a href="../classes/InteractionGuildMember.html" class="tsd-signature-type tsd-kind-class">InteractionGuildMember</a><a href="#interactionguildmember-1" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></div><div class="tsd-description"><div class="tsd-parameters"><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameter-list"><li><span><span class="tsd-signature-symbol">...</span><span class="tsd-kind-parameter">args</span>: <span class="tsd-signature-symbol">[</span><br/>    client<span class="tsd-signature-symbol">:</span> <a href="../interfaces/UsingClient.html" class="tsd-signature-type tsd-kind-interface">UsingClient</a><span class="tsd-signature-symbol">,</span><br/>    data<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">APIInteractionDataResolvedGuildMember</span><span class="tsd-signature-symbol">,</span><br/>    user<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">APIUser</span><span class="tsd-signature-symbol">,</span><br/>    guildId<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">,</span><br/><span class="tsd-signature-symbol">]</span></span></li></ul></div><h4 class="tsd-returns-title">Returns <a href="../classes/InteractionGuildMember.html" class="tsd-signature-type tsd-kind-class">InteractionGuildMember</a></h4><aside class="tsd-sources"><ul><li>Defined in <a href="https://github.com/tiramisulabs/seyfert//blob/9ae4290ef220ffc96449d469d16d76b2031eca69/src/client/transformers.ts#L142">src/client/transformers.ts:142</a></li></ul></aside></div></li></ul></li><li class="tsd-parameter"><h5 id="mediachannel"><span class="tsd-kind-method">MediaChannel</span><span class="tsd-signature-symbol">:</span> function</h5><ul class="tsd-signatures"><li class=""><div class="tsd-signature tsd-anchor-link" id="mediachannel-1"><span class="tsd-kind-call-signature">MediaChannel</span><span class="tsd-signature-symbol">(</span><br/>    <span class="tsd-signature-symbol">...</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">[</span>client<span class="tsd-signature-symbol">:</span> <a href="../interfaces/UsingClient.html" class="tsd-signature-type tsd-kind-interface">UsingClient</a><span class="tsd-signature-symbol">,</span> data<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">APIGuildChannel</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">ChannelType</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">]</span><span class="tsd-signature-symbol">,</span><br/><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <a href="../classes/MediaChannel.html" class="tsd-signature-type tsd-kind-class">MediaChannel</a><a href="#mediachannel-1" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></div><div class="tsd-description"><div class="tsd-parameters"><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameter-list"><li><span><span class="tsd-signature-symbol">...</span><span class="tsd-kind-parameter">args</span>: <span class="tsd-signature-symbol">[</span>client<span class="tsd-signature-symbol">:</span> <a href="../interfaces/UsingClient.html" class="tsd-signature-type tsd-kind-interface">UsingClient</a><span class="tsd-signature-symbol">,</span> data<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">APIGuildChannel</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">ChannelType</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">]</span></span></li></ul></div><h4 class="tsd-returns-title">Returns <a href="../classes/MediaChannel.html" class="tsd-signature-type tsd-kind-class">MediaChannel</a></h4><aside class="tsd-sources"><ul><li>Defined in <a href="https://github.com/tiramisulabs/seyfert//blob/9ae4290ef220ffc96449d469d16d76b2031eca69/src/client/transformers.ts#L106">src/client/transformers.ts:106</a></li></ul></aside></div></li></ul></li><li class="tsd-parameter"><h5 id="message"><span class="tsd-kind-method">Message</span><span class="tsd-signature-symbol">:</span> function</h5><ul class="tsd-signatures"><li class=""><div class="tsd-signature tsd-anchor-link" id="message-1"><span class="tsd-kind-call-signature">Message</span><span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">...</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">[</span>client<span class="tsd-signature-symbol">:</span> <a href="../interfaces/UsingClient.html" class="tsd-signature-type tsd-kind-interface">UsingClient</a><span class="tsd-signature-symbol">,</span> data<span class="tsd-signature-symbol">:</span> <a href="../types/MessageData.html" class="tsd-signature-type tsd-kind-type-alias">MessageData</a><span class="tsd-signature-symbol">]</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <a href="../classes/Message.html" class="tsd-signature-type tsd-kind-class">Message</a><a href="#message-1" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></div><div class="tsd-description"><div class="tsd-parameters"><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameter-list"><li><span><span class="tsd-signature-symbol">...</span><span class="tsd-kind-parameter">args</span>: <span class="tsd-signature-symbol">[</span>client<span class="tsd-signature-symbol">:</span> <a href="../interfaces/UsingClient.html" class="tsd-signature-type tsd-kind-interface">UsingClient</a><span class="tsd-signature-symbol">,</span> data<span class="tsd-signature-symbol">:</span> <a href="../types/MessageData.html" class="tsd-signature-type tsd-kind-type-alias">MessageData</a><span class="tsd-signature-symbol">]</span></span></li></ul></div><h4 class="tsd-returns-title">Returns <a href="../classes/Message.html" class="tsd-signature-type tsd-kind-class">Message</a></h4><aside class="tsd-sources"><ul><li>Defined in <a href="https://github.com/tiramisulabs/seyfert//blob/9ae4290ef220ffc96449d469d16d76b2031eca69/src/client/transformers.ts#L153">src/client/transformers.ts:153</a></li></ul></aside></div></li></ul></li><li class="tsd-parameter"><h5 id="newschannel"><span class="tsd-kind-method">NewsChannel</span><span class="tsd-signature-symbol">:</span> function</h5><ul class="tsd-signatures"><li class=""><div class="tsd-signature tsd-anchor-link" id="newschannel-1"><span class="tsd-kind-call-signature">NewsChannel</span><span class="tsd-signature-symbol">(</span><br/>    <span class="tsd-signature-symbol">...</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">[</span>client<span class="tsd-signature-symbol">:</span> <a href="../interfaces/UsingClient.html" class="tsd-signature-type tsd-kind-interface">UsingClient</a><span class="tsd-signature-symbol">,</span> data<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">APIGuildChannel</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">ChannelType</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">]</span><span class="tsd-signature-symbol">,</span><br/><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <a href="../classes/NewsChannel.html" class="tsd-signature-type tsd-kind-class">NewsChannel</a><a href="#newschannel-1" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></div><div class="tsd-description"><div class="tsd-parameters"><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameter-list"><li><span><span class="tsd-signature-symbol">...</span><span class="tsd-kind-parameter">args</span>: <span class="tsd-signature-symbol">[</span>client<span class="tsd-signature-symbol">:</span> <a href="../interfaces/UsingClient.html" class="tsd-signature-type tsd-kind-interface">UsingClient</a><span class="tsd-signature-symbol">,</span> data<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">APIGuildChannel</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">ChannelType</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">]</span></span></li></ul></div><h4 class="tsd-returns-title">Returns <a href="../classes/NewsChannel.html" class="tsd-signature-type tsd-kind-class">NewsChannel</a></h4><aside class="tsd-sources"><ul><li>Defined in <a href="https://github.com/tiramisulabs/seyfert//blob/9ae4290ef220ffc96449d469d16d76b2031eca69/src/client/transformers.ts#L118">src/client/transformers.ts:118</a></li></ul></aside></div></li></ul></li><li class="tsd-parameter"><h5 id="optionresolver"><span class="tsd-kind-method">OptionResolver</span><span class="tsd-signature-symbol">:</span> function</h5><ul class="tsd-signatures"><li class=""><div class="tsd-signature tsd-anchor-link" id="optionresolver-1"><span class="tsd-kind-call-signature">OptionResolver</span><span class="tsd-signature-symbol">(</span><br/>    <span class="tsd-signature-symbol">...</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">[</span><br/>        client<span class="tsd-signature-symbol">:</span> <a href="../interfaces/UsingClient.html" class="tsd-signature-type tsd-kind-interface">UsingClient</a><span class="tsd-signature-symbol">,</span><br/>        options<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">APIApplicationCommandInteractionDataOption</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">,</span><br/>        parent<span class="tsd-signature-symbol">?:</span> <a href="../classes/Command.html" class="tsd-signature-type tsd-kind-class">Command</a><span class="tsd-signature-symbol">,</span><br/>        guildId<span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">,</span><br/>        resolved<span class="tsd-signature-symbol">?:</span> <a href="../types/ContextOptionsResolved.html" class="tsd-signature-type tsd-kind-type-alias">ContextOptionsResolved</a><span class="tsd-signature-symbol">,</span><br/>    <span class="tsd-signature-symbol">]</span><span class="tsd-signature-symbol">,</span><br/><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <a href="../classes/OptionResolver.html" class="tsd-signature-type tsd-kind-class">OptionResolver</a><a href="#optionresolver-1" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></div><div class="tsd-description"><div class="tsd-parameters"><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameter-list"><li><span><span class="tsd-signature-symbol">...</span><span class="tsd-kind-parameter">args</span>: <span class="tsd-signature-symbol">[</span><br/>    client<span class="tsd-signature-symbol">:</span> <a href="../interfaces/UsingClient.html" class="tsd-signature-type tsd-kind-interface">UsingClient</a><span class="tsd-signature-symbol">,</span><br/>    options<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">APIApplicationCommandInteractionDataOption</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">,</span><br/>    parent<span class="tsd-signature-symbol">?:</span> <a href="../classes/Command.html" class="tsd-signature-type tsd-kind-class">Command</a><span class="tsd-signature-symbol">,</span><br/>    guildId<span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">,</span><br/>    resolved<span class="tsd-signature-symbol">?:</span> <a href="../types/ContextOptionsResolved.html" class="tsd-signature-type tsd-kind-type-alias">ContextOptionsResolved</a><span class="tsd-signature-symbol">,</span><br/><span class="tsd-signature-symbol">]</span></span></li></ul></div><h4 class="tsd-returns-title">Returns <a href="../classes/OptionResolver.html" class="tsd-signature-type tsd-kind-class">OptionResolver</a></h4><aside class="tsd-sources"><ul><li>Defined in <a href="https://github.com/tiramisulabs/seyfert//blob/9ae4290ef220ffc96449d469d16d76b2031eca69/src/client/transformers.ts#L174">src/client/transformers.ts:174</a></li></ul></aside></div></li></ul></li><li class="tsd-parameter"><h5 id="poll"><span class="tsd-kind-method">Poll</span><span class="tsd-signature-symbol">:</span> function</h5><ul class="tsd-signatures"><li class=""><div class="tsd-signature tsd-anchor-link" id="poll-1"><span class="tsd-kind-call-signature">Poll</span><span class="tsd-signature-symbol">(</span><br/>    <span class="tsd-signature-symbol">...</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">[</span><br/>        client<span class="tsd-signature-symbol">:</span> <a href="../interfaces/UsingClient.html" class="tsd-signature-type tsd-kind-interface">UsingClient</a><span class="tsd-signature-symbol">,</span><br/>        data<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">APIPoll</span><span class="tsd-signature-symbol">,</span><br/>        channelId<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">,</span><br/>        messageId<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">,</span><br/>    <span class="tsd-signature-symbol">]</span><span class="tsd-signature-symbol">,</span><br/><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <a href="../classes/Poll.html" class="tsd-signature-type tsd-kind-class">Poll</a><a href="#poll-1" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></div><div class="tsd-description"><div class="tsd-parameters"><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameter-list"><li><span><span class="tsd-signature-symbol">...</span><span class="tsd-kind-parameter">args</span>: <span class="tsd-signature-symbol">[</span>client<span class="tsd-signature-symbol">:</span> <a href="../interfaces/UsingClient.html" class="tsd-signature-type tsd-kind-interface">UsingClient</a><span class="tsd-signature-symbol">,</span> data<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">APIPoll</span><span class="tsd-signature-symbol">,</span> channelId<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">,</span> messageId<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">]</span></span></li></ul></div><h4 class="tsd-returns-title">Returns <a href="../classes/Poll.html" class="tsd-signature-type tsd-kind-class">Poll</a></h4><aside class="tsd-sources"><ul><li>Defined in <a href="https://github.com/tiramisulabs/seyfert//blob/9ae4290ef220ffc96449d469d16d76b2031eca69/src/client/transformers.ts#L159">src/client/transformers.ts:159</a></li></ul></aside></div></li></ul></li><li class="tsd-parameter"><h5 id="stagechannel"><span class="tsd-kind-method">StageChannel</span><span class="tsd-signature-symbol">:</span> function</h5><ul class="tsd-signatures"><li class=""><div class="tsd-signature tsd-anchor-link" id="stagechannel-1"><span class="tsd-kind-call-signature">StageChannel</span><span class="tsd-signature-symbol">(</span><br/>    <span class="tsd-signature-symbol">...</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">[</span>client<span class="tsd-signature-symbol">:</span> <a href="../interfaces/UsingClient.html" class="tsd-signature-type tsd-kind-interface">UsingClient</a><span class="tsd-signature-symbol">,</span> data<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">APIGuildChannel</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">ChannelType</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">]</span><span class="tsd-signature-symbol">,</span><br/><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <a href="../classes/StageChannel.html" class="tsd-signature-type tsd-kind-class">StageChannel</a><a href="#stagechannel-1" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></div><div class="tsd-description"><div class="tsd-parameters"><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameter-list"><li><span><span class="tsd-signature-symbol">...</span><span class="tsd-kind-parameter">args</span>: <span class="tsd-signature-symbol">[</span>client<span class="tsd-signature-symbol">:</span> <a href="../interfaces/UsingClient.html" class="tsd-signature-type tsd-kind-interface">UsingClient</a><span class="tsd-signature-symbol">,</span> data<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">APIGuildChannel</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">ChannelType</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">]</span></span></li></ul></div><h4 class="tsd-returns-title">Returns <a href="../classes/StageChannel.html" class="tsd-signature-type tsd-kind-class">StageChannel</a></h4><aside class="tsd-sources"><ul><li>Defined in <a href="https://github.com/tiramisulabs/seyfert//blob/9ae4290ef220ffc96449d469d16d76b2031eca69/src/client/transformers.ts#L103">src/client/transformers.ts:103</a></li></ul></aside></div></li></ul></li><li class="tsd-parameter"><h5 id="sticker"><span class="tsd-kind-method">Sticker</span><span class="tsd-signature-symbol">:</span> function</h5><ul class="tsd-signatures"><li class=""><div class="tsd-signature tsd-anchor-link" id="sticker-1"><span class="tsd-kind-call-signature">Sticker</span><span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">...</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">[</span>client<span class="tsd-signature-symbol">:</span> <a href="../interfaces/UsingClient.html" class="tsd-signature-type tsd-kind-interface">UsingClient</a><span class="tsd-signature-symbol">,</span> data<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">APISticker</span><span class="tsd-signature-symbol">]</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <a href="../classes/Sticker.html" class="tsd-signature-type tsd-kind-class">Sticker</a><a href="#sticker-1" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></div><div class="tsd-description"><div class="tsd-parameters"><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameter-list"><li><span><span class="tsd-signature-symbol">...</span><span class="tsd-kind-parameter">args</span>: <span class="tsd-signature-symbol">[</span>client<span class="tsd-signature-symbol">:</span> <a href="../interfaces/UsingClient.html" class="tsd-signature-type tsd-kind-interface">UsingClient</a><span class="tsd-signature-symbol">,</span> data<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">APISticker</span><span class="tsd-signature-symbol">]</span></span></li></ul></div><h4 class="tsd-returns-title">Returns <a href="../classes/Sticker.html" class="tsd-signature-type tsd-kind-class">Sticker</a></h4><aside class="tsd-sources"><ul><li>Defined in <a href="https://github.com/tiramisulabs/seyfert//blob/9ae4290ef220ffc96449d469d16d76b2031eca69/src/client/transformers.ts#L162">src/client/transformers.ts:162</a></li></ul></aside></div></li></ul></li><li class="tsd-parameter"><h5 id="textguildchannel"><span class="tsd-kind-method">TextGuildChannel</span><span class="tsd-signature-symbol">:</span> function</h5><ul class="tsd-signatures"><li class=""><div class="tsd-signature tsd-anchor-link" id="textguildchannel-1"><span class="tsd-kind-call-signature">TextGuildChannel</span><span class="tsd-signature-symbol">(</span><br/>    <span class="tsd-signature-symbol">...</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">[</span>client<span class="tsd-signature-symbol">:</span> <a href="../interfaces/UsingClient.html" class="tsd-signature-type tsd-kind-interface">UsingClient</a><span class="tsd-signature-symbol">,</span> data<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">APIGuildChannel</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">ChannelType</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">]</span><span class="tsd-signature-symbol">,</span><br/><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <a href="../classes/TextGuildChannel.html" class="tsd-signature-type tsd-kind-class">TextGuildChannel</a><a href="#textguildchannel-1" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></div><div class="tsd-description"><div class="tsd-parameters"><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameter-list"><li><span><span class="tsd-signature-symbol">...</span><span class="tsd-kind-parameter">args</span>: <span class="tsd-signature-symbol">[</span>client<span class="tsd-signature-symbol">:</span> <a href="../interfaces/UsingClient.html" class="tsd-signature-type tsd-kind-interface">UsingClient</a><span class="tsd-signature-symbol">,</span> data<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">APIGuildChannel</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">ChannelType</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">]</span></span></li></ul></div><h4 class="tsd-returns-title">Returns <a href="../classes/TextGuildChannel.html" class="tsd-signature-type tsd-kind-class">TextGuildChannel</a></h4><aside class="tsd-sources"><ul><li>Defined in <a href="https://github.com/tiramisulabs/seyfert//blob/9ae4290ef220ffc96449d469d16d76b2031eca69/src/client/transformers.ts#L94">src/client/transformers.ts:94</a></li></ul></aside></div></li></ul></li><li class="tsd-parameter"><h5 id="threadchannel"><span class="tsd-kind-method">ThreadChannel</span><span class="tsd-signature-symbol">:</span> function</h5><ul class="tsd-signatures"><li class=""><div class="tsd-signature tsd-anchor-link" id="threadchannel-1"><span class="tsd-kind-call-signature">ThreadChannel</span><span class="tsd-signature-symbol">(</span><br/>    <span class="tsd-signature-symbol">...</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">[</span>client<span class="tsd-signature-symbol">:</span> <a href="../interfaces/UsingClient.html" class="tsd-signature-type tsd-kind-interface">UsingClient</a><span class="tsd-signature-symbol">,</span> data<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">APIChannelBase</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">ChannelType</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">]</span><span class="tsd-signature-symbol">,</span><br/><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <a href="../classes/ThreadChannel.html" class="tsd-signature-type tsd-kind-class">ThreadChannel</a><a href="#threadchannel-1" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></div><div class="tsd-description"><div class="tsd-parameters"><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameter-list"><li><span><span class="tsd-signature-symbol">...</span><span class="tsd-kind-parameter">args</span>: <span class="tsd-signature-symbol">[</span>client<span class="tsd-signature-symbol">:</span> <a href="../interfaces/UsingClient.html" class="tsd-signature-type tsd-kind-interface">UsingClient</a><span class="tsd-signature-symbol">,</span> data<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">APIChannelBase</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">ChannelType</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">]</span></span></li></ul></div><h4 class="tsd-returns-title">Returns <a href="../classes/ThreadChannel.html" class="tsd-signature-type tsd-kind-class">ThreadChannel</a></h4><aside class="tsd-sources"><ul><li>Defined in <a href="https://github.com/tiramisulabs/seyfert//blob/9ae4290ef220ffc96449d469d16d76b2031eca69/src/client/transformers.ts#L112">src/client/transformers.ts:112</a></li></ul></aside></div></li></ul></li><li class="tsd-parameter"><h5 id="user"><span class="tsd-kind-method">User</span><span class="tsd-signature-symbol">:</span> function</h5><ul class="tsd-signatures"><li class=""><div class="tsd-signature tsd-anchor-link" id="user-1"><span class="tsd-kind-call-signature">User</span><span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">...</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">[</span>client<span class="tsd-signature-symbol">:</span> <a href="../interfaces/UsingClient.html" class="tsd-signature-type tsd-kind-interface">UsingClient</a><span class="tsd-signature-symbol">,</span> data<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">APIUser</span><span class="tsd-signature-symbol">]</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <a href="../classes/User.html" class="tsd-signature-type tsd-kind-class">User</a><a href="#user-1" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></div><div class="tsd-description"><div class="tsd-parameters"><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameter-list"><li><span><span class="tsd-signature-symbol">...</span><span class="tsd-kind-parameter">args</span>: <span class="tsd-signature-symbol">[</span>client<span class="tsd-signature-symbol">:</span> <a href="../interfaces/UsingClient.html" class="tsd-signature-type tsd-kind-interface">UsingClient</a><span class="tsd-signature-symbol">,</span> data<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">APIUser</span><span class="tsd-signature-symbol">]</span></span></li></ul></div><h4 class="tsd-returns-title">Returns <a href="../classes/User.html" class="tsd-signature-type tsd-kind-class">User</a></h4><aside class="tsd-sources"><ul><li>Defined in <a href="https://github.com/tiramisulabs/seyfert//blob/9ae4290ef220ffc96449d469d16d76b2031eca69/src/client/transformers.ts#L165">src/client/transformers.ts:165</a></li></ul></aside></div></li></ul></li><li class="tsd-parameter"><h5 id="voicechannel"><span class="tsd-kind-method">VoiceChannel</span><span class="tsd-signature-symbol">:</span> function</h5><ul class="tsd-signatures"><li class=""><div class="tsd-signature tsd-anchor-link" id="voicechannel-1"><span class="tsd-kind-call-signature">VoiceChannel</span><span class="tsd-signature-symbol">(</span><br/>    <span class="tsd-signature-symbol">...</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">[</span>client<span class="tsd-signature-symbol">:</span> <a href="../interfaces/UsingClient.html" class="tsd-signature-type tsd-kind-interface">UsingClient</a><span class="tsd-signature-symbol">,</span> data<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">APIGuildChannel</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">ChannelType</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">]</span><span class="tsd-signature-symbol">,</span><br/><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <a href="../classes/VoiceChannel.html" class="tsd-signature-type tsd-kind-class">VoiceChannel</a><a href="#voicechannel-1" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></div><div class="tsd-description"><div class="tsd-parameters"><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameter-list"><li><span><span class="tsd-signature-symbol">...</span><span class="tsd-kind-parameter">args</span>: <span class="tsd-signature-symbol">[</span>client<span class="tsd-signature-symbol">:</span> <a href="../interfaces/UsingClient.html" class="tsd-signature-type tsd-kind-interface">UsingClient</a><span class="tsd-signature-symbol">,</span> data<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">APIGuildChannel</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">ChannelType</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">]</span></span></li></ul></div><h4 class="tsd-returns-title">Returns <a href="../classes/VoiceChannel.html" class="tsd-signature-type tsd-kind-class">VoiceChannel</a></h4><aside class="tsd-sources"><ul><li>Defined in <a href="https://github.com/tiramisulabs/seyfert//blob/9ae4290ef220ffc96449d469d16d76b2031eca69/src/client/transformers.ts#L100">src/client/transformers.ts:100</a></li></ul></aside></div></li></ul></li><li class="tsd-parameter"><h5 id="voicestate"><span class="tsd-kind-method">VoiceState</span><span class="tsd-signature-symbol">:</span> function</h5><ul class="tsd-signatures"><li class=""><div class="tsd-signature tsd-anchor-link" id="voicestate-1"><span class="tsd-kind-call-signature">VoiceState</span><span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">...</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">[</span>client<span class="tsd-signature-symbol">:</span> <a href="../interfaces/UsingClient.html" class="tsd-signature-type tsd-kind-interface">UsingClient</a><span class="tsd-signature-symbol">,</span> data<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">APIVoiceState</span><span class="tsd-signature-symbol">]</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <a href="../classes/VoiceState.html" class="tsd-signature-type tsd-kind-class">VoiceState</a><a href="#voicestate-1" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></div><div class="tsd-description"><div class="tsd-parameters"><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameter-list"><li><span><span class="tsd-signature-symbol">...</span><span class="tsd-kind-parameter">args</span>: <span class="tsd-signature-symbol">[</span>client<span class="tsd-signature-symbol">:</span> <a href="../interfaces/UsingClient.html" class="tsd-signature-type tsd-kind-interface">UsingClient</a><span class="tsd-signature-symbol">,</span> data<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">APIVoiceState</span><span class="tsd-signature-symbol">]</span></span></li></ul></div><h4 class="tsd-returns-title">Returns <a href="../classes/VoiceState.html" class="tsd-signature-type tsd-kind-class">VoiceState</a></h4><aside class="tsd-sources"><ul><li>Defined in <a href="https://github.com/tiramisulabs/seyfert//blob/9ae4290ef220ffc96449d469d16d76b2031eca69/src/client/transformers.ts#L168">src/client/transformers.ts:168</a></li></ul></aside></div></li></ul></li><li class="tsd-parameter"><h5 id="webhook"><span class="tsd-kind-method">Webhook</span><span class="tsd-signature-symbol">:</span> function</h5><ul class="tsd-signatures"><li class=""><div class="tsd-signature tsd-anchor-link" id="webhook-1"><span class="tsd-kind-call-signature">Webhook</span><span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">...</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">[</span>client<span class="tsd-signature-symbol">:</span> <a href="../interfaces/UsingClient.html" class="tsd-signature-type tsd-kind-interface">UsingClient</a><span class="tsd-signature-symbol">,</span> data<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">APIWebhook</span><span class="tsd-signature-symbol">]</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <a href="../classes/Webhook.html" class="tsd-signature-type tsd-kind-class">Webhook</a><a href="#webhook-1" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></div><div class="tsd-description"><div class="tsd-parameters"><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameter-list"><li><span><span class="tsd-signature-symbol">...</span><span class="tsd-kind-parameter">args</span>: <span class="tsd-signature-symbol">[</span>client<span class="tsd-signature-symbol">:</span> <a href="../interfaces/UsingClient.html" class="tsd-signature-type tsd-kind-interface">UsingClient</a><span class="tsd-signature-symbol">,</span> data<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">APIWebhook</span><span class="tsd-signature-symbol">]</span></span></li></ul></div><h4 class="tsd-returns-title">Returns <a href="../classes/Webhook.html" class="tsd-signature-type tsd-kind-class">Webhook</a></h4><aside class="tsd-sources"><ul><li>Defined in <a href="https://github.com/tiramisulabs/seyfert//blob/9ae4290ef220ffc96449d469d16d76b2031eca69/src/client/transformers.ts#L171">src/client/transformers.ts:171</a></li></ul></aside></div></li></ul></li><li class="tsd-parameter"><h5 id="webhookmessage"><span class="tsd-kind-method">WebhookMessage</span><span class="tsd-signature-symbol">:</span> function</h5><ul class="tsd-signatures"><li class=""><div class="tsd-signature tsd-anchor-link" id="webhookmessage-1"><span class="tsd-kind-call-signature">WebhookMessage</span><span class="tsd-signature-symbol">(</span><br/>    <span class="tsd-signature-symbol">...</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">[</span><br/>        client<span class="tsd-signature-symbol">:</span> <a href="../interfaces/UsingClient.html" class="tsd-signature-type tsd-kind-interface">UsingClient</a><span class="tsd-signature-symbol">,</span><br/>        data<span class="tsd-signature-symbol">:</span> <a href="../types/MessageData.html" class="tsd-signature-type tsd-kind-type-alias">MessageData</a><span class="tsd-signature-symbol">,</span><br/>        webhookId<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">,</span><br/>        webhookToken<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">,</span><br/>    <span class="tsd-signature-symbol">]</span><span class="tsd-signature-symbol">,</span><br/><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <a href="../classes/WebhookMessage.html" class="tsd-signature-type tsd-kind-class">WebhookMessage</a><a href="#webhookmessage-1" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></div><div class="tsd-description"><div class="tsd-parameters"><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameter-list"><li><span><span class="tsd-signature-symbol">...</span><span class="tsd-kind-parameter">args</span>: <span class="tsd-signature-symbol">[</span><br/>    client<span class="tsd-signature-symbol">:</span> <a href="../interfaces/UsingClient.html" class="tsd-signature-type tsd-kind-interface">UsingClient</a><span class="tsd-signature-symbol">,</span><br/>    data<span class="tsd-signature-symbol">:</span> <a href="../types/MessageData.html" class="tsd-signature-type tsd-kind-type-alias">MessageData</a><span class="tsd-signature-symbol">,</span><br/>    webhookId<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">,</span><br/>    webhookToken<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">,</span><br/><span class="tsd-signature-symbol">]</span></span></li></ul></div><h4 class="tsd-returns-title">Returns <a href="../classes/WebhookMessage.html" class="tsd-signature-type tsd-kind-class">WebhookMessage</a></h4><aside class="tsd-sources"><ul><li>Defined in <a href="https://github.com/tiramisulabs/seyfert//blob/9ae4290ef220ffc96449d469d16d76b2031eca69/src/client/transformers.ts#L156">src/client/transformers.ts:156</a></li></ul></aside></div></li></ul></li></ul></div><aside class="tsd-sources"><ul><li>Defined in <a href="https://github.com/tiramisulabs/seyfert//blob/9ae4290ef220ffc96449d469d16d76b2031eca69/src/client/transformers.ts#L75">src/client/transformers.ts:75</a></li></ul></aside></div><div class="col-sidebar"><div class="page-menu"><div class="tsd-navigation settings"><details class="tsd-accordion"><summary class="tsd-accordion-summary"><svg width="20" height="20" viewBox="0 0 24 24" fill="none" aria-hidden="true"><use href="../assets/icons.svg#icon-chevronDown"></use></svg><h3>Settings</h3></summary><div class="tsd-accordion-details"><div class="tsd-filter-visibility"><span class="settings-label">Member Visibility</span><ul id="tsd-filter-options"><li class="tsd-filter-item"><label class="tsd-filter-input"><input type="checkbox" id="tsd-filter-protected" name="protected"/><svg width="32" height="32" viewBox="0 0 32 32" aria-hidden="true"><rect class="tsd-checkbox-background" width="30" height="30" x="1" y="1" rx="6" fill="none"></rect><path class="tsd-checkbox-checkmark" d="M8.35422 16.8214L13.2143 21.75L24.6458 10.25" stroke="none" stroke-width="3.5" stroke-linejoin="round" fill="none"></path></svg><span>Protected</span></label></li><li class="tsd-filter-item"><label class="tsd-filter-input"><input type="checkbox" id="tsd-filter-inherited" name="inherited" checked/><svg width="32" height="32" viewBox="0 0 32 32" aria-hidden="true"><rect class="tsd-checkbox-background" width="30" height="30" x="1" y="1" rx="6" fill="none"></rect><path class="tsd-checkbox-checkmark" d="M8.35422 16.8214L13.2143 21.75L24.6458 10.25" stroke="none" stroke-width="3.5" stroke-linejoin="round" fill="none"></path></svg><span>Inherited</span></label></li><li class="tsd-filter-item"><label class="tsd-filter-input"><input type="checkbox" id="tsd-filter-external" name="external"/><svg width="32" height="32" viewBox="0 0 32 32" aria-hidden="true"><rect class="tsd-checkbox-background" width="30" height="30" x="1" y="1" rx="6" fill="none"></rect><path class="tsd-checkbox-checkmark" d="M8.35422 16.8214L13.2143 21.75L24.6458 10.25" stroke="none" stroke-width="3.5" stroke-linejoin="round" fill="none"></path></svg><span>External</span></label></li></ul></div><div class="tsd-theme-toggle"><label class="settings-label" for="tsd-theme">Theme</label><select id="tsd-theme"><option value="os">OS</option><option value="light">Light</option><option value="dark">Dark</option></select></div></div></details></div></div><div class="site-menu"><nav class="tsd-navigation"><a href="../modules.html">seyfert</a><ul class="tsd-small-nested-navigation" id="tsd-nav-container"><li>Loading...</li></ul></nav></div></div></div><footer><p class="tsd-generator">Generated using <a href="https://typedoc.org/" target="_blank">TypeDoc</a></p></footer><div class="overlay"></div></body></html>
