name: <PERSON><PERSON>, <PERSON><PERSON>, Format & Publish
on:
  push:
  pull_request:

jobs:
  linter:
    runs-on: ubuntu-latest

    steps:
      - name: check out code
        uses: actions/checkout@v4
        with:
          ref: ${{ github.ref }}

      - name: Install Node
        uses: actions/setup-node@v4
        with:
          node-version: '20.x'
          registry-url: 'https://registry.npmjs.org'

      - uses: pnpm/action-setup@v4
        with:
          version: 9

      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: Run Biome
        run: pnpm run check
        id: biome

      - name: Commit and Push changes
        continue-on-error: true
        run: |
          git config --global user.name 'github-actions[bot]'
          git config --global user.email '41898282+github-actions[bot]@users.noreply.github.com'
          git add -A
          if ! git diff-index --quiet HEAD; then
            git commit -m "chore: apply formatting"
            git push origin HEAD:${{ github.ref }}
          fi
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          HUSKY: 0

      - name: Test suites
        run: pnpm test
  
      - name: Run Bot
        id: bot-up
        if: env.BOT_TOKEN != ''
        env:
          BOT_TOKEN: ${{ secrets.BOT_TOKEN }}
        run: node ./tests/bot.js
        

      - name: Publish in pkg.pr.new
        run: pnpx pkg-pr-new publish
        if: github.repository == 'tiramisulabs/seyfert'
