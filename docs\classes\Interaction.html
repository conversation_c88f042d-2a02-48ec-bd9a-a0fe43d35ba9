<!DOCTYPE html><html class="default" lang="en" data-base="../"><head><meta charset="utf-8"/><meta http-equiv="x-ua-compatible" content="IE=edge"/><title>Interaction | seyfert</title><meta name="description" content="Documentation for seyfert"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="stylesheet" href="../assets/style.css"/><link rel="stylesheet" href="../assets/highlight.css"/><script defer src="../assets/main.js"></script><script async src="../assets/icons.js" id="tsd-icons-script"></script><script async src="../assets/search.js" id="tsd-search-script"></script><script async src="../assets/navigation.js" id="tsd-nav-script"></script><script async src="../assets/hierarchy.js" id="tsd-hierarchy-script"></script></head><body><script>document.documentElement.dataset.theme = localStorage.getItem("tsd-theme") || "os";document.body.style.display="none";setTimeout(() => window.app?app.showPage():document.body.style.removeProperty("display"),500)</script><header class="tsd-page-toolbar"><div class="tsd-toolbar-contents container"><a href="../index.html" class="title">seyfert</a><div id="tsd-toolbar-links"></div><button id="tsd-search-trigger" class="tsd-widget" aria-label="Search"><svg width="16" height="16" viewBox="0 0 16 16" fill="none" aria-hidden="true"><use href="../assets/icons.svg#icon-search"></use></svg></button><dialog id="tsd-search" aria-label="Search"><input role="combobox" id="tsd-search-input" aria-controls="tsd-search-results" aria-autocomplete="list" aria-expanded="true" autocapitalize="off" autocomplete="off" placeholder="Search the docs" maxLength="100"/><ul role="listbox" id="tsd-search-results"></ul><div id="tsd-search-status" aria-live="polite" aria-atomic="true"><div>Preparing search index...</div></div></dialog><a href="#" class="tsd-widget menu" id="tsd-toolbar-menu-trigger" data-toggle="menu" aria-label="Menu"><svg width="16" height="16" viewBox="0 0 16 16" fill="none" aria-hidden="true"><use href="../assets/icons.svg#icon-menu"></use></svg></a></div></header><div class="container container-main"><div class="col-content"><div class="tsd-page-title"><ul class="tsd-breadcrumb" aria-label="Breadcrumb"><li><a href="" aria-current="page">Interaction</a></li></ul><h1>Class Interaction&lt;FromGuild, Type&gt;</h1></div><section class="tsd-panel"><h4>Type Parameters</h4><ul class="tsd-type-parameter-list"><li><span id="fromguild"><span class="tsd-kind-type-parameter">FromGuild</span> <span class="tsd-signature-keyword">extends</span> <span class="tsd-signature-type">boolean</span> = <span class="tsd-signature-type">boolean</span></span></li><li><span id="type"><span class="tsd-kind-type-parameter">Type</span> <span class="tsd-signature-keyword">extends</span> <span class="tsd-signature-type">APIInteraction</span> = <span class="tsd-signature-type">APIInteraction</span></span></li></ul></section><section class="tsd-panel tsd-hierarchy" data-refl="17914"><h4>Hierarchy (<a href="../hierarchy.html#Interaction">View Summary</a>)</h4><ul class="tsd-hierarchy"><li class="tsd-hierarchy-item"><a href="BaseInteraction.html" class="tsd-signature-type tsd-kind-class">BaseInteraction</a><span class="tsd-signature-symbol">&lt;</span><a class="tsd-signature-type tsd-kind-type-parameter" href="#constructorinteractionfromguild">FromGuild</a><span class="tsd-signature-symbol">,</span> <a class="tsd-signature-type tsd-kind-type-parameter" href="#constructorinteractiontype">Type</a><span class="tsd-signature-symbol">&gt;</span><ul class="tsd-hierarchy"><li class="tsd-hierarchy-item"><span class="tsd-hierarchy-target">Interaction</span><ul class="tsd-hierarchy"><li class="tsd-hierarchy-item"><a href="ApplicationCommandInteraction.html" class="tsd-signature-type tsd-kind-class">ApplicationCommandInteraction</a></li><li class="tsd-hierarchy-item"><a href="ComponentInteraction.html" class="tsd-signature-type tsd-kind-class">ComponentInteraction</a></li></ul></li></ul></li></ul></section><aside class="tsd-sources"><ul><li>Defined in <a href="https://github.com/tiramisulabs/seyfert//blob/9ae4290ef220ffc96449d469d16d76b2031eca69/src/structures/Interaction.ts#L450">src/structures/Interaction.ts:450</a></li></ul></aside><section class="tsd-panel-group tsd-index-group"><section class="tsd-panel tsd-index-panel"><details class="tsd-index-content tsd-accordion" open><summary class="tsd-accordion-summary tsd-index-summary"><svg width="20" height="20" viewBox="0 0 24 24" fill="none" aria-hidden="true"><use href="../assets/icons.svg#icon-chevronDown"></use></svg><h5 class="tsd-index-heading uppercase">Index</h5></summary><div class="tsd-accordion-details"><section class="tsd-index-section"><h3 class="tsd-index-heading">Constructors</h3><div class="tsd-index-list"><a href="#constructor" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Constructor"><use href="../assets/icons.svg#icon-512"></use></svg><span>constructor</span></a>
</div></section><section class="tsd-index-section"><h3 class="tsd-index-heading">Properties</h3><div class="tsd-index-list"><a href="#__reply" class="tsd-index-link tsd-is-protected tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>__<wbr/>reply?</span></a>
<a href="#applicationid" class="tsd-index-link tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>application<wbr/>Id</span></a>
<a href="#apppermissions" class="tsd-index-link tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>app<wbr/>Permissions</span></a>
<a href="#attachmentsizelimit" class="tsd-index-link tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>attachment<wbr/>Size<wbr/>Limit</span></a>
<a href="#authorizingintegrationowners" class="tsd-index-link tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>authorizing<wbr/>Integration<wbr/>Owners</span></a>
<a href="#channel" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>channel</span></a>
<a href="#client" class="tsd-index-link tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>client</span></a>
<a href="#context" class="tsd-index-link tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>context?</span></a>
<a href="#data" class="tsd-index-link tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>data?</span></a>
<a href="#entitlements" class="tsd-index-link tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>entitlements</span></a>
<a href="#guild" class="tsd-index-link tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>guild?</span></a>
<a href="#guildid" class="tsd-index-link tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>guild<wbr/>Id?</span></a>
<a href="#guildlocale" class="tsd-index-link tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>guild<wbr/>Locale?</span></a>
<a href="#id" class="tsd-index-link tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>id</span></a>
<a href="#locale" class="tsd-index-link tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>locale</span></a>
<a href="#member" class="tsd-index-link tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>member</span></a>
<a href="#message" class="tsd-index-link tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>message?</span></a>
<a href="#replied" class="tsd-index-link tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>replied?</span></a>
<a href="#token" class="tsd-index-link tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>token</span></a>
<a href="#user" class="tsd-index-link tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>user</span></a>
<a href="#version" class="tsd-index-link tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>version</span></a>
</div></section><section class="tsd-index-section"><h3 class="tsd-index-heading">Accessors</h3><div class="tsd-index-list"><a href="#createdat" class="tsd-index-link tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Accessor"><use href="../assets/icons.svg#icon-262144"></use></svg><span>created<wbr/>At</span></a>
<a href="#createdtimestamp" class="tsd-index-link tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Accessor"><use href="../assets/icons.svg#icon-262144"></use></svg><span>created<wbr/>Timestamp</span></a>
</div></section><section class="tsd-index-section"><h3 class="tsd-index-heading">Methods</h3><div class="tsd-index-list"><a href="#deferreply" class="tsd-index-link tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Method"><use href="../assets/icons.svg#icon-2048"></use></svg><span>defer<wbr/>Reply</span></a>
<a href="#deletemessage" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Method"><use href="../assets/icons.svg#icon-2048"></use></svg><span>delete<wbr/>Message</span></a>
<a href="#deleteresponse" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Method"><use href="../assets/icons.svg#icon-2048"></use></svg><span>delete<wbr/>Response</span></a>
<a href="#editmessage" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Method"><use href="../assets/icons.svg#icon-2048"></use></svg><span>edit<wbr/>Message</span></a>
<a href="#editorreply" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Method"><use href="../assets/icons.svg#icon-2048"></use></svg><span>edit<wbr/>Or<wbr/>Reply</span></a>
<a href="#editresponse" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Method"><use href="../assets/icons.svg#icon-2048"></use></svg><span>edit<wbr/>Response</span></a>
<a href="#fetchguild" class="tsd-index-link tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Method"><use href="../assets/icons.svg#icon-2048"></use></svg><span>fetch<wbr/>Guild</span></a>
<a href="#fetchmessage" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Method"><use href="../assets/icons.svg#icon-2048"></use></svg><span>fetch<wbr/>Message</span></a>
<a href="#fetchresponse" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Method"><use href="../assets/icons.svg#icon-2048"></use></svg><span>fetch<wbr/>Response</span></a>
<a href="#followup" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Method"><use href="../assets/icons.svg#icon-2048"></use></svg><span>followup</span></a>
<a href="#isautocomplete" class="tsd-index-link tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Method"><use href="../assets/icons.svg#icon-2048"></use></svg><span>is<wbr/>Autocomplete</span></a>
<a href="#isbutton" class="tsd-index-link tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Method"><use href="../assets/icons.svg#icon-2048"></use></svg><span>is<wbr/>Button</span></a>
<a href="#ischannelselectmenu" class="tsd-index-link tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Method"><use href="../assets/icons.svg#icon-2048"></use></svg><span>is<wbr/>Channel<wbr/>Select<wbr/>Menu</span></a>
<a href="#ischatinput" class="tsd-index-link tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Method"><use href="../assets/icons.svg#icon-2048"></use></svg><span>is<wbr/>Chat<wbr/>Input</span></a>
<a href="#isentrypoint" class="tsd-index-link tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Method"><use href="../assets/icons.svg#icon-2048"></use></svg><span>is<wbr/>Entry<wbr/>Point</span></a>
<a href="#ismentionableselectmenu" class="tsd-index-link tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Method"><use href="../assets/icons.svg#icon-2048"></use></svg><span>is<wbr/>Mentionable<wbr/>Select<wbr/>Menu</span></a>
<a href="#ismessage" class="tsd-index-link tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Method"><use href="../assets/icons.svg#icon-2048"></use></svg><span>is<wbr/>Message</span></a>
<a href="#ismodal" class="tsd-index-link tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Method"><use href="../assets/icons.svg#icon-2048"></use></svg><span>is<wbr/>Modal</span></a>
<a href="#isroleselectmenu" class="tsd-index-link tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Method"><use href="../assets/icons.svg#icon-2048"></use></svg><span>is<wbr/>Role<wbr/>Select<wbr/>Menu</span></a>
<a href="#isstringselectmenu" class="tsd-index-link tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Method"><use href="../assets/icons.svg#icon-2048"></use></svg><span>is<wbr/>String<wbr/>Select<wbr/>Menu</span></a>
<a href="#isuser" class="tsd-index-link tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Method"><use href="../assets/icons.svg#icon-2048"></use></svg><span>is<wbr/>User</span></a>
<a href="#isuserselectmenu" class="tsd-index-link tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Method"><use href="../assets/icons.svg#icon-2048"></use></svg><span>is<wbr/>User<wbr/>Select<wbr/>Menu</span></a>
<a href="#modal" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Method"><use href="../assets/icons.svg#icon-2048"></use></svg><span>modal</span></a>
<a href="#reply" class="tsd-index-link tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Method"><use href="../assets/icons.svg#icon-2048"></use></svg><span>reply</span></a>
<a href="#write" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Method"><use href="../assets/icons.svg#icon-2048"></use></svg><span>write</span></a>
<a href="#from" class="tsd-index-link tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Method"><use href="../assets/icons.svg#icon-2048"></use></svg><span>from</span></a>
<a href="#transformbody" class="tsd-index-link tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Method"><use href="../assets/icons.svg#icon-2048"></use></svg><span>transform<wbr/>Body</span></a>
<a href="#transformbodyrequest" class="tsd-index-link tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Method"><use href="../assets/icons.svg#icon-2048"></use></svg><span>transform<wbr/>Body<wbr/>Request</span></a>
</div></section></div></details></section></section><details class="tsd-panel-group tsd-member-group tsd-accordion" open><summary class="tsd-accordion-summary" data-key="section-Constructors"><svg width="20" height="20" viewBox="0 0 24 24" fill="none" aria-hidden="true"><use href="../assets/icons.svg#icon-chevronDown"></use></svg><h2>Constructors</h2></summary><section><section class="tsd-panel tsd-member"><h3 class="tsd-anchor-link" id="constructor"><span>constructor</span><a href="#constructor" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><ul class="tsd-signatures"><li class=""><div class="tsd-signature tsd-anchor-link" id="constructorinteraction"><span class="tsd-signature-keyword">new</span> <span class="tsd-kind-constructor-signature">Interaction</span><span class="tsd-signature-symbol">&lt;</span><br/>    <a class="tsd-signature-type tsd-kind-type-parameter" href="#constructorinteractionfromguild">FromGuild</a> <span class="tsd-signature-keyword">extends</span> <span class="tsd-signature-type">boolean</span> <span class="tsd-signature-symbol">=</span> <span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">,</span><br/>    <a class="tsd-signature-type tsd-kind-type-parameter" href="#constructorinteractiontype">Type</a> <span class="tsd-signature-keyword">extends</span> <span class="tsd-signature-type">APIInteraction</span> <span class="tsd-signature-symbol">=</span> <span class="tsd-signature-type">APIInteraction</span><span class="tsd-signature-symbol">,</span><br/><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">(</span><br/>    <span class="tsd-kind-parameter">client</span><span class="tsd-signature-symbol">:</span> <a href="../interfaces/UsingClient.html" class="tsd-signature-type tsd-kind-interface">UsingClient</a><span class="tsd-signature-symbol">,</span><br/>    <span class="tsd-kind-parameter">interaction</span><span class="tsd-signature-symbol">:</span> <a class="tsd-signature-type tsd-kind-type-parameter" href="#constructorinteractiontype">Type</a><span class="tsd-signature-symbol">,</span><br/>    <span class="tsd-kind-parameter">__reply</span><span class="tsd-signature-symbol">?:</span> <a href="../types/__InternalReplyFunction.html" class="tsd-signature-type tsd-kind-type-alias">__InternalReplyFunction</a><span class="tsd-signature-symbol">,</span><br/><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <a href="" class="tsd-signature-type tsd-kind-class">Interaction</a><span class="tsd-signature-symbol">&lt;</span><a class="tsd-signature-type tsd-kind-type-parameter" href="#constructorinteractionfromguild">FromGuild</a><span class="tsd-signature-symbol">,</span> <a class="tsd-signature-type tsd-kind-type-parameter" href="#constructorinteractiontype">Type</a><span class="tsd-signature-symbol">&gt;</span><a href="#constructorinteraction" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></div><div class="tsd-description"><section class="tsd-panel"><h4>Type Parameters</h4><ul class="tsd-type-parameter-list"><li><span id="constructorinteractionfromguild"><span class="tsd-kind-type-parameter">FromGuild</span> <span class="tsd-signature-keyword">extends</span> <span class="tsd-signature-type">boolean</span> = <span class="tsd-signature-type">boolean</span></span></li><li><span id="constructorinteractiontype"><span class="tsd-kind-type-parameter">Type</span> <span class="tsd-signature-keyword">extends</span> <span class="tsd-signature-type">APIInteraction</span> = <span class="tsd-signature-type">APIInteraction</span></span></li></ul></section><div class="tsd-parameters"><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameter-list"><li><span><span class="tsd-kind-parameter">client</span>: <a href="../interfaces/UsingClient.html" class="tsd-signature-type tsd-kind-interface">UsingClient</a></span></li><li><span><span class="tsd-kind-parameter">interaction</span>: <a class="tsd-signature-type tsd-kind-type-parameter" href="#constructorinteractiontype">Type</a></span></li><li><span><code class="tsd-tag">Optional</code><span class="tsd-kind-parameter">__reply</span>: <a href="../types/__InternalReplyFunction.html" class="tsd-signature-type tsd-kind-type-alias">__InternalReplyFunction</a></span></li></ul></div><h4 class="tsd-returns-title">Returns <a href="" class="tsd-signature-type tsd-kind-class">Interaction</a><span class="tsd-signature-symbol">&lt;</span><a class="tsd-signature-type tsd-kind-type-parameter" href="#constructorinteractionfromguild">FromGuild</a><span class="tsd-signature-symbol">,</span> <a class="tsd-signature-type tsd-kind-type-parameter" href="#constructorinteractiontype">Type</a><span class="tsd-signature-symbol">&gt;</span></h4><aside class="tsd-sources"><p>Inherited from <a href="BaseInteraction.html">BaseInteraction</a>.<a href="BaseInteraction.html#constructor">constructor</a></p><ul><li>Defined in <a href="https://github.com/tiramisulabs/seyfert//blob/9ae4290ef220ffc96449d469d16d76b2031eca69/src/structures/Interaction.ts#L110">src/structures/Interaction.ts:110</a></li></ul></aside></div></li></ul></section></section></details><details class="tsd-panel-group tsd-member-group tsd-accordion" open><summary class="tsd-accordion-summary" data-key="section-Properties"><svg width="20" height="20" viewBox="0 0 24 24" fill="none" aria-hidden="true"><use href="../assets/icons.svg#icon-chevronDown"></use></svg><h2>Properties</h2></summary><section><section class="tsd-panel tsd-member tsd-is-protected tsd-is-inherited"><h3 class="tsd-anchor-link" id="__reply"><code class="tsd-tag">Protected</code> <code class="tsd-tag">Optional</code><span>__<wbr/>reply</span><a href="#__reply" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">__reply</span><span class="tsd-signature-symbol">?:</span> <a href="../types/__InternalReplyFunction.html" class="tsd-signature-type tsd-kind-type-alias">__InternalReplyFunction</a></div><aside class="tsd-sources"><p>Inherited from <a href="BaseInteraction.html">BaseInteraction</a>.<a href="BaseInteraction.html#__reply">__reply</a></p><ul><li>Defined in <a href="https://github.com/tiramisulabs/seyfert//blob/9ae4290ef220ffc96449d469d16d76b2031eca69/src/structures/Interaction.ts#L113">src/structures/Interaction.ts:113</a></li></ul></aside></section><section class="tsd-panel tsd-member tsd-is-inherited"><h3 class="tsd-anchor-link" id="applicationid"><span>application<wbr/>Id</span><a href="#applicationid" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">applicationId</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span></div><aside class="tsd-sources"><p>Inherited from <a href="BaseInteraction.html">BaseInteraction</a>.<a href="BaseInteraction.html#applicationid">applicationId</a></p></aside></section><section class="tsd-panel tsd-member tsd-is-inherited"><h3 class="tsd-anchor-link" id="apppermissions"><span>app<wbr/>Permissions</span><a href="#apppermissions" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">appPermissions</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">PermissionsBitField</span></div><aside class="tsd-sources"><p>Inherited from <a href="BaseInteraction.html">BaseInteraction</a>.<a href="BaseInteraction.html#apppermissions">appPermissions</a></p><ul><li>Defined in <a href="https://github.com/tiramisulabs/seyfert//blob/9ae4290ef220ffc96449d469d16d76b2031eca69/src/structures/Interaction.ts#L107">src/structures/Interaction.ts:107</a></li></ul></aside></section><section class="tsd-panel tsd-member tsd-is-inherited"><h3 class="tsd-anchor-link" id="attachmentsizelimit"><span>attachment<wbr/>Size<wbr/>Limit</span><a href="#attachmentsizelimit" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">attachmentSizeLimit</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">number</span></div><aside class="tsd-sources"><p>Inherited from <a href="BaseInteraction.html">BaseInteraction</a>.<a href="BaseInteraction.html#attachmentsizelimit">attachmentSizeLimit</a></p></aside></section><section class="tsd-panel tsd-member tsd-is-inherited"><h3 class="tsd-anchor-link" id="authorizingintegrationowners"><span>authorizing<wbr/>Integration<wbr/>Owners</span><a href="#authorizingintegrationowners" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">authorizingIntegrationOwners</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span> <span class="tsd-kind-property">&quot;0&quot;</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span> <span class="tsd-kind-property">&quot;1&quot;</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">string</span> <span class="tsd-signature-symbol">}</span></div><aside class="tsd-sources"><p>Inherited from <a href="BaseInteraction.html">BaseInteraction</a>.<a href="BaseInteraction.html#authorizingintegrationowners">authorizingIntegrationOwners</a></p></aside></section><section class="tsd-panel tsd-member"><h3 class="tsd-anchor-link" id="channel"><span>channel</span><a href="#channel" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">channel</span><span class="tsd-signature-symbol">:</span> <a href="../types/AllChannels.html" class="tsd-signature-type tsd-kind-type-alias">AllChannels</a></div><aside class="tsd-sources"><p>Overrides <a href="BaseInteraction.html">BaseInteraction</a>.<a href="BaseInteraction.html#channel">channel</a></p><ul><li>Defined in <a href="https://github.com/tiramisulabs/seyfert//blob/9ae4290ef220ffc96449d469d16d76b2031eca69/src/structures/Interaction.ts#L454">src/structures/Interaction.ts:454</a></li></ul></aside></section><section class="tsd-panel tsd-member tsd-is-inherited"><h3 class="tsd-anchor-link" id="client"><code class="tsd-tag">Readonly</code><span>client</span><a href="#client" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">client</span><span class="tsd-signature-symbol">:</span> <a href="../interfaces/UsingClient.html" class="tsd-signature-type tsd-kind-interface">UsingClient</a></div><aside class="tsd-sources"><p>Inherited from <a href="BaseInteraction.html">BaseInteraction</a>.<a href="BaseInteraction.html#client">client</a></p><ul><li>Defined in <a href="https://github.com/tiramisulabs/seyfert//blob/9ae4290ef220ffc96449d469d16d76b2031eca69/src/structures/Interaction.ts#L111">src/structures/Interaction.ts:111</a></li></ul></aside></section><section class="tsd-panel tsd-member tsd-is-inherited"><h3 class="tsd-anchor-link" id="context"><code class="tsd-tag">Optional</code><span>context</span><a href="#context" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">context</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">InteractionContextType</span></div><aside class="tsd-sources"><p>Inherited from <a href="BaseInteraction.html">BaseInteraction</a>.<a href="BaseInteraction.html#context">context</a></p></aside></section><section class="tsd-panel tsd-member tsd-is-inherited"><h3 class="tsd-anchor-link" id="data"><code class="tsd-tag">Optional</code><span>data</span><a href="#data" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">data</span><span class="tsd-signature-symbol">?:</span><br/>    <span class="tsd-signature-symbol">|</span> (<br/>        <span class="tsd-signature-symbol">|</span> (<br/>            (((((((((((any | { [x: Lowercase&lt;string&gt;]: (any | any)[] | { [x: Lowercase&lt;string&gt;]: (any | any)[] | any | (any | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | (any | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: ((any | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: ((any | any)[] | any)[] | any | ((any | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | ((any | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: (((any | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: (((any | any)[] | any)[] | any)[] | any | (((any | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | (((any | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: ((((any | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: ((((any | any)[] | any)[] | any)[] | any)[] | any | ((((any | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | ((((any | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: (((((any | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: (((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any | (((((any | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | (((((any | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: ((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: ((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | ((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | ((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: (((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: (((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | (((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | (((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: ((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: ((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | ((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | ((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: (((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: (((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | (((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | (((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: ((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: ((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | ((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | ((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: (((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: (((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | (((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | (((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: ((((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: ((((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | ((((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | ((((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; }<br/>        )<span class="tsd-signature-symbol">[]</span><br/>        <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-symbol">{</span><br/>            <span class="tsd-signature-symbol">[</span><span class="tsd-kind-index-signature">key</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Lowercase</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">]:</span><br/>                <span class="tsd-signature-symbol">|</span> (<br/>                    ((((((((((((any | { [x: Lowercase&lt;string&gt;]: (any | any)[] | { [x: Lowercase&lt;string&gt;]: (any | any)[] | any | (any | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | (any | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: ((any | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: ((any | any)[] | any)[] | any | ((any | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | ((any | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: (((any | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: (((any | any)[] | any)[] | any)[] | any | (((any | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | (((any | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: ((((any | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: ((((any | any)[] | any)[] | any)[] | any)[] | any | ((((any | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | ((((any | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: (((((any | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: (((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any | (((((any | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | (((((any | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: ((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: ((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | ((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | ((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: (((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: (((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | (((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | (((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: ((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: ((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | ((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | ((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: (((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: (((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | (((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | (((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: ((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: ((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | ((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | ((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: (((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: (((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | (((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | (((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: ((((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: ((((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | ((((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | ((((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[]<br/>                )<br/>                <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-symbol">{</span><br/>                    <span class="tsd-signature-symbol">[</span><span class="tsd-kind-index-signature">key</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Lowercase</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">]:</span> ((((((((((((any | { [x: Lowercase&lt;string&gt;]: (any | any)[] | { [x: Lowercase&lt;string&gt;]: (any | any)[] | any | (any | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | (any | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: ((any | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: ((any | any)[] | any)[] | any | ((any | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | ((any | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: (((any | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: (((any | any)[] | any)[] | any)[] | any | (((any | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | (((any | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: ((((any | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: ((((any | any)[] | any)[] | any)[] | any)[] | any | ((((any | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | ((((any | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: (((((any | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: (((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any | (((((any | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | (((((any | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: ((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: ((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | ((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | ((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: (((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: (((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | (((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | (((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: ((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: ((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | ((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | ((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: (((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: (((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | (((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | (((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: ((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: ((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | ((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | ((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: (((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: (((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | (((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | (((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: ((((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: ((((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | ((((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | ((((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: ((((((((((((any | { [x: Lowercase&lt;string&gt;]: (any | any)[] | any | (any | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: ((any | any)[] | any)[] | any | ((any | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: (((any | any)[] | any)[] | any)[] | any | (((any | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: ((((any | any)[] | any)[] | any)[] | any)[] | any | ((((any | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: (((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any | (((((any | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: ((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | ((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: (((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | (((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: ((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | ((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: (((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | (((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: ((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | ((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: (((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | (((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: ((((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | ((((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | any | ((((((((((((any | { [x: Lowercase&lt;string&gt;]: (any | any)[] | any | (any | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: ((any | any)[] | any)[] | any | ((any | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: (((any | any)[] | any)[] | any)[] | any | (((any | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: ((((any | any)[] | any)[] | any)[] | any)[] | any | ((((any | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: (((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any | (((((any | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: ((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | ((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: (((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | (((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: ((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | ((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: (((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | (((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: ((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | ((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: (((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | (((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: ((((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | ((((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: any; }; } | ((((((((((((any | { [x: Lowercase&lt;string&gt;]: (any | any)[] | { [x: Lowercase&lt;string&gt;]: (any | any)[] | any | (any | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | (any | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: ((any | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: ((any | any)[] | any)[] | any | ((any | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | ((any | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: (((any | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: (((any | any)[] | any)[] | any)[] | any | (((any | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | (((any | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: ((((any | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: ((((any | any)[] | any)[] | any)[] | any)[] | any | ((((any | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | ((((any | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: (((((any | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: (((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any | (((((any | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | (((((any | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: ((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: ((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | ((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | ((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: (((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: (((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | (((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | (((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: ((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: ((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | ((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | ((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: (((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: (((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | (((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | (((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: ((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: ((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | ((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | ((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: (((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: (((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | (((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | (((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: ((((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: ((((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | ((((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | ((((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: any; }<span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-signature-symbol">}</span><br/>                <span class="tsd-signature-symbol">|</span> (<br/>                    (((((((((((any | { [x: Lowercase&lt;string&gt;]: (any | any)[] | { [x: Lowercase&lt;string&gt;]: (any | any)[] | any | (any | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | (any | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: ((any | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: ((any | any)[] | any)[] | any | ((any | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | ((any | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: (((any | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: (((any | any)[] | any)[] | any)[] | any | (((any | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | (((any | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: ((((any | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: ((((any | any)[] | any)[] | any)[] | any)[] | any | ((((any | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | ((((any | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: (((((any | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: (((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any | (((((any | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | (((((any | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: ((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: ((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | ((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | ((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: (((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: (((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | (((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | (((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: ((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: ((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | ((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | ((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: (((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: (((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | (((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | (((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: ((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: ((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | ((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | ((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: (((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: (((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | (((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | (((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: ((((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: ((((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | ((((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | ((((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; }<br/>                )<span class="tsd-signature-symbol">[]</span><br/>                <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-symbol">{</span> <span class="tsd-signature-symbol">[</span><span class="tsd-kind-index-signature">key</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Lowercase</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">]:</span> <span class="tsd-signature-type">any</span> <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-signature-symbol">}</span><br/>    )<span class="tsd-signature-symbol">[]</span><br/>    <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-symbol">{</span><br/>        <span class="tsd-signature-symbol">[</span><span class="tsd-kind-index-signature">key</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Lowercase</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">]:</span><br/>            <span class="tsd-signature-symbol">|</span> (<br/>                <span class="tsd-signature-symbol">|</span> (<br/>                    (((((((((((any | { [x: Lowercase&lt;string&gt;]: (any | any)[] | { [x: Lowercase&lt;string&gt;]: (any | any)[] | any | (any | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | (any | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: ((any | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: ((any | any)[] | any)[] | any | ((any | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | ((any | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: (((any | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: (((any | any)[] | any)[] | any)[] | any | (((any | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | (((any | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: ((((any | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: ((((any | any)[] | any)[] | any)[] | any)[] | any | ((((any | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | ((((any | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: (((((any | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: (((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any | (((((any | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | (((((any | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: ((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: ((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | ((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | ((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: (((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: (((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | (((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | (((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: ((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: ((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | ((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | ((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: (((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: (((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | (((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | (((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: ((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: ((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | ((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | ((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: (((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: (((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | (((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | (((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: ((((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: ((((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | ((((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | ((((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; }<br/>                )<span class="tsd-signature-symbol">[]</span><br/>                <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-symbol">{</span><br/>                    <span class="tsd-signature-symbol">[</span><span class="tsd-kind-index-signature">key</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Lowercase</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">]:</span> ((((((((((((any | { [x: Lowercase&lt;string&gt;]: (any | any)[] | { [x: Lowercase&lt;string&gt;]: (any | any)[] | any | (any | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | (any | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: ((any | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: ((any | any)[] | any)[] | any | ((any | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | ((any | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: (((any | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: (((any | any)[] | any)[] | any)[] | any | (((any | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | (((any | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: ((((any | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: ((((any | any)[] | any)[] | any)[] | any)[] | any | ((((any | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | ((((any | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: (((((any | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: (((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any | (((((any | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | (((((any | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: ((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: ((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | ((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | ((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: (((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: (((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | (((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | (((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: ((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: ((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | ((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | ((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: (((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: (((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | (((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | (((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: ((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: ((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | ((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | ((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: (((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: (((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | (((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | (((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: ((((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: ((((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | ((((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | ((((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: ((((((((((((any | { [x: Lowercase&lt;string&gt;]: (any | any)[] | any | (any | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: ((any | any)[] | any)[] | any | ((any | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: (((any | any)[] | any)[] | any)[] | any | (((any | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: ((((any | any)[] | any)[] | any)[] | any)[] | any | ((((any | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: (((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any | (((((any | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: ((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | ((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: (((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | (((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: ((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | ((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: (((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | (((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: ((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | ((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: (((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | (((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: ((((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | ((((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | any | ((((((((((((any | { [x: Lowercase&lt;string&gt;]: (any | any)[] | any | (any | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: ((any | any)[] | any)[] | any | ((any | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: (((any | any)[] | any)[] | any)[] | any | (((any | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: ((((any | any)[] | any)[] | any)[] | any)[] | any | ((((any | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: (((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any | (((((any | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: ((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | ((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: (((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | (((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: ((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | ((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: (((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | (((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: ((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | ((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: (((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | (((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: ((((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | ((((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: any; }; } | ((((((((((((any | { [x: Lowercase&lt;string&gt;]: (any | any)[] | { [x: Lowercase&lt;string&gt;]: (any | any)[] | any | (any | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | (any | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: ((any | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: ((any | any)[] | any)[] | any | ((any | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | ((any | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: (((any | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: (((any | any)[] | any)[] | any)[] | any | (((any | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | (((any | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: ((((any | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: ((((any | any)[] | any)[] | any)[] | any)[] | any | ((((any | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | ((((any | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: (((((any | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: (((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any | (((((any | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | (((((any | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: ((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: ((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | ((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | ((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: (((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: (((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | (((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | (((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: ((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: ((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | ((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | ((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: (((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: (((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | (((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | (((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: ((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: ((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | ((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | ((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: (((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: (((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | (((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | (((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: ((((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: ((((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | ((((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | ((((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: any; }<span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-signature-symbol">}</span><br/>            )<span class="tsd-signature-symbol">[]</span><br/>            <span class="tsd-signature-symbol">|</span> (<br/>                { [x: Lowercase&lt;string&gt;]: ((((((((((((any | { [x: Lowercase&lt;string&gt;]: (any | any)[] | any | (any | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: ((any | any)[] | any)[] | any | ((any | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: (((any | any)[] | any)[] | any)[] | any | (((any | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: ((((any | any)[] | any)[] | any)[] | any)[] | any | ((((any | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: (((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any | (((((any | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: ((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | ((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: (((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | (((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: ((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | ((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: (((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | (((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: ((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | ((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: (((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | (((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: ((((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | ((((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | any | ((((((((((((any | { [x: Lowercase&lt;string&gt;]: (any | any)[] | any | (any | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: ((any | any)[] | any)[] | any | ((any | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: (((any | any)[] | any)[] | any)[] | any | (((any | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: ((((any | any)[] | any)[] | any)[] | any)[] | any | ((((any | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: (((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any | (((((any | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: ((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | ((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: (((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | (((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: ((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | ((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: (((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | (((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: ((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | ((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: (((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | (((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: ((((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | ((((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: any; }; }<br/>            )<br/>            <span class="tsd-signature-symbol">|</span> (<br/>                <span class="tsd-signature-symbol">|</span> (<br/>                    (((((((((((any | { [x: Lowercase&lt;string&gt;]: (any | any)[] | { [x: Lowercase&lt;string&gt;]: (any | any)[] | any | (any | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | (any | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: ((any | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: ((any | any)[] | any)[] | any | ((any | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | ((any | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: (((any | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: (((any | any)[] | any)[] | any)[] | any | (((any | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | (((any | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: ((((any | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: ((((any | any)[] | any)[] | any)[] | any)[] | any | ((((any | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | ((((any | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: (((((any | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: (((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any | (((((any | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | (((((any | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: ((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: ((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | ((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | ((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: (((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: (((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | (((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | (((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: ((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: ((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | ((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | ((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: (((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: (((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | (((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | (((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: ((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: ((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | ((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | ((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: (((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: (((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | (((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | (((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: ((((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: ((((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | ((((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | ((((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; }<br/>                )<span class="tsd-signature-symbol">[]</span><br/>                <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-symbol">{</span><br/>                    <span class="tsd-signature-symbol">[</span><span class="tsd-kind-index-signature">key</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Lowercase</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">]:</span> ((((((((((((any | { [x: Lowercase&lt;string&gt;]: (any | any)[] | { [x: Lowercase&lt;string&gt;]: (any | any)[] | any | (any | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | (any | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: ((any | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: ((any | any)[] | any)[] | any | ((any | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | ((any | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: (((any | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: (((any | any)[] | any)[] | any)[] | any | (((any | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | (((any | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: ((((any | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: ((((any | any)[] | any)[] | any)[] | any)[] | any | ((((any | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | ((((any | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: (((((any | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: (((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any | (((((any | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | (((((any | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: ((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: ((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | ((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | ((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: (((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: (((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | (((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | (((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: ((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: ((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | ((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | ((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: (((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: (((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | (((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | (((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: ((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: ((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | ((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | ((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: (((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: (((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | (((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | (((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: ((((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: ((((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | ((((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | ((((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: ((((((((((((any | { [x: Lowercase&lt;string&gt;]: (any | any)[] | any | (any | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: ((any | any)[] | any)[] | any | ((any | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: (((any | any)[] | any)[] | any)[] | any | (((any | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: ((((any | any)[] | any)[] | any)[] | any)[] | any | ((((any | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: (((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any | (((((any | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: ((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | ((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: (((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | (((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: ((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | ((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: (((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | (((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: ((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | ((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: (((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | (((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: ((((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | ((((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | any | ((((((((((((any | { [x: Lowercase&lt;string&gt;]: (any | any)[] | any | (any | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: ((any | any)[] | any)[] | any | ((any | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: (((any | any)[] | any)[] | any)[] | any | (((any | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: ((((any | any)[] | any)[] | any)[] | any)[] | any | ((((any | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: (((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any | (((((any | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: ((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | ((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: (((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | (((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: ((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | ((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: (((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | (((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: ((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | ((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: (((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | (((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: ((((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | ((((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: any; }; } | ((((((((((((any | { [x: Lowercase&lt;string&gt;]: (any | any)[] | { [x: Lowercase&lt;string&gt;]: (any | any)[] | any | (any | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | (any | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: ((any | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: ((any | any)[] | any)[] | any | ((any | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | ((any | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: (((any | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: (((any | any)[] | any)[] | any)[] | any | (((any | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | (((any | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: ((((any | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: ((((any | any)[] | any)[] | any)[] | any)[] | any | ((((any | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | ((((any | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: (((((any | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: (((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any | (((((any | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | (((((any | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: ((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: ((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | ((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | ((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: (((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: (((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | (((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | (((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: ((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: ((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | ((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | ((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: (((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: (((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | (((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | (((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: ((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: ((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | ((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | ((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: (((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: (((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | (((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | (((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: ((((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: ((((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | ((((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | ((((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: any; }<span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-signature-symbol">}</span><br/>            )<span class="tsd-signature-symbol">[]</span><br/>            <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-symbol">{</span> <span class="tsd-signature-symbol">[</span><span class="tsd-kind-index-signature">key</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Lowercase</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">]:</span> <span class="tsd-signature-type">any</span> <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-signature-symbol">}</span><br/>    <span class="tsd-signature-symbol">|</span> (<br/>        <span class="tsd-signature-symbol">|</span> (<br/>            (((((((((((any | { [x: Lowercase&lt;string&gt;]: (any | any)[] | { [x: Lowercase&lt;string&gt;]: (any | any)[] | any | (any | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | (any | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: ((any | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: ((any | any)[] | any)[] | any | ((any | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | ((any | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: (((any | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: (((any | any)[] | any)[] | any)[] | any | (((any | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | (((any | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: ((((any | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: ((((any | any)[] | any)[] | any)[] | any)[] | any | ((((any | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | ((((any | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: (((((any | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: (((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any | (((((any | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | (((((any | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: ((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: ((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | ((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | ((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: (((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: (((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | (((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | (((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: ((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: ((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | ((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | ((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: (((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: (((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | (((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | (((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: ((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: ((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | ((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | ((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: (((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: (((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | (((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | (((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: ((((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: ((((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | ((((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | ((((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; }<br/>        )<span class="tsd-signature-symbol">[]</span><br/>        <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-symbol">{</span><br/>            <span class="tsd-signature-symbol">[</span><span class="tsd-kind-index-signature">key</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Lowercase</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">]:</span><br/>                <span class="tsd-signature-symbol">|</span> (<br/>                    (((((((((((any | { [x: Lowercase&lt;string&gt;]: (any | any)[] | { [x: Lowercase&lt;string&gt;]: (any | any)[] | any | (any | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | (any | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: ((any | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: ((any | any)[] | any)[] | any | ((any | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | ((any | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: (((any | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: (((any | any)[] | any)[] | any)[] | any | (((any | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | (((any | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: ((((any | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: ((((any | any)[] | any)[] | any)[] | any)[] | any | ((((any | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | ((((any | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: (((((any | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: (((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any | (((((any | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | (((((any | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: ((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: ((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | ((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | ((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: (((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: (((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | (((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | (((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: ((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: ((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | ((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | ((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: (((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: (((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | (((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | (((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: ((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: ((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | ((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | ((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: (((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: (((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | (((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | (((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: ((((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: ((((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | ((((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | ((((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; }<br/>                )<span class="tsd-signature-symbol">[]</span><br/>                <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-symbol">{</span><br/>                    <span class="tsd-signature-symbol">[</span><span class="tsd-kind-index-signature">key</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Lowercase</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">]:</span> ((((((((((((any | { [x: Lowercase&lt;string&gt;]: (any | any)[] | { [x: Lowercase&lt;string&gt;]: (any | any)[] | any | (any | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | (any | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: ((any | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: ((any | any)[] | any)[] | any | ((any | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | ((any | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: (((any | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: (((any | any)[] | any)[] | any)[] | any | (((any | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | (((any | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: ((((any | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: ((((any | any)[] | any)[] | any)[] | any)[] | any | ((((any | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | ((((any | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: (((((any | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: (((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any | (((((any | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | (((((any | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: ((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: ((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | ((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | ((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: (((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: (((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | (((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | (((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: ((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: ((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | ((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | ((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: (((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: (((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | (((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | (((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: ((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: ((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | ((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | ((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: (((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: (((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | (((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | (((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: ((((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: ((((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | ((((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | ((((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: ((((((((((((any | { [x: Lowercase&lt;string&gt;]: (any | any)[] | any | (any | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: ((any | any)[] | any)[] | any | ((any | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: (((any | any)[] | any)[] | any)[] | any | (((any | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: ((((any | any)[] | any)[] | any)[] | any)[] | any | ((((any | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: (((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any | (((((any | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: ((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | ((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: (((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | (((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: ((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | ((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: (((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | (((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: ((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | ((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: (((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | (((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: ((((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | ((((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | any | ((((((((((((any | { [x: Lowercase&lt;string&gt;]: (any | any)[] | any | (any | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: ((any | any)[] | any)[] | any | ((any | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: (((any | any)[] | any)[] | any)[] | any | (((any | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: ((((any | any)[] | any)[] | any)[] | any)[] | any | ((((any | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: (((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any | (((((any | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: ((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | ((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: (((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | (((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: ((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | ((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: (((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | (((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: ((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | ((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: (((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | (((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: ((((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | ((((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: any; }; } | ((((((((((((any | { [x: Lowercase&lt;string&gt;]: (any | any)[] | { [x: Lowercase&lt;string&gt;]: (any | any)[] | any | (any | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | (any | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: ((any | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: ((any | any)[] | any)[] | any | ((any | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | ((any | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: (((any | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: (((any | any)[] | any)[] | any)[] | any | (((any | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | (((any | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: ((((any | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: ((((any | any)[] | any)[] | any)[] | any)[] | any | ((((any | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | ((((any | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: (((((any | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: (((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any | (((((any | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | (((((any | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: ((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: ((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | ((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | ((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: (((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: (((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | (((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | (((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: ((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: ((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | ((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | ((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: (((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: (((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | (((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | (((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: ((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: ((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | ((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | ((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: (((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: (((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | (((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | (((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: ((((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: ((((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | ((((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | ((((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: any; }<span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-signature-symbol">}</span><br/>                <span class="tsd-signature-symbol">|</span> (<br/>                    ((((((((((((any | { [x: Lowercase&lt;string&gt;]: (any | any)[] | { [x: Lowercase&lt;string&gt;]: (any | any)[] | any | (any | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | (any | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: ((any | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: ((any | any)[] | any)[] | any | ((any | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | ((any | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: (((any | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: (((any | any)[] | any)[] | any)[] | any | (((any | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | (((any | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: ((((any | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: ((((any | any)[] | any)[] | any)[] | any)[] | any | ((((any | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | ((((any | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: (((((any | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: (((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any | (((((any | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | (((((any | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: ((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: ((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | ((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | ((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: (((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: (((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | (((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | (((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: ((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: ((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | ((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | ((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: (((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: (((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | (((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | (((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: ((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: ((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | ((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | ((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: (((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: (((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | (((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | (((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[] | { [x: Lowercase&lt;string&gt;]: ((((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: ((((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any | ((((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; } | ((((((((((((any | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | any)[] | { [x: Lowercase&lt;string&gt;]: any; }; })[]<br/>                )<br/>                <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-symbol">{</span> <span class="tsd-signature-symbol">[</span><span class="tsd-kind-index-signature">key</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Lowercase</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">]:</span> <span class="tsd-signature-type">any</span> <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-signature-symbol">}</span><br/>    )<span class="tsd-signature-symbol">[]</span><br/>    <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-symbol">{</span> <span class="tsd-signature-symbol">[</span><span class="tsd-kind-index-signature">key</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Lowercase</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">]:</span> <span class="tsd-signature-type">any</span> <span class="tsd-signature-symbol">}</span></div><aside class="tsd-sources"><p>Inherited from <a href="BaseInteraction.html">BaseInteraction</a>.<a href="BaseInteraction.html#data">data</a></p></aside></section><section class="tsd-panel tsd-member tsd-is-inherited"><h3 class="tsd-anchor-link" id="entitlements"><span>entitlements</span><a href="#entitlements" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">entitlements</span><span class="tsd-signature-symbol">:</span> <a href="Entitlement.html" class="tsd-signature-type tsd-kind-class">Entitlement</a><span class="tsd-signature-symbol">[]</span></div><aside class="tsd-sources"><p>Inherited from <a href="BaseInteraction.html">BaseInteraction</a>.<a href="BaseInteraction.html#entitlements">entitlements</a></p><ul><li>Defined in <a href="https://github.com/tiramisulabs/seyfert//blob/9ae4290ef220ffc96449d469d16d76b2031eca69/src/structures/Interaction.ts#L108">src/structures/Interaction.ts:108</a></li></ul></aside></section><section class="tsd-panel tsd-member tsd-is-inherited"><h3 class="tsd-anchor-link" id="guild"><code class="tsd-tag">Optional</code><span>guild</span><a href="#guild" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">guild</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-symbol">{</span> <span class="tsd-kind-property">features</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">GuildFeature</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">;</span> <span class="tsd-kind-property">id</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span> <span class="tsd-kind-property">locale</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Locale</span> <span class="tsd-signature-symbol">}</span></div><aside class="tsd-sources"><p>Inherited from <a href="BaseInteraction.html">BaseInteraction</a>.<a href="BaseInteraction.html#guild">guild</a></p></aside></section><section class="tsd-panel tsd-member tsd-is-inherited"><h3 class="tsd-anchor-link" id="guildid"><code class="tsd-tag">Optional</code><span>guild<wbr/>Id</span><a href="#guildid" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">guildId</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">string</span></div><aside class="tsd-sources"><p>Inherited from <a href="BaseInteraction.html">BaseInteraction</a>.<a href="BaseInteraction.html#guildid">guildId</a></p></aside></section><section class="tsd-panel tsd-member tsd-is-inherited"><h3 class="tsd-anchor-link" id="guildlocale"><code class="tsd-tag">Optional</code><span>guild<wbr/>Locale</span><a href="#guildlocale" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">guildLocale</span><span class="tsd-signature-symbol">?:</span><br/>    <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;id&quot;</span><br/>    <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;en-US&quot;</span><br/>    <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;en-GB&quot;</span><br/>    <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;bg&quot;</span><br/>    <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;zh-CN&quot;</span><br/>    <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;zh-TW&quot;</span><br/>    <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;hr&quot;</span><br/>    <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;cs&quot;</span><br/>    <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;da&quot;</span><br/>    <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;nl&quot;</span><br/>    <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;fi&quot;</span><br/>    <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;fr&quot;</span><br/>    <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;de&quot;</span><br/>    <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;el&quot;</span><br/>    <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;hi&quot;</span><br/>    <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;hu&quot;</span><br/>    <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;it&quot;</span><br/>    <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;ja&quot;</span><br/>    <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;ko&quot;</span><br/>    <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;lt&quot;</span><br/>    <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;no&quot;</span><br/>    <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;pl&quot;</span><br/>    <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;pt-BR&quot;</span><br/>    <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;ro&quot;</span><br/>    <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;ru&quot;</span><br/>    <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;es-ES&quot;</span><br/>    <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;es-419&quot;</span><br/>    <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;sv-SE&quot;</span><br/>    <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;th&quot;</span><br/>    <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;tr&quot;</span><br/>    <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;uk&quot;</span><br/>    <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;vi&quot;</span></div><aside class="tsd-sources"><p>Inherited from <a href="BaseInteraction.html">BaseInteraction</a>.<a href="BaseInteraction.html#guildlocale">guildLocale</a></p></aside></section><section class="tsd-panel tsd-member tsd-is-inherited"><h3 class="tsd-anchor-link" id="id"><span>id</span><a href="#id" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">id</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span></div><aside class="tsd-sources"><p>Inherited from <a href="BaseInteraction.html">BaseInteraction</a>.<a href="BaseInteraction.html#id">id</a></p><ul><li>Defined in <a href="https://github.com/tiramisulabs/seyfert//blob/9ae4290ef220ffc96449d469d16d76b2031eca69/src/structures/extra/DiscordBase.ts#L6">src/structures/extra/DiscordBase.ts:6</a></li></ul></aside></section><section class="tsd-panel tsd-member tsd-is-inherited"><h3 class="tsd-anchor-link" id="locale"><span>locale</span><a href="#locale" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">locale</span><span class="tsd-signature-symbol">:</span><br/>    <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;id&quot;</span><br/>    <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;en-US&quot;</span><br/>    <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;en-GB&quot;</span><br/>    <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;bg&quot;</span><br/>    <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;zh-CN&quot;</span><br/>    <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;zh-TW&quot;</span><br/>    <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;hr&quot;</span><br/>    <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;cs&quot;</span><br/>    <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;da&quot;</span><br/>    <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;nl&quot;</span><br/>    <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;fi&quot;</span><br/>    <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;fr&quot;</span><br/>    <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;de&quot;</span><br/>    <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;el&quot;</span><br/>    <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;hi&quot;</span><br/>    <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;hu&quot;</span><br/>    <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;it&quot;</span><br/>    <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;ja&quot;</span><br/>    <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;ko&quot;</span><br/>    <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;lt&quot;</span><br/>    <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;no&quot;</span><br/>    <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;pl&quot;</span><br/>    <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;pt-BR&quot;</span><br/>    <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;ro&quot;</span><br/>    <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;ru&quot;</span><br/>    <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;es-ES&quot;</span><br/>    <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;es-419&quot;</span><br/>    <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;sv-SE&quot;</span><br/>    <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;th&quot;</span><br/>    <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;tr&quot;</span><br/>    <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;uk&quot;</span><br/>    <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;vi&quot;</span></div><aside class="tsd-sources"><p>Inherited from <a href="BaseInteraction.html">BaseInteraction</a>.<a href="BaseInteraction.html#locale">locale</a></p></aside></section><section class="tsd-panel tsd-member tsd-is-inherited"><h3 class="tsd-anchor-link" id="member"><span>member</span><a href="#member" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">member</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">When</span><span class="tsd-signature-symbol">&lt;</span><a class="tsd-signature-type tsd-kind-type-parameter" href="#constructorinteractionfromguild">FromGuild</a><span class="tsd-signature-symbol">,</span> <a href="InteractionGuildMember.html" class="tsd-signature-type tsd-kind-class">InteractionGuildMember</a><span class="tsd-signature-symbol">,</span> <span class="tsd-signature-type">undefined</span><span class="tsd-signature-symbol">&gt;</span></div><aside class="tsd-sources"><p>Inherited from <a href="BaseInteraction.html">BaseInteraction</a>.<a href="BaseInteraction.html#member">member</a></p><ul><li>Defined in <a href="https://github.com/tiramisulabs/seyfert//blob/9ae4290ef220ffc96449d469d16d76b2031eca69/src/structures/Interaction.ts#L103">src/structures/Interaction.ts:103</a></li></ul></aside></section><section class="tsd-panel tsd-member tsd-is-inherited"><h3 class="tsd-anchor-link" id="message"><code class="tsd-tag">Optional</code><span>message</span><a href="#message" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">message</span><span class="tsd-signature-symbol">?:</span> <a href="Message.html" class="tsd-signature-type tsd-kind-class">Message</a></div><aside class="tsd-sources"><p>Inherited from <a href="BaseInteraction.html">BaseInteraction</a>.<a href="BaseInteraction.html#message">message</a></p><ul><li>Defined in <a href="https://github.com/tiramisulabs/seyfert//blob/9ae4290ef220ffc96449d469d16d76b2031eca69/src/structures/Interaction.ts#L105">src/structures/Interaction.ts:105</a></li></ul></aside></section><section class="tsd-panel tsd-member tsd-is-inherited"><h3 class="tsd-anchor-link" id="replied"><code class="tsd-tag">Optional</code><span>replied</span><a href="#replied" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">replied</span><span class="tsd-signature-symbol">?:</span><br/>    <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">boolean</span><br/>    <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">undefined</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">boolean</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">RESTPostAPIInteractionCallbackResult</span><span class="tsd-signature-symbol">&gt;</span></div><aside class="tsd-sources"><p>Inherited from <a href="BaseInteraction.html">BaseInteraction</a>.<a href="BaseInteraction.html#replied">replied</a></p><ul><li>Defined in <a href="https://github.com/tiramisulabs/seyfert//blob/9ae4290ef220ffc96449d469d16d76b2031eca69/src/structures/Interaction.ts#L106">src/structures/Interaction.ts:106</a></li></ul></aside></section><section class="tsd-panel tsd-member tsd-is-inherited"><h3 class="tsd-anchor-link" id="token"><span>token</span><a href="#token" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">token</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span></div><aside class="tsd-sources"><p>Inherited from <a href="BaseInteraction.html">BaseInteraction</a>.<a href="BaseInteraction.html#token">token</a></p></aside></section><section class="tsd-panel tsd-member tsd-is-inherited"><h3 class="tsd-anchor-link" id="user"><span>user</span><a href="#user" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">user</span><span class="tsd-signature-symbol">:</span> <a href="User.html" class="tsd-signature-type tsd-kind-class">User</a></div><aside class="tsd-sources"><p>Inherited from <a href="BaseInteraction.html">BaseInteraction</a>.<a href="BaseInteraction.html#user">user</a></p><ul><li>Defined in <a href="https://github.com/tiramisulabs/seyfert//blob/9ae4290ef220ffc96449d469d16d76b2031eca69/src/structures/Interaction.ts#L102">src/structures/Interaction.ts:102</a></li></ul></aside></section><section class="tsd-panel tsd-member tsd-is-inherited"><h3 class="tsd-anchor-link" id="version"><span>version</span><a href="#version" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">version</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">1</span></div><aside class="tsd-sources"><p>Inherited from <a href="BaseInteraction.html">BaseInteraction</a>.<a href="BaseInteraction.html#version">version</a></p></aside></section></section></details><details class="tsd-panel-group tsd-member-group tsd-accordion" open><summary class="tsd-accordion-summary" data-key="section-Accessors"><svg width="20" height="20" viewBox="0 0 24 24" fill="none" aria-hidden="true"><use href="../assets/icons.svg#icon-chevronDown"></use></svg><h2>Accessors</h2></summary><section><section class="tsd-panel tsd-member tsd-is-inherited"><h3 class="tsd-anchor-link" id="createdat"><span>created<wbr/>At</span><a href="#createdat" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><ul class="tsd-signatures tsd-is-inherited"><li><div class="tsd-signature" id="createdatcreatedat"><span class="tsd-signature-keyword">get</span> <span class="tsd-kind-get-signature">createdAt</span><span class="tsd-signature-symbol">()</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Date</span></div><div class="tsd-description"><div class="tsd-comment tsd-typography"><p>createdAt gets the creation Date instace of the current object.</p>
</div><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Date</span></h4><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><p>Inherited from BaseInteraction.createdAt</p><ul><li>Defined in <a href="https://github.com/tiramisulabs/seyfert//blob/9ae4290ef220ffc96449d469d16d76b2031eca69/src/structures/extra/DiscordBase.ts#L27">src/structures/extra/DiscordBase.ts:27</a></li></ul></aside></div></li></ul></section><section class="tsd-panel tsd-member tsd-is-inherited"><h3 class="tsd-anchor-link" id="createdtimestamp"><span>created<wbr/>Timestamp</span><a href="#createdtimestamp" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><ul class="tsd-signatures tsd-is-inherited"><li><div class="tsd-signature" id="createdtimestampcreatedtimestamp"><span class="tsd-signature-keyword">get</span> <span class="tsd-kind-get-signature">createdTimestamp</span><span class="tsd-signature-symbol">()</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">number</span></div><div class="tsd-description"><div class="tsd-comment tsd-typography"><p>Create a timestamp for the current object.</p>
</div><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">number</span></h4><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><p>Inherited from BaseInteraction.createdTimestamp</p><ul><li>Defined in <a href="https://github.com/tiramisulabs/seyfert//blob/9ae4290ef220ffc96449d469d16d76b2031eca69/src/structures/extra/DiscordBase.ts#L20">src/structures/extra/DiscordBase.ts:20</a></li></ul></aside></div></li></ul></section></section></details><details class="tsd-panel-group tsd-member-group tsd-accordion" open><summary class="tsd-accordion-summary" data-key="section-Methods"><svg width="20" height="20" viewBox="0 0 24 24" fill="none" aria-hidden="true"><use href="../assets/icons.svg#icon-chevronDown"></use></svg><h2>Methods</h2></summary><section><section class="tsd-panel tsd-member tsd-is-inherited"><h3 class="tsd-anchor-link" id="deferreply"><span>defer<wbr/>Reply</span><a href="#deferreply" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><ul class="tsd-signatures tsd-is-inherited"><li class="tsd-is-inherited"><div class="tsd-signature tsd-anchor-link" id="deferreply-1"><span class="tsd-kind-call-signature">deferReply</span><span class="tsd-signature-symbol">&lt;</span><a class="tsd-signature-type tsd-kind-type-parameter" href="#deferreplywr">WR</a> <span class="tsd-signature-keyword">extends</span> <span class="tsd-signature-type">boolean</span> <span class="tsd-signature-symbol">=</span> <span class="tsd-signature-type">false</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">(</span><br/>    <span class="tsd-kind-parameter">flags</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">MessageFlags</span><span class="tsd-signature-symbol">,</span><br/>    <span class="tsd-kind-parameter">withResponse</span><span class="tsd-signature-symbol">?:</span> <a class="tsd-signature-type tsd-kind-type-parameter" href="BaseInteraction.html#deferreplywr">WR</a><span class="tsd-signature-symbol">,</span><br/><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">When</span><span class="tsd-signature-symbol">&lt;</span><a class="tsd-signature-type tsd-kind-type-parameter" href="BaseInteraction.html#deferreplywr">WR</a><span class="tsd-signature-symbol">,</span> <a href="WebhookMessage.html" class="tsd-signature-type tsd-kind-class">WebhookMessage</a><span class="tsd-signature-symbol">,</span> <span class="tsd-signature-type">undefined</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">&gt;</span><a href="#deferreply-1" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></div><div class="tsd-description"><section class="tsd-panel"><h4>Type Parameters</h4><ul class="tsd-type-parameter-list"><li><span id="deferreplywr"><span class="tsd-kind-type-parameter">WR</span> <span class="tsd-signature-keyword">extends</span> <span class="tsd-signature-type">boolean</span> = <span class="tsd-signature-type">false</span></span></li></ul></section><div class="tsd-parameters"><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameter-list"><li><span><code class="tsd-tag">Optional</code><span class="tsd-kind-parameter">flags</span>: <span class="tsd-signature-type">MessageFlags</span></span></li><li><span><code class="tsd-tag">Optional</code><span class="tsd-kind-parameter">withResponse</span>: <a class="tsd-signature-type tsd-kind-type-parameter" href="BaseInteraction.html#deferreplywr">WR</a></span></li></ul></div><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">When</span><span class="tsd-signature-symbol">&lt;</span><a class="tsd-signature-type tsd-kind-type-parameter" href="BaseInteraction.html#deferreplywr">WR</a><span class="tsd-signature-symbol">,</span> <a href="WebhookMessage.html" class="tsd-signature-type tsd-kind-class">WebhookMessage</a><span class="tsd-signature-symbol">,</span> <span class="tsd-signature-type">undefined</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">&gt;</span></h4><aside class="tsd-sources"><p>Inherited from <a href="BaseInteraction.html">BaseInteraction</a>.<a href="BaseInteraction.html#deferreply">deferReply</a></p><ul><li>Defined in <a href="https://github.com/tiramisulabs/seyfert//blob/9ae4290ef220ffc96449d469d16d76b2031eca69/src/structures/Interaction.ts#L251">src/structures/Interaction.ts:251</a></li></ul></aside></div></li></ul></section><section class="tsd-panel tsd-member"><h3 class="tsd-anchor-link" id="deletemessage"><span>delete<wbr/>Message</span><a href="#deletemessage" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><ul class="tsd-signatures"><li class=""><div class="tsd-signature tsd-anchor-link" id="deletemessage-1"><span class="tsd-kind-call-signature">deleteMessage</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">messageId</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">void</span><span class="tsd-signature-symbol">&gt;</span><a href="#deletemessage-1" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></div><div class="tsd-description"><div class="tsd-parameters"><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameter-list"><li><span><span class="tsd-kind-parameter">messageId</span>: <span class="tsd-signature-type">string</span></span></li></ul></div><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">void</span><span class="tsd-signature-symbol">&gt;</span></h4><aside class="tsd-sources"><ul><li>Defined in <a href="https://github.com/tiramisulabs/seyfert//blob/9ae4290ef220ffc96449d469d16d76b2031eca69/src/structures/Interaction.ts#L536">src/structures/Interaction.ts:536</a></li></ul></aside></div></li></ul></section><section class="tsd-panel tsd-member"><h3 class="tsd-anchor-link" id="deleteresponse"><span>delete<wbr/>Response</span><a href="#deleteresponse" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><ul class="tsd-signatures"><li class=""><div class="tsd-signature tsd-anchor-link" id="deleteresponse-1"><span class="tsd-kind-call-signature">deleteResponse</span><span class="tsd-signature-symbol">()</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">void</span><span class="tsd-signature-symbol">&gt;</span><a href="#deleteresponse-1" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></div><div class="tsd-description"><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">void</span><span class="tsd-signature-symbol">&gt;</span></h4><aside class="tsd-sources"><ul><li>Defined in <a href="https://github.com/tiramisulabs/seyfert//blob/9ae4290ef220ffc96449d469d16d76b2031eca69/src/structures/Interaction.ts#L532">src/structures/Interaction.ts:532</a></li></ul></aside></div></li></ul></section><section class="tsd-panel tsd-member"><h3 class="tsd-anchor-link" id="editmessage"><span>edit<wbr/>Message</span><a href="#editmessage" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><ul class="tsd-signatures"><li class=""><div class="tsd-signature tsd-anchor-link" id="editmessage-1"><span class="tsd-kind-call-signature">editMessage</span><span class="tsd-signature-symbol">(</span><br/>    <span class="tsd-kind-parameter">messageId</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">,</span><br/>    <span class="tsd-kind-parameter">body</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">InteractionMessageUpdateBodyRequest</span><span class="tsd-signature-symbol">,</span><br/><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="WebhookMessage.html" class="tsd-signature-type tsd-kind-class">WebhookMessage</a><span class="tsd-signature-symbol">&gt;</span><a href="#editmessage-1" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></div><div class="tsd-description"><div class="tsd-parameters"><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameter-list"><li><span><span class="tsd-kind-parameter">messageId</span>: <span class="tsd-signature-type">string</span></span></li><li><span><span class="tsd-kind-parameter">body</span>: <span class="tsd-signature-type">InteractionMessageUpdateBodyRequest</span></span></li></ul></div><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="WebhookMessage.html" class="tsd-signature-type tsd-kind-class">WebhookMessage</a><span class="tsd-signature-symbol">&gt;</span></h4><aside class="tsd-sources"><ul><li>Defined in <a href="https://github.com/tiramisulabs/seyfert//blob/9ae4290ef220ffc96449d469d16d76b2031eca69/src/structures/Interaction.ts#L524">src/structures/Interaction.ts:524</a></li></ul></aside></div></li></ul></section><section class="tsd-panel tsd-member"><h3 class="tsd-anchor-link" id="editorreply"><span>edit<wbr/>Or<wbr/>Reply</span><a href="#editorreply" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><ul class="tsd-signatures"><li class=""><div class="tsd-signature tsd-anchor-link" id="editorreply-1"><span class="tsd-kind-call-signature">editOrReply</span><span class="tsd-signature-symbol">&lt;</span><a class="tsd-signature-type tsd-kind-type-parameter" href="#editorreplyfr">FR</a> <span class="tsd-signature-keyword">extends</span> <span class="tsd-signature-type">boolean</span> <span class="tsd-signature-symbol">=</span> <span class="tsd-signature-type">false</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">(</span><br/>    <span class="tsd-kind-parameter">body</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Omit</span><span class="tsd-signature-symbol">&lt;</span><br/>        <span class="tsd-signature-type">APIInteractionResponseCallbackData</span><span class="tsd-signature-symbol">,</span><br/>        <span class="tsd-signature-type">&quot;components&quot;</span><br/>        <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;content&quot;</span><br/>        <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;embeds&quot;</span><br/>        <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;poll&quot;</span><span class="tsd-signature-symbol">,</span><br/>    <span class="tsd-signature-symbol">&gt;</span> <span class="tsd-signature-symbol">&amp;</span> <span class="tsd-signature-type">SendResolverProps</span><span class="tsd-signature-symbol">,</span><br/>    <span class="tsd-kind-parameter">fetchReply</span><span class="tsd-signature-symbol">?:</span> <a class="tsd-signature-type tsd-kind-type-parameter" href="#editorreplyfr">FR</a><span class="tsd-signature-symbol">,</span><br/><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">When</span><span class="tsd-signature-symbol">&lt;</span><a class="tsd-signature-type tsd-kind-type-parameter" href="#editorreplyfr">FR</a><span class="tsd-signature-symbol">,</span> <a href="WebhookMessage.html" class="tsd-signature-type tsd-kind-class">WebhookMessage</a><span class="tsd-signature-symbol">,</span> <span class="tsd-signature-type">void</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">&gt;</span><a href="#editorreply-1" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></div><div class="tsd-description"><section class="tsd-panel"><h4>Type Parameters</h4><ul class="tsd-type-parameter-list"><li><span id="editorreplyfr"><span class="tsd-kind-type-parameter">FR</span> <span class="tsd-signature-keyword">extends</span> <span class="tsd-signature-type">boolean</span> = <span class="tsd-signature-type">false</span></span></li></ul></section><div class="tsd-parameters"><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameter-list"><li><span><span class="tsd-kind-parameter">body</span>: <span class="tsd-signature-type">Omit</span><span class="tsd-signature-symbol">&lt;</span><br/>    <span class="tsd-signature-type">APIInteractionResponseCallbackData</span><span class="tsd-signature-symbol">,</span><br/>    <span class="tsd-signature-type">&quot;components&quot;</span><br/>    <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;content&quot;</span><br/>    <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;embeds&quot;</span><br/>    <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;poll&quot;</span><span class="tsd-signature-symbol">,</span><br/><span class="tsd-signature-symbol">&gt;</span> <span class="tsd-signature-symbol">&amp;</span> <span class="tsd-signature-type">SendResolverProps</span></span></li><li><span><code class="tsd-tag">Optional</code><span class="tsd-kind-parameter">fetchReply</span>: <a class="tsd-signature-type tsd-kind-type-parameter" href="#editorreplyfr">FR</a></span></li></ul></div><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">When</span><span class="tsd-signature-symbol">&lt;</span><a class="tsd-signature-type tsd-kind-type-parameter" href="#editorreplyfr">FR</a><span class="tsd-signature-symbol">,</span> <a href="WebhookMessage.html" class="tsd-signature-type tsd-kind-class">WebhookMessage</a><span class="tsd-signature-symbol">,</span> <span class="tsd-signature-type">void</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">&gt;</span></h4><aside class="tsd-sources"><ul><li>Defined in <a href="https://github.com/tiramisulabs/seyfert//blob/9ae4290ef220ffc96449d469d16d76b2031eca69/src/structures/Interaction.ts#L509">src/structures/Interaction.ts:509</a></li></ul></aside></div></li></ul></section><section class="tsd-panel tsd-member"><h3 class="tsd-anchor-link" id="editresponse"><span>edit<wbr/>Response</span><a href="#editresponse" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><ul class="tsd-signatures"><li class=""><div class="tsd-signature tsd-anchor-link" id="editresponse-1"><span class="tsd-kind-call-signature">editResponse</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">body</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">InteractionMessageUpdateBodyRequest</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="WebhookMessage.html" class="tsd-signature-type tsd-kind-class">WebhookMessage</a><span class="tsd-signature-symbol">&gt;</span><a href="#editresponse-1" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></div><div class="tsd-description"><div class="tsd-parameters"><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameter-list"><li><span><span class="tsd-kind-parameter">body</span>: <span class="tsd-signature-type">InteractionMessageUpdateBodyRequest</span></span></li></ul></div><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="WebhookMessage.html" class="tsd-signature-type tsd-kind-class">WebhookMessage</a><span class="tsd-signature-symbol">&gt;</span></h4><aside class="tsd-sources"><ul><li>Defined in <a href="https://github.com/tiramisulabs/seyfert//blob/9ae4290ef220ffc96449d469d16d76b2031eca69/src/structures/Interaction.ts#L528">src/structures/Interaction.ts:528</a></li></ul></aside></div></li></ul></section><section class="tsd-panel tsd-member tsd-is-inherited"><h3 class="tsd-anchor-link" id="fetchguild"><span>fetch<wbr/>Guild</span><a href="#fetchguild" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><ul class="tsd-signatures tsd-is-inherited"><li class="tsd-is-inherited"><div class="tsd-signature tsd-anchor-link" id="fetchguild-1"><span class="tsd-kind-call-signature">fetchGuild</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">mode</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">&quot;rest&quot;</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;flow&quot;</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">undefined</span> <span class="tsd-signature-symbol">|</span> <a href="Guild.html" class="tsd-signature-type tsd-kind-class">Guild</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">&quot;cached&quot;</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;api&quot;</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">&gt;</span><a href="#fetchguild-1" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></div><div class="tsd-description"><div class="tsd-parameters"><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameter-list"><li><span><code class="tsd-tag">Optional</code><span class="tsd-kind-parameter">mode</span>: <span class="tsd-signature-type">&quot;rest&quot;</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;flow&quot;</span></span></li></ul></div><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">undefined</span> <span class="tsd-signature-symbol">|</span> <a href="Guild.html" class="tsd-signature-type tsd-kind-class">Guild</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">&quot;cached&quot;</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;api&quot;</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">&gt;</span></h4><aside class="tsd-sources"><p>Inherited from <a href="BaseInteraction.html">BaseInteraction</a>.<a href="BaseInteraction.html#fetchguild">fetchGuild</a></p><ul><li>Defined in <a href="https://github.com/tiramisulabs/seyfert//blob/9ae4290ef220ffc96449d469d16d76b2031eca69/src/structures/Interaction.ts#L369">src/structures/Interaction.ts:369</a></li></ul></aside></div></li><li class="tsd-is-inherited"><div class="tsd-signature tsd-anchor-link" id="fetchguild-2"><span class="tsd-kind-call-signature">fetchGuild</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">mode</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">&quot;cache&quot;</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <a href="../types/ReturnCache.html" class="tsd-signature-type tsd-kind-type-alias">ReturnCache</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">undefined</span> <span class="tsd-signature-symbol">|</span> <a href="Guild.html" class="tsd-signature-type tsd-kind-class">Guild</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">&quot;cached&quot;</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">&gt;</span><a href="#fetchguild-2" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></div><div class="tsd-description"><div class="tsd-parameters"><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameter-list"><li><span><span class="tsd-kind-parameter">mode</span>: <span class="tsd-signature-type">&quot;cache&quot;</span></span></li></ul></div><h4 class="tsd-returns-title">Returns <a href="../types/ReturnCache.html" class="tsd-signature-type tsd-kind-type-alias">ReturnCache</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">undefined</span> <span class="tsd-signature-symbol">|</span> <a href="Guild.html" class="tsd-signature-type tsd-kind-class">Guild</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">&quot;cached&quot;</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">&gt;</span></h4><aside class="tsd-sources"><p>Inherited from <a href="BaseInteraction.html">BaseInteraction</a>.<a href="BaseInteraction.html#fetchguild">fetchGuild</a></p><ul><li>Defined in <a href="https://github.com/tiramisulabs/seyfert//blob/9ae4290ef220ffc96449d469d16d76b2031eca69/src/structures/Interaction.ts#L370">src/structures/Interaction.ts:370</a></li></ul></aside></div></li></ul></section><section class="tsd-panel tsd-member"><h3 class="tsd-anchor-link" id="fetchmessage"><span>fetch<wbr/>Message</span><a href="#fetchmessage" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><ul class="tsd-signatures"><li class=""><div class="tsd-signature tsd-anchor-link" id="fetchmessage-1"><span class="tsd-kind-call-signature">fetchMessage</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">messageId</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="WebhookMessage.html" class="tsd-signature-type tsd-kind-class">WebhookMessage</a><span class="tsd-signature-symbol">&gt;</span><a href="#fetchmessage-1" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></div><div class="tsd-description"><div class="tsd-parameters"><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameter-list"><li><span><span class="tsd-kind-parameter">messageId</span>: <span class="tsd-signature-type">string</span></span></li></ul></div><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="WebhookMessage.html" class="tsd-signature-type tsd-kind-class">WebhookMessage</a><span class="tsd-signature-symbol">&gt;</span></h4><aside class="tsd-sources"><ul><li>Defined in <a href="https://github.com/tiramisulabs/seyfert//blob/9ae4290ef220ffc96449d469d16d76b2031eca69/src/structures/Interaction.ts#L455">src/structures/Interaction.ts:455</a></li></ul></aside></div></li></ul></section><section class="tsd-panel tsd-member"><h3 class="tsd-anchor-link" id="fetchresponse"><span>fetch<wbr/>Response</span><a href="#fetchresponse" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><ul class="tsd-signatures"><li class=""><div class="tsd-signature tsd-anchor-link" id="fetchresponse-1"><span class="tsd-kind-call-signature">fetchResponse</span><span class="tsd-signature-symbol">()</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="WebhookMessage.html" class="tsd-signature-type tsd-kind-class">WebhookMessage</a><span class="tsd-signature-symbol">&gt;</span><a href="#fetchresponse-1" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></div><div class="tsd-description"><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="WebhookMessage.html" class="tsd-signature-type tsd-kind-class">WebhookMessage</a><span class="tsd-signature-symbol">&gt;</span></h4><aside class="tsd-sources"><ul><li>Defined in <a href="https://github.com/tiramisulabs/seyfert//blob/9ae4290ef220ffc96449d469d16d76b2031eca69/src/structures/Interaction.ts#L459">src/structures/Interaction.ts:459</a></li></ul></aside></div></li></ul></section><section class="tsd-panel tsd-member"><h3 class="tsd-anchor-link" id="followup"><span>followup</span><a href="#followup" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><ul class="tsd-signatures"><li class=""><div class="tsd-signature tsd-anchor-link" id="followup-1"><span class="tsd-kind-call-signature">followup</span><span class="tsd-signature-symbol">(</span><br/>    <span class="tsd-kind-parameter">body</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Omit</span><span class="tsd-signature-symbol">&lt;</span><br/>        <span class="tsd-signature-type">RESTPostAPIWebhookWithTokenJSONBody</span><span class="tsd-signature-symbol">,</span><br/>        <span class="tsd-signature-type">&quot;components&quot;</span><br/>        <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;content&quot;</span><br/>        <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;embeds&quot;</span><br/>        <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;poll&quot;</span><span class="tsd-signature-symbol">,</span><br/>    <span class="tsd-signature-symbol">&gt;</span> <span class="tsd-signature-symbol">&amp;</span> <span class="tsd-signature-type">SendResolverProps</span><span class="tsd-signature-symbol">,</span><br/><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="WebhookMessage.html" class="tsd-signature-type tsd-kind-class">WebhookMessage</a><span class="tsd-signature-symbol">&gt;</span><a href="#followup-1" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></div><div class="tsd-description"><div class="tsd-parameters"><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameter-list"><li><span><span class="tsd-kind-parameter">body</span>: <span class="tsd-signature-type">Omit</span><span class="tsd-signature-symbol">&lt;</span><br/>    <span class="tsd-signature-type">RESTPostAPIWebhookWithTokenJSONBody</span><span class="tsd-signature-symbol">,</span><br/>    <span class="tsd-signature-type">&quot;components&quot;</span><br/>    <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;content&quot;</span><br/>    <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;embeds&quot;</span><br/>    <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;poll&quot;</span><span class="tsd-signature-symbol">,</span><br/><span class="tsd-signature-symbol">&gt;</span> <span class="tsd-signature-symbol">&amp;</span> <span class="tsd-signature-type">SendResolverProps</span></span></li></ul></div><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="WebhookMessage.html" class="tsd-signature-type tsd-kind-class">WebhookMessage</a><span class="tsd-signature-symbol">&gt;</span></h4><aside class="tsd-sources"><ul><li>Defined in <a href="https://github.com/tiramisulabs/seyfert//blob/9ae4290ef220ffc96449d469d16d76b2031eca69/src/structures/Interaction.ts#L540">src/structures/Interaction.ts:540</a></li></ul></aside></div></li></ul></section><section class="tsd-panel tsd-member tsd-is-inherited"><h3 class="tsd-anchor-link" id="isautocomplete"><span>is<wbr/>Autocomplete</span><a href="#isautocomplete" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><ul class="tsd-signatures tsd-is-inherited"><li class="tsd-is-inherited"><div class="tsd-signature tsd-anchor-link" id="isautocomplete-1"><span class="tsd-kind-call-signature">isAutocomplete</span><span class="tsd-signature-symbol">()</span><span class="tsd-signature-symbol">:</span> <span class="tsd-kind-parameter">this</span> <span class="tsd-signature-keyword">is</span> <a href="AutocompleteInteraction.html" class="tsd-signature-type tsd-kind-class">AutocompleteInteraction</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">&gt;</span><a href="#isautocomplete-1" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></div><div class="tsd-description"><h4 class="tsd-returns-title">Returns <span class="tsd-kind-parameter">this</span> <span class="tsd-signature-keyword">is</span> <a href="AutocompleteInteraction.html" class="tsd-signature-type tsd-kind-class">AutocompleteInteraction</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">&gt;</span></h4><aside class="tsd-sources"><p>Inherited from <a href="BaseInteraction.html">BaseInteraction</a>.<a href="BaseInteraction.html#isautocomplete">isAutocomplete</a></p><ul><li>Defined in <a href="https://github.com/tiramisulabs/seyfert//blob/9ae4290ef220ffc96449d469d16d76b2031eca69/src/structures/Interaction.ts#L302">src/structures/Interaction.ts:302</a></li></ul></aside></div></li></ul></section><section class="tsd-panel tsd-member tsd-is-inherited"><h3 class="tsd-anchor-link" id="isbutton"><span>is<wbr/>Button</span><a href="#isbutton" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><ul class="tsd-signatures tsd-is-inherited"><li class="tsd-is-inherited"><div class="tsd-signature tsd-anchor-link" id="isbutton-1"><span class="tsd-kind-call-signature">isButton</span><span class="tsd-signature-symbol">()</span><span class="tsd-signature-symbol">:</span> <span class="tsd-kind-parameter">this</span> <span class="tsd-signature-keyword">is</span> <a href="ButtonInteraction.html" class="tsd-signature-type tsd-kind-class">ButtonInteraction</a><a href="#isbutton-1" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></div><div class="tsd-description"><h4 class="tsd-returns-title">Returns <span class="tsd-kind-parameter">this</span> <span class="tsd-signature-keyword">is</span> <a href="ButtonInteraction.html" class="tsd-signature-type tsd-kind-class">ButtonInteraction</a></h4><aside class="tsd-sources"><p>Inherited from <a href="BaseInteraction.html">BaseInteraction</a>.<a href="BaseInteraction.html#isbutton">isButton</a></p><ul><li>Defined in <a href="https://github.com/tiramisulabs/seyfert//blob/9ae4290ef220ffc96449d469d16d76b2031eca69/src/structures/Interaction.ts#L266">src/structures/Interaction.ts:266</a></li></ul></aside></div></li></ul></section><section class="tsd-panel tsd-member tsd-is-inherited"><h3 class="tsd-anchor-link" id="ischannelselectmenu"><span>is<wbr/>Channel<wbr/>Select<wbr/>Menu</span><a href="#ischannelselectmenu" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><ul class="tsd-signatures tsd-is-inherited"><li class="tsd-is-inherited"><div class="tsd-signature tsd-anchor-link" id="ischannelselectmenu-1"><span class="tsd-kind-call-signature">isChannelSelectMenu</span><span class="tsd-signature-symbol">()</span><span class="tsd-signature-symbol">:</span> <span class="tsd-kind-parameter">this</span> <span class="tsd-signature-keyword">is</span> <a href="ChannelSelectMenuInteraction.html" class="tsd-signature-type tsd-kind-class">ChannelSelectMenuInteraction</a><a href="#ischannelselectmenu-1" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></div><div class="tsd-description"><h4 class="tsd-returns-title">Returns <span class="tsd-kind-parameter">this</span> <span class="tsd-signature-keyword">is</span> <a href="ChannelSelectMenuInteraction.html" class="tsd-signature-type tsd-kind-class">ChannelSelectMenuInteraction</a></h4><aside class="tsd-sources"><p>Inherited from <a href="BaseInteraction.html">BaseInteraction</a>.<a href="BaseInteraction.html#ischannelselectmenu">isChannelSelectMenu</a></p><ul><li>Defined in <a href="https://github.com/tiramisulabs/seyfert//blob/9ae4290ef220ffc96449d469d16d76b2031eca69/src/structures/Interaction.ts#L270">src/structures/Interaction.ts:270</a></li></ul></aside></div></li></ul></section><section class="tsd-panel tsd-member tsd-is-inherited"><h3 class="tsd-anchor-link" id="ischatinput"><span>is<wbr/>Chat<wbr/>Input</span><a href="#ischatinput" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><ul class="tsd-signatures tsd-is-inherited"><li class="tsd-is-inherited"><div class="tsd-signature tsd-anchor-link" id="ischatinput-1"><span class="tsd-kind-call-signature">isChatInput</span><span class="tsd-signature-symbol">()</span><span class="tsd-signature-symbol">:</span> <span class="tsd-kind-parameter">this</span> <span class="tsd-signature-keyword">is</span> <a href="ChatInputCommandInteraction.html" class="tsd-signature-type tsd-kind-class">ChatInputCommandInteraction</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">&gt;</span><a href="#ischatinput-1" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></div><div class="tsd-description"><h4 class="tsd-returns-title">Returns <span class="tsd-kind-parameter">this</span> <span class="tsd-signature-keyword">is</span> <a href="ChatInputCommandInteraction.html" class="tsd-signature-type tsd-kind-class">ChatInputCommandInteraction</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">&gt;</span></h4><aside class="tsd-sources"><p>Inherited from <a href="BaseInteraction.html">BaseInteraction</a>.<a href="BaseInteraction.html#ischatinput">isChatInput</a></p><ul><li>Defined in <a href="https://github.com/tiramisulabs/seyfert//blob/9ae4290ef220ffc96449d469d16d76b2031eca69/src/structures/Interaction.ts#L290">src/structures/Interaction.ts:290</a></li></ul></aside></div></li></ul></section><section class="tsd-panel tsd-member tsd-is-inherited"><h3 class="tsd-anchor-link" id="isentrypoint"><span>is<wbr/>Entry<wbr/>Point</span><a href="#isentrypoint" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><ul class="tsd-signatures tsd-is-inherited"><li class="tsd-is-inherited"><div class="tsd-signature tsd-anchor-link" id="isentrypoint-1"><span class="tsd-kind-call-signature">isEntryPoint</span><span class="tsd-signature-symbol">()</span><span class="tsd-signature-symbol">:</span> <span class="tsd-kind-parameter">this</span> <span class="tsd-signature-keyword">is</span> <a href="EntryPointInteraction.html" class="tsd-signature-type tsd-kind-class">EntryPointInteraction</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">&gt;</span><a href="#isentrypoint-1" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></div><div class="tsd-description"><h4 class="tsd-returns-title">Returns <span class="tsd-kind-parameter">this</span> <span class="tsd-signature-keyword">is</span> <a href="EntryPointInteraction.html" class="tsd-signature-type tsd-kind-class">EntryPointInteraction</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">&gt;</span></h4><aside class="tsd-sources"><p>Inherited from <a href="BaseInteraction.html">BaseInteraction</a>.<a href="BaseInteraction.html#isentrypoint">isEntryPoint</a></p><ul><li>Defined in <a href="https://github.com/tiramisulabs/seyfert//blob/9ae4290ef220ffc96449d469d16d76b2031eca69/src/structures/Interaction.ts#L310">src/structures/Interaction.ts:310</a></li></ul></aside></div></li></ul></section><section class="tsd-panel tsd-member tsd-is-inherited"><h3 class="tsd-anchor-link" id="ismentionableselectmenu"><span>is<wbr/>Mentionable<wbr/>Select<wbr/>Menu</span><a href="#ismentionableselectmenu" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><ul class="tsd-signatures tsd-is-inherited"><li class="tsd-is-inherited"><div class="tsd-signature tsd-anchor-link" id="ismentionableselectmenu-1"><span class="tsd-kind-call-signature">isMentionableSelectMenu</span><span class="tsd-signature-symbol">()</span><span class="tsd-signature-symbol">:</span> <span class="tsd-kind-parameter">this</span> <span class="tsd-signature-keyword">is</span> <a href="MentionableSelectMenuInteraction.html" class="tsd-signature-type tsd-kind-class">MentionableSelectMenuInteraction</a><a href="#ismentionableselectmenu-1" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></div><div class="tsd-description"><h4 class="tsd-returns-title">Returns <span class="tsd-kind-parameter">this</span> <span class="tsd-signature-keyword">is</span> <a href="MentionableSelectMenuInteraction.html" class="tsd-signature-type tsd-kind-class">MentionableSelectMenuInteraction</a></h4><aside class="tsd-sources"><p>Inherited from <a href="BaseInteraction.html">BaseInteraction</a>.<a href="BaseInteraction.html#ismentionableselectmenu">isMentionableSelectMenu</a></p><ul><li>Defined in <a href="https://github.com/tiramisulabs/seyfert//blob/9ae4290ef220ffc96449d469d16d76b2031eca69/src/structures/Interaction.ts#L278">src/structures/Interaction.ts:278</a></li></ul></aside></div></li></ul></section><section class="tsd-panel tsd-member tsd-is-inherited"><h3 class="tsd-anchor-link" id="ismessage"><span>is<wbr/>Message</span><a href="#ismessage" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><ul class="tsd-signatures tsd-is-inherited"><li class="tsd-is-inherited"><div class="tsd-signature tsd-anchor-link" id="ismessage-1"><span class="tsd-kind-call-signature">isMessage</span><span class="tsd-signature-symbol">()</span><span class="tsd-signature-symbol">:</span> <span class="tsd-kind-parameter">this</span> <span class="tsd-signature-keyword">is</span> <a href="MessageCommandInteraction.html" class="tsd-signature-type tsd-kind-class">MessageCommandInteraction</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">&gt;</span><a href="#ismessage-1" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></div><div class="tsd-description"><h4 class="tsd-returns-title">Returns <span class="tsd-kind-parameter">this</span> <span class="tsd-signature-keyword">is</span> <a href="MessageCommandInteraction.html" class="tsd-signature-type tsd-kind-class">MessageCommandInteraction</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">&gt;</span></h4><aside class="tsd-sources"><p>Inherited from <a href="BaseInteraction.html">BaseInteraction</a>.<a href="BaseInteraction.html#ismessage">isMessage</a></p><ul><li>Defined in <a href="https://github.com/tiramisulabs/seyfert//blob/9ae4290ef220ffc96449d469d16d76b2031eca69/src/structures/Interaction.ts#L298">src/structures/Interaction.ts:298</a></li></ul></aside></div></li></ul></section><section class="tsd-panel tsd-member tsd-is-inherited"><h3 class="tsd-anchor-link" id="ismodal"><span>is<wbr/>Modal</span><a href="#ismodal" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><ul class="tsd-signatures tsd-is-inherited"><li class="tsd-is-inherited"><div class="tsd-signature tsd-anchor-link" id="ismodal-1"><span class="tsd-kind-call-signature">isModal</span><span class="tsd-signature-symbol">()</span><span class="tsd-signature-symbol">:</span> <span class="tsd-kind-parameter">this</span> <span class="tsd-signature-keyword">is</span> <a href="ModalSubmitInteraction.html" class="tsd-signature-type tsd-kind-class">ModalSubmitInteraction</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">&gt;</span><a href="#ismodal-1" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></div><div class="tsd-description"><h4 class="tsd-returns-title">Returns <span class="tsd-kind-parameter">this</span> <span class="tsd-signature-keyword">is</span> <a href="ModalSubmitInteraction.html" class="tsd-signature-type tsd-kind-class">ModalSubmitInteraction</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">&gt;</span></h4><aside class="tsd-sources"><p>Inherited from <a href="BaseInteraction.html">BaseInteraction</a>.<a href="BaseInteraction.html#ismodal">isModal</a></p><ul><li>Defined in <a href="https://github.com/tiramisulabs/seyfert//blob/9ae4290ef220ffc96449d469d16d76b2031eca69/src/structures/Interaction.ts#L306">src/structures/Interaction.ts:306</a></li></ul></aside></div></li></ul></section><section class="tsd-panel tsd-member tsd-is-inherited"><h3 class="tsd-anchor-link" id="isroleselectmenu"><span>is<wbr/>Role<wbr/>Select<wbr/>Menu</span><a href="#isroleselectmenu" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><ul class="tsd-signatures tsd-is-inherited"><li class="tsd-is-inherited"><div class="tsd-signature tsd-anchor-link" id="isroleselectmenu-1"><span class="tsd-kind-call-signature">isRoleSelectMenu</span><span class="tsd-signature-symbol">()</span><span class="tsd-signature-symbol">:</span> <span class="tsd-kind-parameter">this</span> <span class="tsd-signature-keyword">is</span> <a href="RoleSelectMenuInteraction.html" class="tsd-signature-type tsd-kind-class">RoleSelectMenuInteraction</a><a href="#isroleselectmenu-1" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></div><div class="tsd-description"><h4 class="tsd-returns-title">Returns <span class="tsd-kind-parameter">this</span> <span class="tsd-signature-keyword">is</span> <a href="RoleSelectMenuInteraction.html" class="tsd-signature-type tsd-kind-class">RoleSelectMenuInteraction</a></h4><aside class="tsd-sources"><p>Inherited from <a href="BaseInteraction.html">BaseInteraction</a>.<a href="BaseInteraction.html#isroleselectmenu">isRoleSelectMenu</a></p><ul><li>Defined in <a href="https://github.com/tiramisulabs/seyfert//blob/9ae4290ef220ffc96449d469d16d76b2031eca69/src/structures/Interaction.ts#L274">src/structures/Interaction.ts:274</a></li></ul></aside></div></li></ul></section><section class="tsd-panel tsd-member tsd-is-inherited"><h3 class="tsd-anchor-link" id="isstringselectmenu"><span>is<wbr/>String<wbr/>Select<wbr/>Menu</span><a href="#isstringselectmenu" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><ul class="tsd-signatures tsd-is-inherited"><li class="tsd-is-inherited"><div class="tsd-signature tsd-anchor-link" id="isstringselectmenu-1"><span class="tsd-kind-call-signature">isStringSelectMenu</span><span class="tsd-signature-symbol">()</span><span class="tsd-signature-symbol">:</span> <span class="tsd-kind-parameter">this</span> <span class="tsd-signature-keyword">is</span> <a href="StringSelectMenuInteraction.html" class="tsd-signature-type tsd-kind-class">StringSelectMenuInteraction</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">&gt;</span><a href="#isstringselectmenu-1" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></div><div class="tsd-description"><h4 class="tsd-returns-title">Returns <span class="tsd-kind-parameter">this</span> <span class="tsd-signature-keyword">is</span> <a href="StringSelectMenuInteraction.html" class="tsd-signature-type tsd-kind-class">StringSelectMenuInteraction</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">&gt;</span></h4><aside class="tsd-sources"><p>Inherited from <a href="BaseInteraction.html">BaseInteraction</a>.<a href="BaseInteraction.html#isstringselectmenu">isStringSelectMenu</a></p><ul><li>Defined in <a href="https://github.com/tiramisulabs/seyfert//blob/9ae4290ef220ffc96449d469d16d76b2031eca69/src/structures/Interaction.ts#L286">src/structures/Interaction.ts:286</a></li></ul></aside></div></li></ul></section><section class="tsd-panel tsd-member tsd-is-inherited"><h3 class="tsd-anchor-link" id="isuser"><span>is<wbr/>User</span><a href="#isuser" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><ul class="tsd-signatures tsd-is-inherited"><li class="tsd-is-inherited"><div class="tsd-signature tsd-anchor-link" id="isuser-1"><span class="tsd-kind-call-signature">isUser</span><span class="tsd-signature-symbol">()</span><span class="tsd-signature-symbol">:</span> <span class="tsd-kind-parameter">this</span> <span class="tsd-signature-keyword">is</span> <a href="UserCommandInteraction.html" class="tsd-signature-type tsd-kind-class">UserCommandInteraction</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">&gt;</span><a href="#isuser-1" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></div><div class="tsd-description"><h4 class="tsd-returns-title">Returns <span class="tsd-kind-parameter">this</span> <span class="tsd-signature-keyword">is</span> <a href="UserCommandInteraction.html" class="tsd-signature-type tsd-kind-class">UserCommandInteraction</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">&gt;</span></h4><aside class="tsd-sources"><p>Inherited from <a href="BaseInteraction.html">BaseInteraction</a>.<a href="BaseInteraction.html#isuser">isUser</a></p><ul><li>Defined in <a href="https://github.com/tiramisulabs/seyfert//blob/9ae4290ef220ffc96449d469d16d76b2031eca69/src/structures/Interaction.ts#L294">src/structures/Interaction.ts:294</a></li></ul></aside></div></li></ul></section><section class="tsd-panel tsd-member tsd-is-inherited"><h3 class="tsd-anchor-link" id="isuserselectmenu"><span>is<wbr/>User<wbr/>Select<wbr/>Menu</span><a href="#isuserselectmenu" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><ul class="tsd-signatures tsd-is-inherited"><li class="tsd-is-inherited"><div class="tsd-signature tsd-anchor-link" id="isuserselectmenu-1"><span class="tsd-kind-call-signature">isUserSelectMenu</span><span class="tsd-signature-symbol">()</span><span class="tsd-signature-symbol">:</span> <span class="tsd-kind-parameter">this</span> <span class="tsd-signature-keyword">is</span> <a href="UserSelectMenuInteraction.html" class="tsd-signature-type tsd-kind-class">UserSelectMenuInteraction</a><a href="#isuserselectmenu-1" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></div><div class="tsd-description"><h4 class="tsd-returns-title">Returns <span class="tsd-kind-parameter">this</span> <span class="tsd-signature-keyword">is</span> <a href="UserSelectMenuInteraction.html" class="tsd-signature-type tsd-kind-class">UserSelectMenuInteraction</a></h4><aside class="tsd-sources"><p>Inherited from <a href="BaseInteraction.html">BaseInteraction</a>.<a href="BaseInteraction.html#isuserselectmenu">isUserSelectMenu</a></p><ul><li>Defined in <a href="https://github.com/tiramisulabs/seyfert//blob/9ae4290ef220ffc96449d469d16d76b2031eca69/src/structures/Interaction.ts#L282">src/structures/Interaction.ts:282</a></li></ul></aside></div></li></ul></section><section class="tsd-panel tsd-member"><h3 class="tsd-anchor-link" id="modal"><span>modal</span><a href="#modal" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><ul class="tsd-signatures"><li class=""><div class="tsd-signature tsd-anchor-link" id="modal-1"><span class="tsd-kind-call-signature">modal</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">body</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">ModalCreateBodyRequest</span><span class="tsd-signature-symbol">,</span> <span class="tsd-kind-parameter">options</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">undefined</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">undefined</span><span class="tsd-signature-symbol">&gt;</span><a href="#modal-1" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></div><div class="tsd-description"><div class="tsd-parameters"><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameter-list"><li><span><span class="tsd-kind-parameter">body</span>: <span class="tsd-signature-type">ModalCreateBodyRequest</span></span></li><li><span><code class="tsd-tag">Optional</code><span class="tsd-kind-parameter">options</span>: <span class="tsd-signature-type">undefined</span></span></li></ul></div><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">undefined</span><span class="tsd-signature-symbol">&gt;</span></h4><aside class="tsd-sources"><ul><li>Defined in <a href="https://github.com/tiramisulabs/seyfert//blob/9ae4290ef220ffc96449d469d16d76b2031eca69/src/structures/Interaction.ts#L476">src/structures/Interaction.ts:476</a></li></ul></aside></div></li><li class=""><div class="tsd-signature tsd-anchor-link" id="modal-2"><span class="tsd-kind-call-signature">modal</span><span class="tsd-signature-symbol">(</span><br/>    <span class="tsd-kind-parameter">body</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">ModalCreateBodyRequest</span><span class="tsd-signature-symbol">,</span><br/>    <span class="tsd-kind-parameter">options</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">ModalCreateOptions</span><span class="tsd-signature-symbol">,</span><br/><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">null</span> <span class="tsd-signature-symbol">|</span> <a href="ModalSubmitInteraction.html" class="tsd-signature-type tsd-kind-class">ModalSubmitInteraction</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">&gt;</span><a href="#modal-2" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></div><div class="tsd-description"><div class="tsd-parameters"><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameter-list"><li><span><span class="tsd-kind-parameter">body</span>: <span class="tsd-signature-type">ModalCreateBodyRequest</span></span></li><li><span><span class="tsd-kind-parameter">options</span>: <span class="tsd-signature-type">ModalCreateOptions</span></span></li></ul></div><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">null</span> <span class="tsd-signature-symbol">|</span> <a href="ModalSubmitInteraction.html" class="tsd-signature-type tsd-kind-class">ModalSubmitInteraction</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">&gt;</span></h4><aside class="tsd-sources"><ul><li>Defined in <a href="https://github.com/tiramisulabs/seyfert//blob/9ae4290ef220ffc96449d469d16d76b2031eca69/src/structures/Interaction.ts#L477">src/structures/Interaction.ts:477</a></li></ul></aside></div></li></ul></section><section class="tsd-panel tsd-member tsd-is-inherited"><h3 class="tsd-anchor-link" id="reply"><span>reply</span><a href="#reply" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><ul class="tsd-signatures tsd-is-inherited"><li class="tsd-is-inherited"><div class="tsd-signature tsd-anchor-link" id="reply-1"><span class="tsd-kind-call-signature">reply</span><span class="tsd-signature-symbol">&lt;</span><a class="tsd-signature-type tsd-kind-type-parameter" href="#replywr">WR</a> <span class="tsd-signature-keyword">extends</span> <span class="tsd-signature-type">boolean</span> <span class="tsd-signature-symbol">=</span> <span class="tsd-signature-type">false</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">(</span><br/>    <span class="tsd-kind-parameter">body</span><span class="tsd-signature-symbol">:</span> <a href="../types/ReplyInteractionBody.html" class="tsd-signature-type tsd-kind-type-alias">ReplyInteractionBody</a><span class="tsd-signature-symbol">,</span><br/>    <span class="tsd-kind-parameter">withResponse</span><span class="tsd-signature-symbol">?:</span> <a class="tsd-signature-type tsd-kind-type-parameter" href="BaseInteraction.html#replywr">WR</a><span class="tsd-signature-symbol">,</span><br/><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">When</span><span class="tsd-signature-symbol">&lt;</span><a class="tsd-signature-type tsd-kind-type-parameter" href="BaseInteraction.html#replywr">WR</a><span class="tsd-signature-symbol">,</span> <a href="WebhookMessage.html" class="tsd-signature-type tsd-kind-class">WebhookMessage</a><span class="tsd-signature-symbol">,</span> <span class="tsd-signature-type">undefined</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">&gt;</span><a href="#reply-1" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></div><div class="tsd-description"><section class="tsd-panel"><h4>Type Parameters</h4><ul class="tsd-type-parameter-list"><li><span id="replywr"><span class="tsd-kind-type-parameter">WR</span> <span class="tsd-signature-keyword">extends</span> <span class="tsd-signature-type">boolean</span> = <span class="tsd-signature-type">false</span></span></li></ul></section><div class="tsd-parameters"><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameter-list"><li><span><span class="tsd-kind-parameter">body</span>: <a href="../types/ReplyInteractionBody.html" class="tsd-signature-type tsd-kind-type-alias">ReplyInteractionBody</a></span></li><li><span><code class="tsd-tag">Optional</code><span class="tsd-kind-parameter">withResponse</span>: <a class="tsd-signature-type tsd-kind-type-parameter" href="BaseInteraction.html#replywr">WR</a></span></li></ul></div><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">When</span><span class="tsd-signature-symbol">&lt;</span><a class="tsd-signature-type tsd-kind-type-parameter" href="BaseInteraction.html#replywr">WR</a><span class="tsd-signature-symbol">,</span> <a href="WebhookMessage.html" class="tsd-signature-type tsd-kind-class">WebhookMessage</a><span class="tsd-signature-symbol">,</span> <span class="tsd-signature-type">undefined</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">&gt;</span></h4><aside class="tsd-sources"><p>Inherited from <a href="BaseInteraction.html">BaseInteraction</a>.<a href="BaseInteraction.html#reply">reply</a></p><ul><li>Defined in <a href="https://github.com/tiramisulabs/seyfert//blob/9ae4290ef220ffc96449d469d16d76b2031eca69/src/structures/Interaction.ts#L234">src/structures/Interaction.ts:234</a></li></ul></aside></div></li></ul></section><section class="tsd-panel tsd-member"><h3 class="tsd-anchor-link" id="write"><span>write</span><a href="#write" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><ul class="tsd-signatures"><li class=""><div class="tsd-signature tsd-anchor-link" id="write-1"><span class="tsd-kind-call-signature">write</span><span class="tsd-signature-symbol">&lt;</span><a class="tsd-signature-type tsd-kind-type-parameter" href="#writefr">FR</a> <span class="tsd-signature-keyword">extends</span> <span class="tsd-signature-type">boolean</span> <span class="tsd-signature-symbol">=</span> <span class="tsd-signature-type">false</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">(</span><br/>    <span class="tsd-kind-parameter">body</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Omit</span><span class="tsd-signature-symbol">&lt;</span><br/>        <span class="tsd-signature-type">APIInteractionResponseCallbackData</span><span class="tsd-signature-symbol">,</span><br/>        <span class="tsd-signature-type">&quot;components&quot;</span><br/>        <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;content&quot;</span><br/>        <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;embeds&quot;</span><br/>        <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;poll&quot;</span><span class="tsd-signature-symbol">,</span><br/>    <span class="tsd-signature-symbol">&gt;</span> <span class="tsd-signature-symbol">&amp;</span> <span class="tsd-signature-type">SendResolverProps</span><span class="tsd-signature-symbol">,</span><br/>    <span class="tsd-kind-parameter">withResponse</span><span class="tsd-signature-symbol">?:</span> <a class="tsd-signature-type tsd-kind-type-parameter" href="#writefr">FR</a><span class="tsd-signature-symbol">,</span><br/><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">When</span><span class="tsd-signature-symbol">&lt;</span><a class="tsd-signature-type tsd-kind-type-parameter" href="#writefr">FR</a><span class="tsd-signature-symbol">,</span> <a href="WebhookMessage.html" class="tsd-signature-type tsd-kind-class">WebhookMessage</a><span class="tsd-signature-symbol">,</span> <span class="tsd-signature-type">void</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">&gt;</span><a href="#write-1" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></div><div class="tsd-description"><section class="tsd-panel"><h4>Type Parameters</h4><ul class="tsd-type-parameter-list"><li><span id="writefr"><span class="tsd-kind-type-parameter">FR</span> <span class="tsd-signature-keyword">extends</span> <span class="tsd-signature-type">boolean</span> = <span class="tsd-signature-type">false</span></span></li></ul></section><div class="tsd-parameters"><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameter-list"><li><span><span class="tsd-kind-parameter">body</span>: <span class="tsd-signature-type">Omit</span><span class="tsd-signature-symbol">&lt;</span><br/>    <span class="tsd-signature-type">APIInteractionResponseCallbackData</span><span class="tsd-signature-symbol">,</span><br/>    <span class="tsd-signature-type">&quot;components&quot;</span><br/>    <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;content&quot;</span><br/>    <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;embeds&quot;</span><br/>    <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;poll&quot;</span><span class="tsd-signature-symbol">,</span><br/><span class="tsd-signature-symbol">&gt;</span> <span class="tsd-signature-symbol">&amp;</span> <span class="tsd-signature-type">SendResolverProps</span></span></li><li><span><code class="tsd-tag">Optional</code><span class="tsd-kind-parameter">withResponse</span>: <a class="tsd-signature-type tsd-kind-type-parameter" href="#writefr">FR</a></span></li></ul></div><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">When</span><span class="tsd-signature-symbol">&lt;</span><a class="tsd-signature-type tsd-kind-type-parameter" href="#writefr">FR</a><span class="tsd-signature-symbol">,</span> <a href="WebhookMessage.html" class="tsd-signature-type tsd-kind-class">WebhookMessage</a><span class="tsd-signature-symbol">,</span> <span class="tsd-signature-type">void</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">&gt;</span></h4><aside class="tsd-sources"><ul><li>Defined in <a href="https://github.com/tiramisulabs/seyfert//blob/9ae4290ef220ffc96449d469d16d76b2031eca69/src/structures/Interaction.ts#L463">src/structures/Interaction.ts:463</a></li></ul></aside></div></li></ul></section><section class="tsd-panel tsd-member tsd-is-inherited"><h3 class="tsd-anchor-link" id="from"><code class="tsd-tag">Static</code><span>from</span><a href="#from" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><ul class="tsd-signatures tsd-is-inherited"><li class="tsd-is-inherited"><div class="tsd-signature tsd-anchor-link" id="from-1"><span class="tsd-kind-call-signature">from</span><span class="tsd-signature-symbol">(</span><br/>    <span class="tsd-kind-parameter">client</span><span class="tsd-signature-symbol">:</span> <a href="../interfaces/UsingClient.html" class="tsd-signature-type tsd-kind-interface">UsingClient</a><span class="tsd-signature-symbol">,</span><br/>    <span class="tsd-kind-parameter">gateway</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">APIInteraction</span><span class="tsd-signature-symbol">,</span><br/>    <span class="tsd-kind-parameter">__reply</span><span class="tsd-signature-symbol">?:</span> <a href="../types/__InternalReplyFunction.html" class="tsd-signature-type tsd-kind-type-alias">__InternalReplyFunction</a><span class="tsd-signature-symbol">,</span><br/><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span><br/>    <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">undefined</span><br/>    <span class="tsd-signature-symbol">|</span> <a href="ModalSubmitInteraction.html" class="tsd-signature-type tsd-kind-class">ModalSubmitInteraction</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">&gt;</span><br/>    <span class="tsd-signature-symbol">|</span> <a href="ButtonInteraction.html" class="tsd-signature-type tsd-kind-class">ButtonInteraction</a><br/>    <span class="tsd-signature-symbol">|</span> <a href="ChannelSelectMenuInteraction.html" class="tsd-signature-type tsd-kind-class">ChannelSelectMenuInteraction</a><br/>    <span class="tsd-signature-symbol">|</span> <a href="RoleSelectMenuInteraction.html" class="tsd-signature-type tsd-kind-class">RoleSelectMenuInteraction</a><br/>    <span class="tsd-signature-symbol">|</span> <a href="MentionableSelectMenuInteraction.html" class="tsd-signature-type tsd-kind-class">MentionableSelectMenuInteraction</a><br/>    <span class="tsd-signature-symbol">|</span> <a href="UserSelectMenuInteraction.html" class="tsd-signature-type tsd-kind-class">UserSelectMenuInteraction</a><br/>    <span class="tsd-signature-symbol">|</span> <a href="StringSelectMenuInteraction.html" class="tsd-signature-type tsd-kind-class">StringSelectMenuInteraction</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">&gt;</span><br/>    <span class="tsd-signature-symbol">|</span> <a href="ChatInputCommandInteraction.html" class="tsd-signature-type tsd-kind-class">ChatInputCommandInteraction</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">&gt;</span><br/>    <span class="tsd-signature-symbol">|</span> <a href="UserCommandInteraction.html" class="tsd-signature-type tsd-kind-class">UserCommandInteraction</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">&gt;</span><br/>    <span class="tsd-signature-symbol">|</span> <a href="MessageCommandInteraction.html" class="tsd-signature-type tsd-kind-class">MessageCommandInteraction</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">&gt;</span><br/>    <span class="tsd-signature-symbol">|</span> <a href="AutocompleteInteraction.html" class="tsd-signature-type tsd-kind-class">AutocompleteInteraction</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">&gt;</span><br/>    <span class="tsd-signature-symbol">|</span> <a href="BaseInteraction.html" class="tsd-signature-type tsd-kind-class">BaseInteraction</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">,</span> <span class="tsd-signature-type">APIPingInteraction</span><span class="tsd-signature-symbol">&gt;</span><a href="#from-1" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></div><div class="tsd-description"><div class="tsd-parameters"><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameter-list"><li><span><span class="tsd-kind-parameter">client</span>: <a href="../interfaces/UsingClient.html" class="tsd-signature-type tsd-kind-interface">UsingClient</a></span></li><li><span><span class="tsd-kind-parameter">gateway</span>: <span class="tsd-signature-type">APIInteraction</span></span></li><li><span><code class="tsd-tag">Optional</code><span class="tsd-kind-parameter">__reply</span>: <a href="../types/__InternalReplyFunction.html" class="tsd-signature-type tsd-kind-type-alias">__InternalReplyFunction</a></span></li></ul></div><h4 class="tsd-returns-title">Returns <br/>    <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">undefined</span><br/>    <span class="tsd-signature-symbol">|</span> <a href="ModalSubmitInteraction.html" class="tsd-signature-type tsd-kind-class">ModalSubmitInteraction</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">&gt;</span><br/>    <span class="tsd-signature-symbol">|</span> <a href="ButtonInteraction.html" class="tsd-signature-type tsd-kind-class">ButtonInteraction</a><br/>    <span class="tsd-signature-symbol">|</span> <a href="ChannelSelectMenuInteraction.html" class="tsd-signature-type tsd-kind-class">ChannelSelectMenuInteraction</a><br/>    <span class="tsd-signature-symbol">|</span> <a href="RoleSelectMenuInteraction.html" class="tsd-signature-type tsd-kind-class">RoleSelectMenuInteraction</a><br/>    <span class="tsd-signature-symbol">|</span> <a href="MentionableSelectMenuInteraction.html" class="tsd-signature-type tsd-kind-class">MentionableSelectMenuInteraction</a><br/>    <span class="tsd-signature-symbol">|</span> <a href="UserSelectMenuInteraction.html" class="tsd-signature-type tsd-kind-class">UserSelectMenuInteraction</a><br/>    <span class="tsd-signature-symbol">|</span> <a href="StringSelectMenuInteraction.html" class="tsd-signature-type tsd-kind-class">StringSelectMenuInteraction</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">&gt;</span><br/>    <span class="tsd-signature-symbol">|</span> <a href="ChatInputCommandInteraction.html" class="tsd-signature-type tsd-kind-class">ChatInputCommandInteraction</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">&gt;</span><br/>    <span class="tsd-signature-symbol">|</span> <a href="UserCommandInteraction.html" class="tsd-signature-type tsd-kind-class">UserCommandInteraction</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">&gt;</span><br/>    <span class="tsd-signature-symbol">|</span> <a href="MessageCommandInteraction.html" class="tsd-signature-type tsd-kind-class">MessageCommandInteraction</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">&gt;</span><br/>    <span class="tsd-signature-symbol">|</span> <a href="AutocompleteInteraction.html" class="tsd-signature-type tsd-kind-class">AutocompleteInteraction</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">&gt;</span><br/>    <span class="tsd-signature-symbol">|</span> <a href="BaseInteraction.html" class="tsd-signature-type tsd-kind-class">BaseInteraction</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">,</span> <span class="tsd-signature-type">APIPingInteraction</span><span class="tsd-signature-symbol">&gt;</span></h4><aside class="tsd-sources"><p>Inherited from <a href="BaseInteraction.html">BaseInteraction</a>.<a href="BaseInteraction.html#from">from</a></p><ul><li>Defined in <a href="https://github.com/tiramisulabs/seyfert//blob/9ae4290ef220ffc96449d469d16d76b2031eca69/src/structures/Interaction.ts#L314">src/structures/Interaction.ts:314</a></li></ul></aside></div></li></ul></section><section class="tsd-panel tsd-member tsd-is-inherited"><h3 class="tsd-anchor-link" id="transformbody"><code class="tsd-tag">Static</code><span>transform<wbr/>Body</span><a href="#transformbody" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><ul class="tsd-signatures tsd-is-inherited"><li class="tsd-is-inherited"><div class="tsd-signature tsd-anchor-link" id="transformbody-1"><span class="tsd-kind-call-signature">transformBody</span><span class="tsd-signature-symbol">&lt;</span><a class="tsd-signature-type tsd-kind-type-parameter" href="#transformbodyt">T</a><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">(</span><br/>    <span class="tsd-kind-parameter">body</span><span class="tsd-signature-symbol">:</span><br/>        <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">Omit</span><span class="tsd-signature-symbol">&lt;</span><br/>            <span class="tsd-signature-type">RESTPostAPIChannelMessageJSONBody</span><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-type">&quot;components&quot;</span><br/>            <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;content&quot;</span><br/>            <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;embeds&quot;</span><br/>            <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;poll&quot;</span><span class="tsd-signature-symbol">,</span><br/>        <span class="tsd-signature-symbol">&gt;</span> <span class="tsd-signature-symbol">&amp;</span> <span class="tsd-signature-type">SendResolverProps</span><br/>        <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">Omit</span><span class="tsd-signature-symbol">&lt;</span><br/>            <span class="tsd-signature-type">APIInteractionResponseCallbackData</span><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-type">&quot;components&quot;</span><br/>            <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;content&quot;</span><br/>            <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;embeds&quot;</span><br/>            <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;poll&quot;</span><span class="tsd-signature-symbol">,</span><br/>        <span class="tsd-signature-symbol">&gt;</span> <span class="tsd-signature-symbol">&amp;</span> <span class="tsd-signature-type">SendResolverProps</span><br/>        <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">InteractionMessageUpdateBodyRequest</span><br/>        <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">Omit</span><span class="tsd-signature-symbol">&lt;</span><br/>            <span class="tsd-signature-type">RESTPatchAPIChannelMessageJSONBody</span><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-type">&quot;components&quot;</span><br/>            <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;content&quot;</span><br/>            <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;embeds&quot;</span><span class="tsd-signature-symbol">,</span><br/>        <span class="tsd-signature-symbol">&gt;</span> <span class="tsd-signature-symbol">&amp;</span> <span class="tsd-signature-type">ResolverProps</span><br/>        <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">Omit</span><span class="tsd-signature-symbol">&lt;</span><br/>            <span class="tsd-signature-type">RESTPostAPIWebhookWithTokenJSONBody</span><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-type">&quot;components&quot;</span><br/>            <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;content&quot;</span><br/>            <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;embeds&quot;</span><br/>            <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;poll&quot;</span><span class="tsd-signature-symbol">,</span><br/>        <span class="tsd-signature-symbol">&gt;</span> <span class="tsd-signature-symbol">&amp;</span> <span class="tsd-signature-type">SendResolverProps</span><span class="tsd-signature-symbol">,</span><br/>    <span class="tsd-kind-parameter">files</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">undefined</span> <span class="tsd-signature-symbol">|</span> <a href="../interfaces/RawFile.html" class="tsd-signature-type tsd-kind-interface">RawFile</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">,</span><br/>    <span class="tsd-kind-parameter">self</span><span class="tsd-signature-symbol">:</span> <a href="../interfaces/UsingClient.html" class="tsd-signature-type tsd-kind-interface">UsingClient</a><span class="tsd-signature-symbol">,</span><br/><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <a class="tsd-signature-type tsd-kind-type-parameter" href="BaseInteraction.html#transformbodyt">T</a><a href="#transformbody-1" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></div><div class="tsd-description"><section class="tsd-panel"><h4>Type Parameters</h4><ul class="tsd-type-parameter-list"><li><span id="transformbodyt"><span class="tsd-kind-type-parameter">T</span></span></li></ul></section><div class="tsd-parameters"><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameter-list"><li><span><span class="tsd-kind-parameter">body</span>: <br/>    <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">Omit</span><span class="tsd-signature-symbol">&lt;</span><br/>        <span class="tsd-signature-type">RESTPostAPIChannelMessageJSONBody</span><span class="tsd-signature-symbol">,</span><br/>        <span class="tsd-signature-type">&quot;components&quot;</span><br/>        <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;content&quot;</span><br/>        <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;embeds&quot;</span><br/>        <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;poll&quot;</span><span class="tsd-signature-symbol">,</span><br/>    <span class="tsd-signature-symbol">&gt;</span> <span class="tsd-signature-symbol">&amp;</span> <span class="tsd-signature-type">SendResolverProps</span><br/>    <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">Omit</span><span class="tsd-signature-symbol">&lt;</span><br/>        <span class="tsd-signature-type">APIInteractionResponseCallbackData</span><span class="tsd-signature-symbol">,</span><br/>        <span class="tsd-signature-type">&quot;components&quot;</span><br/>        <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;content&quot;</span><br/>        <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;embeds&quot;</span><br/>        <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;poll&quot;</span><span class="tsd-signature-symbol">,</span><br/>    <span class="tsd-signature-symbol">&gt;</span> <span class="tsd-signature-symbol">&amp;</span> <span class="tsd-signature-type">SendResolverProps</span><br/>    <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">InteractionMessageUpdateBodyRequest</span><br/>    <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">Omit</span><span class="tsd-signature-symbol">&lt;</span><br/>        <span class="tsd-signature-type">RESTPatchAPIChannelMessageJSONBody</span><span class="tsd-signature-symbol">,</span><br/>        <span class="tsd-signature-type">&quot;components&quot;</span><br/>        <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;content&quot;</span><br/>        <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;embeds&quot;</span><span class="tsd-signature-symbol">,</span><br/>    <span class="tsd-signature-symbol">&gt;</span> <span class="tsd-signature-symbol">&amp;</span> <span class="tsd-signature-type">ResolverProps</span><br/>    <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">Omit</span><span class="tsd-signature-symbol">&lt;</span><br/>        <span class="tsd-signature-type">RESTPostAPIWebhookWithTokenJSONBody</span><span class="tsd-signature-symbol">,</span><br/>        <span class="tsd-signature-type">&quot;components&quot;</span><br/>        <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;content&quot;</span><br/>        <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;embeds&quot;</span><br/>        <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;poll&quot;</span><span class="tsd-signature-symbol">,</span><br/>    <span class="tsd-signature-symbol">&gt;</span> <span class="tsd-signature-symbol">&amp;</span> <span class="tsd-signature-type">SendResolverProps</span></span></li><li><span><span class="tsd-kind-parameter">files</span>: <span class="tsd-signature-type">undefined</span> <span class="tsd-signature-symbol">|</span> <a href="../interfaces/RawFile.html" class="tsd-signature-type tsd-kind-interface">RawFile</a><span class="tsd-signature-symbol">[]</span></span></li><li><span><span class="tsd-kind-parameter">self</span>: <a href="../interfaces/UsingClient.html" class="tsd-signature-type tsd-kind-interface">UsingClient</a></span></li></ul></div><h4 class="tsd-returns-title">Returns <a class="tsd-signature-type tsd-kind-type-parameter" href="BaseInteraction.html#transformbodyt">T</a></h4><aside class="tsd-sources"><p>Inherited from <a href="BaseInteraction.html">BaseInteraction</a>.<a href="BaseInteraction.html#transformbody">transformBody</a></p><ul><li>Defined in <a href="https://github.com/tiramisulabs/seyfert//blob/9ae4290ef220ffc96449d469d16d76b2031eca69/src/structures/Interaction.ts#L179">src/structures/Interaction.ts:179</a></li></ul></aside></div></li></ul></section><section class="tsd-panel tsd-member tsd-is-inherited"><h3 class="tsd-anchor-link" id="transformbodyrequest"><code class="tsd-tag">Static</code><span>transform<wbr/>Body<wbr/>Request</span><a href="#transformbodyrequest" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><ul class="tsd-signatures tsd-is-inherited"><li class="tsd-is-inherited"><div class="tsd-signature tsd-anchor-link" id="transformbodyrequest-1"><span class="tsd-kind-call-signature">transformBodyRequest</span><span class="tsd-signature-symbol">(</span><br/>    <span class="tsd-kind-parameter">body</span><span class="tsd-signature-symbol">:</span> <a href="../types/ReplyInteractionBody.html" class="tsd-signature-type tsd-kind-type-alias">ReplyInteractionBody</a><span class="tsd-signature-symbol">,</span><br/>    <span class="tsd-kind-parameter">files</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">undefined</span> <span class="tsd-signature-symbol">|</span> <a href="../interfaces/RawFile.html" class="tsd-signature-type tsd-kind-interface">RawFile</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">,</span><br/>    <span class="tsd-kind-parameter">self</span><span class="tsd-signature-symbol">:</span> <a href="../interfaces/UsingClient.html" class="tsd-signature-type tsd-kind-interface">UsingClient</a><span class="tsd-signature-symbol">,</span><br/><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">APIInteractionResponse</span><a href="#transformbodyrequest-1" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></div><div class="tsd-description"><div class="tsd-parameters"><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameter-list"><li><span><span class="tsd-kind-parameter">body</span>: <a href="../types/ReplyInteractionBody.html" class="tsd-signature-type tsd-kind-type-alias">ReplyInteractionBody</a></span></li><li><span><span class="tsd-kind-parameter">files</span>: <span class="tsd-signature-type">undefined</span> <span class="tsd-signature-symbol">|</span> <a href="../interfaces/RawFile.html" class="tsd-signature-type tsd-kind-interface">RawFile</a><span class="tsd-signature-symbol">[]</span></span></li><li><span><span class="tsd-kind-parameter">self</span>: <a href="../interfaces/UsingClient.html" class="tsd-signature-type tsd-kind-interface">UsingClient</a></span></li></ul></div><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">APIInteractionResponse</span></h4><aside class="tsd-sources"><p>Inherited from <a href="BaseInteraction.html">BaseInteraction</a>.<a href="BaseInteraction.html#transformbodyrequest">transformBodyRequest</a></p><ul><li>Defined in <a href="https://github.com/tiramisulabs/seyfert//blob/9ae4290ef220ffc96449d469d16d76b2031eca69/src/structures/Interaction.ts#L136">src/structures/Interaction.ts:136</a></li></ul></aside></div></li></ul></section></section></details></div><div class="col-sidebar"><div class="page-menu"><div class="tsd-navigation settings"><details class="tsd-accordion"><summary class="tsd-accordion-summary"><svg width="20" height="20" viewBox="0 0 24 24" fill="none" aria-hidden="true"><use href="../assets/icons.svg#icon-chevronDown"></use></svg><h3>Settings</h3></summary><div class="tsd-accordion-details"><div class="tsd-filter-visibility"><span class="settings-label">Member Visibility</span><ul id="tsd-filter-options"><li class="tsd-filter-item"><label class="tsd-filter-input"><input type="checkbox" id="tsd-filter-protected" name="protected"/><svg width="32" height="32" viewBox="0 0 32 32" aria-hidden="true"><rect class="tsd-checkbox-background" width="30" height="30" x="1" y="1" rx="6" fill="none"></rect><path class="tsd-checkbox-checkmark" d="M8.35422 16.8214L13.2143 21.75L24.6458 10.25" stroke="none" stroke-width="3.5" stroke-linejoin="round" fill="none"></path></svg><span>Protected</span></label></li><li class="tsd-filter-item"><label class="tsd-filter-input"><input type="checkbox" id="tsd-filter-inherited" name="inherited" checked/><svg width="32" height="32" viewBox="0 0 32 32" aria-hidden="true"><rect class="tsd-checkbox-background" width="30" height="30" x="1" y="1" rx="6" fill="none"></rect><path class="tsd-checkbox-checkmark" d="M8.35422 16.8214L13.2143 21.75L24.6458 10.25" stroke="none" stroke-width="3.5" stroke-linejoin="round" fill="none"></path></svg><span>Inherited</span></label></li><li class="tsd-filter-item"><label class="tsd-filter-input"><input type="checkbox" id="tsd-filter-external" name="external"/><svg width="32" height="32" viewBox="0 0 32 32" aria-hidden="true"><rect class="tsd-checkbox-background" width="30" height="30" x="1" y="1" rx="6" fill="none"></rect><path class="tsd-checkbox-checkmark" d="M8.35422 16.8214L13.2143 21.75L24.6458 10.25" stroke="none" stroke-width="3.5" stroke-linejoin="round" fill="none"></path></svg><span>External</span></label></li></ul></div><div class="tsd-theme-toggle"><label class="settings-label" for="tsd-theme">Theme</label><select id="tsd-theme"><option value="os">OS</option><option value="light">Light</option><option value="dark">Dark</option></select></div></div></details></div><details open class="tsd-accordion tsd-page-navigation"><summary class="tsd-accordion-summary"><svg width="20" height="20" viewBox="0 0 24 24" fill="none" aria-hidden="true"><use href="../assets/icons.svg#icon-chevronDown"></use></svg><h3>On This Page</h3></summary><div class="tsd-accordion-details"><details open class="tsd-accordion tsd-page-navigation-section"><summary class="tsd-accordion-summary" data-key="section-Constructors"><svg width="20" height="20" viewBox="0 0 24 24" fill="none" aria-hidden="true"><use href="../assets/icons.svg#icon-chevronDown"></use></svg>Constructors</summary><div><a href="#constructor"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Constructor"><use href="../assets/icons.svg#icon-512"></use></svg><span>constructor</span></a></div></details><details open class="tsd-accordion tsd-page-navigation-section"><summary class="tsd-accordion-summary" data-key="section-Properties"><svg width="20" height="20" viewBox="0 0 24 24" fill="none" aria-hidden="true"><use href="../assets/icons.svg#icon-chevronDown"></use></svg>Properties</summary><div><a href="#__reply" class="tsd-is-protected tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>__<wbr/>reply</span></a><a href="#applicationid" class="tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>application<wbr/>Id</span></a><a href="#apppermissions" class="tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>app<wbr/>Permissions</span></a><a href="#attachmentsizelimit" class="tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>attachment<wbr/>Size<wbr/>Limit</span></a><a href="#authorizingintegrationowners" class="tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>authorizing<wbr/>Integration<wbr/>Owners</span></a><a href="#channel"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>channel</span></a><a href="#client" class="tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>client</span></a><a href="#context" class="tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>context</span></a><a href="#data" class="tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>data</span></a><a href="#entitlements" class="tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>entitlements</span></a><a href="#guild" class="tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>guild</span></a><a href="#guildid" class="tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>guild<wbr/>Id</span></a><a href="#guildlocale" class="tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>guild<wbr/>Locale</span></a><a href="#id" class="tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>id</span></a><a href="#locale" class="tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>locale</span></a><a href="#member" class="tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>member</span></a><a href="#message" class="tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>message</span></a><a href="#replied" class="tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>replied</span></a><a href="#token" class="tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>token</span></a><a href="#user" class="tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>user</span></a><a href="#version" class="tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>version</span></a></div></details><details open class="tsd-accordion tsd-page-navigation-section"><summary class="tsd-accordion-summary" data-key="section-Accessors"><svg width="20" height="20" viewBox="0 0 24 24" fill="none" aria-hidden="true"><use href="../assets/icons.svg#icon-chevronDown"></use></svg>Accessors</summary><div><a href="#createdat" class="tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Accessor"><use href="../assets/icons.svg#icon-262144"></use></svg><span>created<wbr/>At</span></a><a href="#createdtimestamp" class="tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Accessor"><use href="../assets/icons.svg#icon-262144"></use></svg><span>created<wbr/>Timestamp</span></a></div></details><details open class="tsd-accordion tsd-page-navigation-section"><summary class="tsd-accordion-summary" data-key="section-Methods"><svg width="20" height="20" viewBox="0 0 24 24" fill="none" aria-hidden="true"><use href="../assets/icons.svg#icon-chevronDown"></use></svg>Methods</summary><div><a href="#deferreply" class="tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Method"><use href="../assets/icons.svg#icon-2048"></use></svg><span>defer<wbr/>Reply</span></a><a href="#deletemessage"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Method"><use href="../assets/icons.svg#icon-2048"></use></svg><span>delete<wbr/>Message</span></a><a href="#deleteresponse"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Method"><use href="../assets/icons.svg#icon-2048"></use></svg><span>delete<wbr/>Response</span></a><a href="#editmessage"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Method"><use href="../assets/icons.svg#icon-2048"></use></svg><span>edit<wbr/>Message</span></a><a href="#editorreply"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Method"><use href="../assets/icons.svg#icon-2048"></use></svg><span>edit<wbr/>Or<wbr/>Reply</span></a><a href="#editresponse"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Method"><use href="../assets/icons.svg#icon-2048"></use></svg><span>edit<wbr/>Response</span></a><a href="#fetchguild" class="tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Method"><use href="../assets/icons.svg#icon-2048"></use></svg><span>fetch<wbr/>Guild</span></a><a href="#fetchmessage"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Method"><use href="../assets/icons.svg#icon-2048"></use></svg><span>fetch<wbr/>Message</span></a><a href="#fetchresponse"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Method"><use href="../assets/icons.svg#icon-2048"></use></svg><span>fetch<wbr/>Response</span></a><a href="#followup"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Method"><use href="../assets/icons.svg#icon-2048"></use></svg><span>followup</span></a><a href="#isautocomplete" class="tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Method"><use href="../assets/icons.svg#icon-2048"></use></svg><span>is<wbr/>Autocomplete</span></a><a href="#isbutton" class="tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Method"><use href="../assets/icons.svg#icon-2048"></use></svg><span>is<wbr/>Button</span></a><a href="#ischannelselectmenu" class="tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Method"><use href="../assets/icons.svg#icon-2048"></use></svg><span>is<wbr/>Channel<wbr/>Select<wbr/>Menu</span></a><a href="#ischatinput" class="tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Method"><use href="../assets/icons.svg#icon-2048"></use></svg><span>is<wbr/>Chat<wbr/>Input</span></a><a href="#isentrypoint" class="tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Method"><use href="../assets/icons.svg#icon-2048"></use></svg><span>is<wbr/>Entry<wbr/>Point</span></a><a href="#ismentionableselectmenu" class="tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Method"><use href="../assets/icons.svg#icon-2048"></use></svg><span>is<wbr/>Mentionable<wbr/>Select<wbr/>Menu</span></a><a href="#ismessage" class="tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Method"><use href="../assets/icons.svg#icon-2048"></use></svg><span>is<wbr/>Message</span></a><a href="#ismodal" class="tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Method"><use href="../assets/icons.svg#icon-2048"></use></svg><span>is<wbr/>Modal</span></a><a href="#isroleselectmenu" class="tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Method"><use href="../assets/icons.svg#icon-2048"></use></svg><span>is<wbr/>Role<wbr/>Select<wbr/>Menu</span></a><a href="#isstringselectmenu" class="tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Method"><use href="../assets/icons.svg#icon-2048"></use></svg><span>is<wbr/>String<wbr/>Select<wbr/>Menu</span></a><a href="#isuser" class="tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Method"><use href="../assets/icons.svg#icon-2048"></use></svg><span>is<wbr/>User</span></a><a href="#isuserselectmenu" class="tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Method"><use href="../assets/icons.svg#icon-2048"></use></svg><span>is<wbr/>User<wbr/>Select<wbr/>Menu</span></a><a href="#modal"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Method"><use href="../assets/icons.svg#icon-2048"></use></svg><span>modal</span></a><a href="#reply" class="tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Method"><use href="../assets/icons.svg#icon-2048"></use></svg><span>reply</span></a><a href="#write"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Method"><use href="../assets/icons.svg#icon-2048"></use></svg><span>write</span></a><a href="#from" class="tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Method"><use href="../assets/icons.svg#icon-2048"></use></svg><span>from</span></a><a href="#transformbody" class="tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Method"><use href="../assets/icons.svg#icon-2048"></use></svg><span>transform<wbr/>Body</span></a><a href="#transformbodyrequest" class="tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Method"><use href="../assets/icons.svg#icon-2048"></use></svg><span>transform<wbr/>Body<wbr/>Request</span></a></div></details></div></details></div><div class="site-menu"><nav class="tsd-navigation"><a href="../modules.html">seyfert</a><ul class="tsd-small-nested-navigation" id="tsd-nav-container"><li>Loading...</li></ul></nav></div></div></div><footer><p class="tsd-generator">Generated using <a href="https://typedoc.org/" target="_blank">TypeDoc</a></p></footer><div class="overlay"></div></body></html>
