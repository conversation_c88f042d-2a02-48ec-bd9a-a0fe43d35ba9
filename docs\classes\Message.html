<!DOCTYPE html><html class="default" lang="en" data-base="../"><head><meta charset="utf-8"/><meta http-equiv="x-ua-compatible" content="IE=edge"/><title>Message | seyfert</title><meta name="description" content="Documentation for seyfert"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="stylesheet" href="../assets/style.css"/><link rel="stylesheet" href="../assets/highlight.css"/><script defer src="../assets/main.js"></script><script async src="../assets/icons.js" id="tsd-icons-script"></script><script async src="../assets/search.js" id="tsd-search-script"></script><script async src="../assets/navigation.js" id="tsd-nav-script"></script><script async src="../assets/hierarchy.js" id="tsd-hierarchy-script"></script></head><body><script>document.documentElement.dataset.theme = localStorage.getItem("tsd-theme") || "os";document.body.style.display="none";setTimeout(() => window.app?app.showPage():document.body.style.removeProperty("display"),500)</script><header class="tsd-page-toolbar"><div class="tsd-toolbar-contents container"><a href="../index.html" class="title">seyfert</a><div id="tsd-toolbar-links"></div><button id="tsd-search-trigger" class="tsd-widget" aria-label="Search"><svg width="16" height="16" viewBox="0 0 16 16" fill="none" aria-hidden="true"><use href="../assets/icons.svg#icon-search"></use></svg></button><dialog id="tsd-search" aria-label="Search"><input role="combobox" id="tsd-search-input" aria-controls="tsd-search-results" aria-autocomplete="list" aria-expanded="true" autocapitalize="off" autocomplete="off" placeholder="Search the docs" maxLength="100"/><ul role="listbox" id="tsd-search-results"></ul><div id="tsd-search-status" aria-live="polite" aria-atomic="true"><div>Preparing search index...</div></div></dialog><a href="#" class="tsd-widget menu" id="tsd-toolbar-menu-trigger" data-toggle="menu" aria-label="Menu"><svg width="16" height="16" viewBox="0 0 16 16" fill="none" aria-hidden="true"><use href="../assets/icons.svg#icon-menu"></use></svg></a></div></header><div class="container container-main"><div class="col-content"><div class="tsd-page-title"><ul class="tsd-breadcrumb" aria-label="Breadcrumb"><li><a href="" aria-current="page">Message</a></li></ul><h1>Class Message</h1></div><section class="tsd-panel tsd-hierarchy" data-refl="21909"><h4>Hierarchy (<a href="../hierarchy.html#Message">View Summary</a>)</h4><ul class="tsd-hierarchy"><li class="tsd-hierarchy-item"><a href="BaseMessage.html" class="tsd-signature-type tsd-kind-class">BaseMessage</a></li><li class="tsd-hierarchy-item"><span class="tsd-signature-type">ObjectToLower</span><span class="tsd-signature-symbol">&lt;</span><br/>    <span class="tsd-signature-type">Omit</span><span class="tsd-signature-symbol">&lt;</span><br/>        <a href="../types/MessageData.html" class="tsd-signature-type tsd-kind-type-alias">MessageData</a><span class="tsd-signature-symbol">,</span><br/>        <span class="tsd-signature-type">&quot;timestamp&quot;</span><br/>        <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;author&quot;</span><br/>        <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;mentions&quot;</span><br/>        <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;components&quot;</span><br/>        <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;poll&quot;</span><br/>        <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;embeds&quot;</span><span class="tsd-signature-symbol">,</span><br/>    <span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/><span class="tsd-signature-symbol">&gt;</span><ul class="tsd-hierarchy"><li class="tsd-hierarchy-item"><span class="tsd-hierarchy-target">Message</span></li></ul></li></ul></section><aside class="tsd-sources"><ul><li>Defined in <a href="https://github.com/tiramisulabs/seyfert//blob/9ae4290ef220ffc96449d469d16d76b2031eca69/src/structures/Message.ts#L144">src/structures/Message.ts:144</a></li><li>Defined in <a href="https://github.com/tiramisulabs/seyfert//blob/9ae4290ef220ffc96449d469d16d76b2031eca69/src/structures/Message.ts#L148">src/structures/Message.ts:148</a></li></ul></aside><section class="tsd-panel-group tsd-index-group"><section class="tsd-panel tsd-index-panel"><details class="tsd-index-content tsd-accordion" open><summary class="tsd-accordion-summary tsd-index-summary"><svg width="20" height="20" viewBox="0 0 24 24" fill="none" aria-hidden="true"><use href="../assets/icons.svg#icon-chevronDown"></use></svg><h5 class="tsd-index-heading uppercase">Index</h5></summary><div class="tsd-accordion-details"><section class="tsd-index-section"><h3 class="tsd-index-heading">Constructors</h3><div class="tsd-index-list"><a href="#constructor" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Constructor"><use href="../assets/icons.svg#icon-512"></use></svg><span>constructor</span></a>
</div></section><section class="tsd-index-section"><h3 class="tsd-index-heading">Properties</h3><div class="tsd-index-list"><a href="#activity" class="tsd-index-link tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>activity?</span></a>
<a href="#application" class="tsd-index-link tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>application?</span></a>
<a href="#applicationid" class="tsd-index-link tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>application<wbr/>Id?</span></a>
<a href="#attachments" class="tsd-index-link tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>attachments</span></a>
<a href="#author" class="tsd-index-link tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>author</span></a>
<a href="#call" class="tsd-index-link tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>call?</span></a>
<a href="#channelid" class="tsd-index-link tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>channel<wbr/>Id</span></a>
<a href="#client" class="tsd-index-link tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>client</span></a>
<a href="#components" class="tsd-index-link tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>components</span></a>
<a href="#content" class="tsd-index-link tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>content</span></a>
<a href="#editedtimestamp" class="tsd-index-link tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>edited<wbr/>Timestamp</span></a>
<a href="#embeds" class="tsd-index-link tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>embeds</span></a>
<a href="#flags" class="tsd-index-link tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>flags?</span></a>
<a href="#guildid" class="tsd-index-link tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>guild<wbr/>Id?</span></a>
<a href="#id" class="tsd-index-link tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>id</span></a>
<a href="#interactionmetadata" class="tsd-index-link tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>interaction<wbr/>Metadata?</span></a>
<a href="#member" class="tsd-index-link tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>member?</span></a>
<a href="#mentionchannels" class="tsd-index-link tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>mention<wbr/>Channels?</span></a>
<a href="#mentioneveryone" class="tsd-index-link tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>mention<wbr/>Everyone</span></a>
<a href="#mentionroles" class="tsd-index-link tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>mention<wbr/>Roles</span></a>
<a href="#mentions" class="tsd-index-link tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>mentions</span></a>
<a href="#messagereference" class="tsd-index-link tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>message<wbr/>Reference?</span></a>
<a href="#messagesnapshots" class="tsd-index-link tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>message<wbr/>Snapshots?</span></a>
<a href="#nonce" class="tsd-index-link tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>nonce?</span></a>
<a href="#pinned" class="tsd-index-link tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>pinned</span></a>
<a href="#poll" class="tsd-index-link tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>poll?</span></a>
<a href="#position" class="tsd-index-link tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>position?</span></a>
<a href="#reactions" class="tsd-index-link tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>reactions?</span></a>
<a href="#referencedmessage" class="tsd-index-link tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>referenced<wbr/>Message?</span></a>
<a href="#resolved" class="tsd-index-link tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>resolved?</span></a>
<a href="#rolesubscriptiondata" class="tsd-index-link tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>role<wbr/>Subscription<wbr/>Data?</span></a>
<a href="#stickeritems" class="tsd-index-link tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>sticker<wbr/>Items?</span></a>
<a href="#thread" class="tsd-index-link tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>thread?</span></a>
<a href="#timestamp" class="tsd-index-link tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>timestamp?</span></a>
<a href="#tts" class="tsd-index-link tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>tts</span></a>
<a href="#type" class="tsd-index-link tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>type</span></a>
<a href="#webhookid" class="tsd-index-link tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>webhook<wbr/>Id?</span></a>
</div></section><section class="tsd-index-section"><h3 class="tsd-index-heading">Accessors</h3><div class="tsd-index-list"><a href="#createdat" class="tsd-index-link tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Accessor"><use href="../assets/icons.svg#icon-262144"></use></svg><span>created<wbr/>At</span></a>
<a href="#createdtimestamp" class="tsd-index-link tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Accessor"><use href="../assets/icons.svg#icon-262144"></use></svg><span>created<wbr/>Timestamp</span></a>
<a href="#url" class="tsd-index-link tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Accessor"><use href="../assets/icons.svg#icon-262144"></use></svg><span>url</span></a>
<a href="#user" class="tsd-index-link tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Accessor"><use href="../assets/icons.svg#icon-262144"></use></svg><span>user</span></a>
</div></section><section class="tsd-index-section"><h3 class="tsd-index-heading">Methods</h3><div class="tsd-index-list"><a href="#channel" class="tsd-index-link tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Method"><use href="../assets/icons.svg#icon-2048"></use></svg><span>channel</span></a>
<a href="#createcomponentcollector" class="tsd-index-link tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Method"><use href="../assets/icons.svg#icon-2048"></use></svg><span>create<wbr/>Component<wbr/>Collector</span></a>
<a href="#crosspost" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Method"><use href="../assets/icons.svg#icon-2048"></use></svg><span>crosspost</span></a>
<a href="#delete" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Method"><use href="../assets/icons.svg#icon-2048"></use></svg><span>delete</span></a>
<a href="#edit" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Method"><use href="../assets/icons.svg#icon-2048"></use></svg><span>edit</span></a>
<a href="#fetch" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Method"><use href="../assets/icons.svg#icon-2048"></use></svg><span>fetch</span></a>
<a href="#guild" class="tsd-index-link tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Method"><use href="../assets/icons.svg#icon-2048"></use></svg><span>guild</span></a>
<a href="#react" class="tsd-index-link tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Method"><use href="../assets/icons.svg#icon-2048"></use></svg><span>react</span></a>
<a href="#reply" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Method"><use href="../assets/icons.svg#icon-2048"></use></svg><span>reply</span></a>
<a href="#write" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Method"><use href="../assets/icons.svg#icon-2048"></use></svg><span>write</span></a>
</div></section></div></details></section></section><details class="tsd-panel-group tsd-member-group tsd-accordion" open><summary class="tsd-accordion-summary" data-key="section-Constructors"><svg width="20" height="20" viewBox="0 0 24 24" fill="none" aria-hidden="true"><use href="../assets/icons.svg#icon-chevronDown"></use></svg><h2>Constructors</h2></summary><section><section class="tsd-panel tsd-member"><h3 class="tsd-anchor-link" id="constructor"><span>constructor</span><a href="#constructor" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><ul class="tsd-signatures"><li class=""><div class="tsd-signature tsd-anchor-link" id="constructormessage"><span class="tsd-signature-keyword">new</span> <span class="tsd-kind-constructor-signature">Message</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">client</span><span class="tsd-signature-symbol">:</span> <a href="../interfaces/UsingClient.html" class="tsd-signature-type tsd-kind-interface">UsingClient</a><span class="tsd-signature-symbol">,</span> <span class="tsd-kind-parameter">data</span><span class="tsd-signature-symbol">:</span> <a href="../types/MessageData.html" class="tsd-signature-type tsd-kind-type-alias">MessageData</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <a href="" class="tsd-signature-type tsd-kind-class">Message</a><a href="#constructormessage" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></div><div class="tsd-description"><div class="tsd-parameters"><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameter-list"><li><span><span class="tsd-kind-parameter">client</span>: <a href="../interfaces/UsingClient.html" class="tsd-signature-type tsd-kind-interface">UsingClient</a></span></li><li><span><span class="tsd-kind-parameter">data</span>: <a href="../types/MessageData.html" class="tsd-signature-type tsd-kind-type-alias">MessageData</a></span></li></ul></div><h4 class="tsd-returns-title">Returns <a href="" class="tsd-signature-type tsd-kind-class">Message</a></h4><aside class="tsd-sources"><p>Inherited from <a href="BaseMessage.html">BaseMessage</a>.<a href="BaseMessage.html#constructor">constructor</a></p><ul><li>Defined in <a href="https://github.com/tiramisulabs/seyfert//blob/9ae4290ef220ffc96449d469d16d76b2031eca69/src/structures/Message.ts#L149">src/structures/Message.ts:149</a></li></ul></aside></div></li></ul></section></section></details><details class="tsd-panel-group tsd-member-group tsd-accordion" open><summary class="tsd-accordion-summary" data-key="section-Properties"><svg width="20" height="20" viewBox="0 0 24 24" fill="none" aria-hidden="true"><use href="../assets/icons.svg#icon-chevronDown"></use></svg><h2>Properties</h2></summary><section><section class="tsd-panel tsd-member tsd-is-inherited"><h3 class="tsd-anchor-link" id="activity"><code class="tsd-tag">Optional</code><span>activity</span><a href="#activity" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">activity</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-symbol">{</span> <span class="tsd-kind-property">partyId</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span> <span class="tsd-kind-property">type</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">MessageActivityType</span> <span class="tsd-signature-symbol">}</span></div><aside class="tsd-sources"><p>Inherited from <a href="BaseMessage.html">BaseMessage</a>.<a href="BaseMessage.html#activity">activity</a></p></aside></section><section class="tsd-panel tsd-member tsd-is-inherited"><h3 class="tsd-anchor-link" id="application"><code class="tsd-tag">Optional</code><span>application</span><a href="#application" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">application</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-symbol">{</span><br/>    <span class="tsd-kind-property">approximateGuildCount</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-property">approximateUserInstallCount</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-property">bot</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">APIUser</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-property">botPublic</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-property">botRequireCodeGrant</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-property">coverImage</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-property">customInstallUrl</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-property">description</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-property">flags</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">ApplicationFlags</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-property">guild</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">APIPartialGuild</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-property">guildId</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-property">icon</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">null</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-property">id</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-property">installParams</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">APIApplicationInstallParams</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-property">integrationTypesConfig</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">APIApplicationIntegrationTypesConfigMap</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-property">interactionsEndpointUrl</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">null</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-property">name</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-property">owner</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">APIUser</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-property">primarySkuId</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-property">privacyPolicyUrl</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-property">redirectUris</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-property">roleConnectionsVerificationUrl</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">null</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-property">rpcOrigins</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-property">slug</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-property">tags</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-symbol">[</span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">,</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">?</span><span class="tsd-signature-symbol">,</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">?</span><span class="tsd-signature-symbol">,</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">?</span><span class="tsd-signature-symbol">,</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">?</span><span class="tsd-signature-symbol">]</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-property">team</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">null</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">APITeam</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-property">termsOfServiceUrl</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-property">verifyKey</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/><span class="tsd-signature-symbol">}</span></div><aside class="tsd-sources"><p>Inherited from <a href="BaseMessage.html">BaseMessage</a>.<a href="BaseMessage.html#application">application</a></p></aside></section><section class="tsd-panel tsd-member tsd-is-inherited"><h3 class="tsd-anchor-link" id="applicationid"><code class="tsd-tag">Optional</code><span>application<wbr/>Id</span><a href="#applicationid" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">applicationId</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">string</span></div><aside class="tsd-sources"><p>Inherited from <a href="BaseMessage.html">BaseMessage</a>.<a href="BaseMessage.html#applicationid">applicationId</a></p></aside></section><section class="tsd-panel tsd-member tsd-is-inherited"><h3 class="tsd-anchor-link" id="attachments"><span>attachments</span><a href="#attachments" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">attachments</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>    <span class="tsd-kind-property">contentType</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-property">description</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-property">durationSecs</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-property">ephemeral</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-property">filename</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-property">flags</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">AttachmentFlags</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-property">height</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">null</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-property">id</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-property">proxyUrl</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-property">size</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-property">title</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-property">url</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-property">waveform</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-property">width</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">null</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">;</span><br/><span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">[]</span></div><aside class="tsd-sources"><p>Inherited from <a href="BaseMessage.html">BaseMessage</a>.<a href="BaseMessage.html#attachments">attachments</a></p></aside></section><section class="tsd-panel tsd-member tsd-is-inherited"><h3 class="tsd-anchor-link" id="author"><span>author</span><a href="#author" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">author</span><span class="tsd-signature-symbol">:</span> <a href="User.html" class="tsd-signature-type tsd-kind-class">User</a></div><aside class="tsd-sources"><p>Inherited from <a href="BaseMessage.html">BaseMessage</a>.<a href="BaseMessage.html#author">author</a></p><ul><li>Defined in <a href="https://github.com/tiramisulabs/seyfert//blob/9ae4290ef220ffc96449d469d16d76b2031eca69/src/structures/Message.ts#L36">src/structures/Message.ts:36</a></li></ul></aside></section><section class="tsd-panel tsd-member tsd-is-inherited"><h3 class="tsd-anchor-link" id="call"><code class="tsd-tag">Optional</code><span>call</span><a href="#call" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">call</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-symbol">{</span> <span class="tsd-kind-property">endedTimestamp</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">null</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span> <span class="tsd-kind-property">participants</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">[]</span> <span class="tsd-signature-symbol">}</span></div><aside class="tsd-sources"><p>Inherited from <a href="BaseMessage.html">BaseMessage</a>.<a href="BaseMessage.html#call">call</a></p></aside></section><section class="tsd-panel tsd-member tsd-is-inherited"><h3 class="tsd-anchor-link" id="channelid"><span>channel<wbr/>Id</span><a href="#channelid" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">channelId</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span></div><aside class="tsd-sources"><p>Inherited from <a href="BaseMessage.html">BaseMessage</a>.<a href="BaseMessage.html#channelid">channelId</a></p></aside></section><section class="tsd-panel tsd-member tsd-is-inherited"><h3 class="tsd-anchor-link" id="client"><code class="tsd-tag">Readonly</code><span>client</span><a href="#client" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">client</span><span class="tsd-signature-symbol">:</span> <a href="../interfaces/UsingClient.html" class="tsd-signature-type tsd-kind-interface">UsingClient</a></div><aside class="tsd-sources"><p>Inherited from <a href="BaseMessage.html">BaseMessage</a>.<a href="BaseMessage.html#client">client</a></p><ul><li>Defined in <a href="https://github.com/tiramisulabs/seyfert//blob/9ae4290ef220ffc96449d469d16d76b2031eca69/src/structures/extra/Base.ts#L41">src/structures/extra/Base.ts:41</a></li></ul></aside></section><section class="tsd-panel tsd-member tsd-is-inherited"><h3 class="tsd-anchor-link" id="components"><span>components</span><a href="#components" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">components</span><span class="tsd-signature-symbol">:</span> <a href="../types/TopLevelComponents.html" class="tsd-signature-type tsd-kind-type-alias">TopLevelComponents</a><span class="tsd-signature-symbol">[]</span></div><aside class="tsd-sources"><p>Inherited from <a href="BaseMessage.html">BaseMessage</a>.<a href="BaseMessage.html#components">components</a></p><ul><li>Defined in <a href="https://github.com/tiramisulabs/seyfert//blob/9ae4290ef220ffc96449d469d16d76b2031eca69/src/structures/Message.ts#L38">src/structures/Message.ts:38</a></li></ul></aside></section><section class="tsd-panel tsd-member tsd-is-inherited"><h3 class="tsd-anchor-link" id="content"><span>content</span><a href="#content" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">content</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span></div><aside class="tsd-sources"><p>Inherited from <a href="BaseMessage.html">BaseMessage</a>.<a href="BaseMessage.html#content">content</a></p></aside></section><section class="tsd-panel tsd-member tsd-is-inherited"><h3 class="tsd-anchor-link" id="editedtimestamp"><span>edited<wbr/>Timestamp</span><a href="#editedtimestamp" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">editedTimestamp</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">null</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">string</span></div><aside class="tsd-sources"><p>Inherited from <a href="BaseMessage.html">BaseMessage</a>.<a href="BaseMessage.html#editedtimestamp">editedTimestamp</a></p></aside></section><section class="tsd-panel tsd-member tsd-is-inherited"><h3 class="tsd-anchor-link" id="embeds"><span>embeds</span><a href="#embeds" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">embeds</span><span class="tsd-signature-symbol">:</span> <a href="InMessageEmbed.html" class="tsd-signature-type tsd-kind-class">InMessageEmbed</a><span class="tsd-signature-symbol">[]</span></div><aside class="tsd-sources"><p>Inherited from <a href="BaseMessage.html">BaseMessage</a>.<a href="BaseMessage.html#embeds">embeds</a></p><ul><li>Defined in <a href="https://github.com/tiramisulabs/seyfert//blob/9ae4290ef220ffc96449d469d16d76b2031eca69/src/structures/Message.ts#L47">src/structures/Message.ts:47</a></li></ul></aside></section><section class="tsd-panel tsd-member tsd-is-inherited"><h3 class="tsd-anchor-link" id="flags"><code class="tsd-tag">Optional</code><span>flags</span><a href="#flags" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">flags</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">MessageFlags</span></div><aside class="tsd-sources"><p>Inherited from <a href="BaseMessage.html">BaseMessage</a>.<a href="BaseMessage.html#flags">flags</a></p></aside></section><section class="tsd-panel tsd-member tsd-is-inherited"><h3 class="tsd-anchor-link" id="guildid"><code class="tsd-tag">Optional</code><span>guild<wbr/>Id</span><a href="#guildid" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">guildId</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">string</span></div><aside class="tsd-sources"><p>Inherited from <a href="BaseMessage.html">BaseMessage</a>.<a href="BaseMessage.html#guildid">guildId</a></p><ul><li>Defined in <a href="https://github.com/tiramisulabs/seyfert//blob/9ae4290ef220ffc96449d469d16d76b2031eca69/src/structures/Message.ts#L35">src/structures/Message.ts:35</a></li></ul></aside></section><section class="tsd-panel tsd-member tsd-is-inherited"><h3 class="tsd-anchor-link" id="id"><span>id</span><a href="#id" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">id</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span></div><aside class="tsd-sources"><p>Inherited from <a href="BaseMessage.html">BaseMessage</a>.<a href="BaseMessage.html#id">id</a></p><ul><li>Defined in <a href="https://github.com/tiramisulabs/seyfert//blob/9ae4290ef220ffc96449d469d16d76b2031eca69/src/structures/extra/DiscordBase.ts#L6">src/structures/extra/DiscordBase.ts:6</a></li></ul></aside></section><section class="tsd-panel tsd-member tsd-is-inherited"><h3 class="tsd-anchor-link" id="interactionmetadata"><code class="tsd-tag">Optional</code><span>interaction<wbr/>Metadata</span><a href="#interactionmetadata" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">interactionMetadata</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-symbol">{</span><br/>    <span class="tsd-kind-property">authorizingIntegrationOwners</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span> <span class="tsd-kind-property">&quot;0&quot;</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span> <span class="tsd-kind-property">&quot;1&quot;</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">string</span> <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-property">id</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-property">interactedMessageId</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-property">originalResponseMessageId</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-property">triggeringInteractionMetadata</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">APIMessageInteractionMetadata</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-property">type</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">InteractionType</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-property">user</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>        <span class="tsd-kind-property">accentColor</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">null</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-property">avatar</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">null</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-property">avatarDecorationData</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">null</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-symbol">{</span> <span class="tsd-kind-property">asset</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span> <span class="tsd-kind-property">skuId</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span> <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-property">banner</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">null</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-property">bot</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-property">discriminator</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-property">email</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">null</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-property">flags</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">UserFlags</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-property">globalName</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">null</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-property">id</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-property">locale</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-property">mfaEnabled</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-property">premiumType</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">UserPremiumType</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-property">publicFlags</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">UserFlags</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-property">system</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-property">username</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-property">verified</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/><span class="tsd-signature-symbol">}</span></div><aside class="tsd-sources"><p>Inherited from <a href="BaseMessage.html">BaseMessage</a>.<a href="BaseMessage.html#interactionmetadata">interactionMetadata</a></p></aside></section><section class="tsd-panel tsd-member tsd-is-inherited"><h3 class="tsd-anchor-link" id="member"><code class="tsd-tag">Optional</code><span>member</span><a href="#member" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">member</span><span class="tsd-signature-symbol">?:</span> <a href="GuildMember.html" class="tsd-signature-type tsd-kind-class">GuildMember</a></div><aside class="tsd-sources"><p>Inherited from <a href="BaseMessage.html">BaseMessage</a>.<a href="BaseMessage.html#member">member</a></p><ul><li>Defined in <a href="https://github.com/tiramisulabs/seyfert//blob/9ae4290ef220ffc96449d469d16d76b2031eca69/src/structures/Message.ts#L37">src/structures/Message.ts:37</a></li></ul></aside></section><section class="tsd-panel tsd-member tsd-is-inherited"><h3 class="tsd-anchor-link" id="mentionchannels"><code class="tsd-tag">Optional</code><span>mention<wbr/>Channels</span><a href="#mentionchannels" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">mentionChannels</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-symbol">{</span><br/>    <span class="tsd-kind-property">guildId</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-property">id</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-property">name</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-property">type</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">ChannelType</span><span class="tsd-signature-symbol">;</span><br/><span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">[]</span></div><aside class="tsd-sources"><p>Inherited from <a href="BaseMessage.html">BaseMessage</a>.<a href="BaseMessage.html#mentionchannels">mentionChannels</a></p></aside></section><section class="tsd-panel tsd-member tsd-is-inherited"><h3 class="tsd-anchor-link" id="mentioneveryone"><span>mention<wbr/>Everyone</span><a href="#mentioneveryone" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">mentionEveryone</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">boolean</span></div><aside class="tsd-sources"><p>Inherited from <a href="BaseMessage.html">BaseMessage</a>.<a href="BaseMessage.html#mentioneveryone">mentionEveryone</a></p></aside></section><section class="tsd-panel tsd-member tsd-is-inherited"><h3 class="tsd-anchor-link" id="mentionroles"><span>mention<wbr/>Roles</span><a href="#mentionroles" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">mentionRoles</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">[]</span></div><aside class="tsd-sources"><p>Inherited from <a href="BaseMessage.html">BaseMessage</a>.<a href="BaseMessage.html#mentionroles">mentionRoles</a></p></aside></section><section class="tsd-panel tsd-member tsd-is-inherited"><h3 class="tsd-anchor-link" id="mentions"><span>mentions</span><a href="#mentions" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">mentions</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>    <span class="tsd-kind-property">channels</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">APIChannelMention</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-property">roles</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-property">users</span><span class="tsd-signature-symbol">:</span> (<a href="User.html" class="tsd-signature-type tsd-kind-class">User</a> <span class="tsd-signature-symbol">|</span> <a href="GuildMember.html" class="tsd-signature-type tsd-kind-class">GuildMember</a>)<span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">;</span><br/><span class="tsd-signature-symbol">}</span></div><aside class="tsd-sources"><p>Inherited from <a href="BaseMessage.html">BaseMessage</a>.<a href="BaseMessage.html#mentions">mentions</a></p><ul><li>Defined in <a href="https://github.com/tiramisulabs/seyfert//blob/9ae4290ef220ffc96449d469d16d76b2031eca69/src/structures/Message.ts#L40">src/structures/Message.ts:40</a></li></ul></aside></section><section class="tsd-panel tsd-member tsd-is-inherited"><h3 class="tsd-anchor-link" id="messagereference"><code class="tsd-tag">Optional</code><span>message<wbr/>Reference</span><a href="#messagereference" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">messageReference</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-symbol">{</span><br/>    <span class="tsd-kind-property">channelId</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-property">guildId</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-property">messageId</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-property">type</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">MessageReferenceType</span><span class="tsd-signature-symbol">;</span><br/><span class="tsd-signature-symbol">}</span></div><aside class="tsd-sources"><p>Inherited from <a href="BaseMessage.html">BaseMessage</a>.<a href="BaseMessage.html#messagereference">messageReference</a></p></aside></section><section class="tsd-panel tsd-member tsd-is-inherited"><h3 class="tsd-anchor-link" id="messagesnapshots"><code class="tsd-tag">Optional</code><span>message<wbr/>Snapshots</span><a href="#messagesnapshots" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">messageSnapshots</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-symbol">{</span><br/>    <span class="tsd-kind-property">message</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>        <span class="tsd-kind-property">attachments</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>            <span class="tsd-kind-property">contentType</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">description</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">durationSecs</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">ephemeral</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">filename</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">flags</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">AttachmentFlags</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">height</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">null</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">id</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">proxyUrl</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">size</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">title</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">url</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">waveform</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">width</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">null</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-property">components</span><span class="tsd-signature-symbol">?:</span> (<br/>            <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-symbol">{</span><br/>                <span class="tsd-kind-property">accentColor</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-kind-property">components</span><span class="tsd-signature-symbol">:</span> (<br/>                    <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-symbol">{</span> <span class="tsd-kind-property">file</span><span class="tsd-signature-symbol">:</span> ...<span class="tsd-signature-symbol">;</span> <span class="tsd-kind-property">id</span><span class="tsd-signature-symbol">?:</span> ...<span class="tsd-signature-symbol">;</span> <span class="tsd-kind-property">spoiler</span><span class="tsd-signature-symbol">?:</span> ...<span class="tsd-signature-symbol">;</span> <span class="tsd-kind-property">type</span><span class="tsd-signature-symbol">:</span> ... <span class="tsd-signature-symbol">}</span><br/>                    <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-symbol">{</span> <span class="tsd-kind-property">id</span><span class="tsd-signature-symbol">?:</span> ...<span class="tsd-signature-symbol">;</span> <span class="tsd-kind-property">items</span><span class="tsd-signature-symbol">:</span> ...<span class="tsd-signature-symbol">;</span> <span class="tsd-kind-property">type</span><span class="tsd-signature-symbol">:</span> ... <span class="tsd-signature-symbol">}</span><br/>                    <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-symbol">{</span> <span class="tsd-kind-property">accessory</span><span class="tsd-signature-symbol">:</span> ...<span class="tsd-signature-symbol">;</span> <span class="tsd-kind-property">components</span><span class="tsd-signature-symbol">:</span> ...<span class="tsd-signature-symbol">;</span> <span class="tsd-kind-property">id</span><span class="tsd-signature-symbol">?:</span> ...<span class="tsd-signature-symbol">;</span> <span class="tsd-kind-property">type</span><span class="tsd-signature-symbol">:</span> ... <span class="tsd-signature-symbol">}</span><br/>                    <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-symbol">{</span> <span class="tsd-kind-property">divider</span><span class="tsd-signature-symbol">?:</span> ...<span class="tsd-signature-symbol">;</span> <span class="tsd-kind-property">id</span><span class="tsd-signature-symbol">?:</span> ...<span class="tsd-signature-symbol">;</span> <span class="tsd-kind-property">spacing</span><span class="tsd-signature-symbol">?:</span> ...<span class="tsd-signature-symbol">;</span> <span class="tsd-kind-property">type</span><span class="tsd-signature-symbol">:</span> ... <span class="tsd-signature-symbol">}</span><br/>                    <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-symbol">{</span> <span class="tsd-kind-property">content</span><span class="tsd-signature-symbol">:</span> ...<span class="tsd-signature-symbol">;</span> <span class="tsd-kind-property">id</span><span class="tsd-signature-symbol">?:</span> ...<span class="tsd-signature-symbol">;</span> <span class="tsd-kind-property">type</span><span class="tsd-signature-symbol">:</span> ... <span class="tsd-signature-symbol">}</span><br/>                    <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-symbol">{</span> <span class="tsd-kind-property">components</span><span class="tsd-signature-symbol">:</span> ...<span class="tsd-signature-symbol">;</span> <span class="tsd-kind-property">type</span><span class="tsd-signature-symbol">:</span> ... <span class="tsd-signature-symbol">}</span><br/>                    <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-symbol">{</span> <span class="tsd-kind-property">description</span><span class="tsd-signature-symbol">?:</span> ...<span class="tsd-signature-symbol">;</span> <span class="tsd-kind-property">id</span><span class="tsd-signature-symbol">?:</span> ...<span class="tsd-signature-symbol">;</span> <span class="tsd-kind-property">media</span><span class="tsd-signature-symbol">:</span> ...<span class="tsd-signature-symbol">;</span> <span class="tsd-kind-property">spoiler</span><span class="tsd-signature-symbol">?:</span> ...<span class="tsd-signature-symbol">;</span> <span class="tsd-kind-property">type</span><span class="tsd-signature-symbol">:</span> ... <span class="tsd-signature-symbol">}</span><br/>                )<span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-kind-property">id</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-kind-property">spoiler</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-kind-property">type</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Container</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-signature-symbol">}</span><br/>            <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-symbol">{</span><br/>                <span class="tsd-kind-property">components</span><span class="tsd-signature-symbol">:</span> (<br/>                    <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-symbol">{</span><br/>                        <span class="tsd-kind-property">customId</span><span class="tsd-signature-symbol">:</span> ...<span class="tsd-signature-symbol">;</span><br/>                        <span class="tsd-kind-property">disabled</span><span class="tsd-signature-symbol">?:</span> ...<span class="tsd-signature-symbol">;</span><br/>                        <span class="tsd-kind-property">emoji</span><span class="tsd-signature-symbol">?:</span> ...<span class="tsd-signature-symbol">;</span><br/>                        <span class="tsd-kind-property">label</span><span class="tsd-signature-symbol">?:</span> ...<span class="tsd-signature-symbol">;</span><br/>                        <span class="tsd-kind-property">style</span><span class="tsd-signature-symbol">:</span> ...<span class="tsd-signature-symbol">;</span><br/>                        <span class="tsd-kind-property">type</span><span class="tsd-signature-symbol">:</span> ...<span class="tsd-signature-symbol">;</span><br/>                    <span class="tsd-signature-symbol">}</span><br/>                    <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-symbol">{</span> <span class="tsd-kind-property">disabled</span><span class="tsd-signature-symbol">?:</span> ...<span class="tsd-signature-symbol">;</span> <span class="tsd-kind-property">skuId</span><span class="tsd-signature-symbol">:</span> ...<span class="tsd-signature-symbol">;</span> <span class="tsd-kind-property">style</span><span class="tsd-signature-symbol">:</span> ...<span class="tsd-signature-symbol">;</span> <span class="tsd-kind-property">type</span><span class="tsd-signature-symbol">:</span> ... <span class="tsd-signature-symbol">}</span><br/>                    <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-symbol">{</span><br/>                        <span class="tsd-kind-property">disabled</span><span class="tsd-signature-symbol">?:</span> ...<span class="tsd-signature-symbol">;</span><br/>                        <span class="tsd-kind-property">emoji</span><span class="tsd-signature-symbol">?:</span> ...<span class="tsd-signature-symbol">;</span><br/>                        <span class="tsd-kind-property">label</span><span class="tsd-signature-symbol">?:</span> ...<span class="tsd-signature-symbol">;</span><br/>                        <span class="tsd-kind-property">style</span><span class="tsd-signature-symbol">:</span> ...<span class="tsd-signature-symbol">;</span><br/>                        <span class="tsd-kind-property">type</span><span class="tsd-signature-symbol">:</span> ...<span class="tsd-signature-symbol">;</span><br/>                        <span class="tsd-kind-property">url</span><span class="tsd-signature-symbol">:</span> ...<span class="tsd-signature-symbol">;</span><br/>                    <span class="tsd-signature-symbol">}</span><br/>                    <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-symbol">{</span><br/>                        <span class="tsd-kind-property">customId</span><span class="tsd-signature-symbol">:</span> ...<span class="tsd-signature-symbol">;</span><br/>                        <span class="tsd-kind-property">label</span><span class="tsd-signature-symbol">:</span> ...<span class="tsd-signature-symbol">;</span><br/>                        <span class="tsd-kind-property">maxLength</span><span class="tsd-signature-symbol">?:</span> ...<span class="tsd-signature-symbol">;</span><br/>                        <span class="tsd-kind-property">minLength</span><span class="tsd-signature-symbol">?:</span> ...<span class="tsd-signature-symbol">;</span><br/>                        <span class="tsd-kind-property">placeholder</span><span class="tsd-signature-symbol">?:</span> ...<span class="tsd-signature-symbol">;</span><br/>                        <span class="tsd-kind-property">required</span><span class="tsd-signature-symbol">?:</span> ...<span class="tsd-signature-symbol">;</span><br/>                        <span class="tsd-kind-property">style</span><span class="tsd-signature-symbol">:</span> ...<span class="tsd-signature-symbol">;</span><br/>                        <span class="tsd-kind-property">type</span><span class="tsd-signature-symbol">:</span> ...<span class="tsd-signature-symbol">;</span><br/>                        <span class="tsd-kind-property">value</span><span class="tsd-signature-symbol">?:</span> ...<span class="tsd-signature-symbol">;</span><br/>                    <span class="tsd-signature-symbol">}</span><br/>                    <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-symbol">{</span><br/>                        <span class="tsd-kind-property">channelTypes</span><span class="tsd-signature-symbol">?:</span> ...<span class="tsd-signature-symbol">;</span><br/>                        <span class="tsd-kind-property">customId</span><span class="tsd-signature-symbol">:</span> ...<span class="tsd-signature-symbol">;</span><br/>                        <span class="tsd-kind-property">defaultValues</span><span class="tsd-signature-symbol">?:</span> ...<span class="tsd-signature-symbol">;</span><br/>                        <span class="tsd-kind-property">disabled</span><span class="tsd-signature-symbol">?:</span> ...<span class="tsd-signature-symbol">;</span><br/>                        <span class="tsd-kind-property">maxValues</span><span class="tsd-signature-symbol">?:</span> ...<span class="tsd-signature-symbol">;</span><br/>                        <span class="tsd-kind-property">minValues</span><span class="tsd-signature-symbol">?:</span> ...<span class="tsd-signature-symbol">;</span><br/>                        <span class="tsd-kind-property">placeholder</span><span class="tsd-signature-symbol">?:</span> ...<span class="tsd-signature-symbol">;</span><br/>                        <span class="tsd-kind-property">type</span><span class="tsd-signature-symbol">:</span> ...<span class="tsd-signature-symbol">;</span><br/>                    <span class="tsd-signature-symbol">}</span><br/>                    <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-symbol">{</span><br/>                        <span class="tsd-kind-property">customId</span><span class="tsd-signature-symbol">:</span> ...<span class="tsd-signature-symbol">;</span><br/>                        <span class="tsd-kind-property">defaultValues</span><span class="tsd-signature-symbol">?:</span> ...<span class="tsd-signature-symbol">;</span><br/>                        <span class="tsd-kind-property">disabled</span><span class="tsd-signature-symbol">?:</span> ...<span class="tsd-signature-symbol">;</span><br/>                        <span class="tsd-kind-property">maxValues</span><span class="tsd-signature-symbol">?:</span> ...<span class="tsd-signature-symbol">;</span><br/>                        <span class="tsd-kind-property">minValues</span><span class="tsd-signature-symbol">?:</span> ...<span class="tsd-signature-symbol">;</span><br/>                        <span class="tsd-kind-property">placeholder</span><span class="tsd-signature-symbol">?:</span> ...<span class="tsd-signature-symbol">;</span><br/>                        <span class="tsd-kind-property">type</span><span class="tsd-signature-symbol">:</span> ...<span class="tsd-signature-symbol">;</span><br/>                    <span class="tsd-signature-symbol">}</span><br/>                    <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-symbol">{</span><br/>                        <span class="tsd-kind-property">customId</span><span class="tsd-signature-symbol">:</span> ...<span class="tsd-signature-symbol">;</span><br/>                        <span class="tsd-kind-property">defaultValues</span><span class="tsd-signature-symbol">?:</span> ...<span class="tsd-signature-symbol">;</span><br/>                        <span class="tsd-kind-property">disabled</span><span class="tsd-signature-symbol">?:</span> ...<span class="tsd-signature-symbol">;</span><br/>                        <span class="tsd-kind-property">maxValues</span><span class="tsd-signature-symbol">?:</span> ...<span class="tsd-signature-symbol">;</span><br/>                        <span class="tsd-kind-property">minValues</span><span class="tsd-signature-symbol">?:</span> ...<span class="tsd-signature-symbol">;</span><br/>                        <span class="tsd-kind-property">placeholder</span><span class="tsd-signature-symbol">?:</span> ...<span class="tsd-signature-symbol">;</span><br/>                        <span class="tsd-kind-property">type</span><span class="tsd-signature-symbol">:</span> ...<span class="tsd-signature-symbol">;</span><br/>                    <span class="tsd-signature-symbol">}</span><br/>                    <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-symbol">{</span><br/>                        <span class="tsd-kind-property">customId</span><span class="tsd-signature-symbol">:</span> ...<span class="tsd-signature-symbol">;</span><br/>                        <span class="tsd-kind-property">disabled</span><span class="tsd-signature-symbol">?:</span> ...<span class="tsd-signature-symbol">;</span><br/>                        <span class="tsd-kind-property">maxValues</span><span class="tsd-signature-symbol">?:</span> ...<span class="tsd-signature-symbol">;</span><br/>                        <span class="tsd-kind-property">minValues</span><span class="tsd-signature-symbol">?:</span> ...<span class="tsd-signature-symbol">;</span><br/>                        <span class="tsd-kind-property">options</span><span class="tsd-signature-symbol">:</span> ...<span class="tsd-signature-symbol">;</span><br/>                        <span class="tsd-kind-property">placeholder</span><span class="tsd-signature-symbol">?:</span> ...<span class="tsd-signature-symbol">;</span><br/>                        <span class="tsd-kind-property">type</span><span class="tsd-signature-symbol">:</span> ...<span class="tsd-signature-symbol">;</span><br/>                    <span class="tsd-signature-symbol">}</span><br/>                    <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-symbol">{</span><br/>                        <span class="tsd-kind-property">customId</span><span class="tsd-signature-symbol">:</span> ...<span class="tsd-signature-symbol">;</span><br/>                        <span class="tsd-kind-property">defaultValues</span><span class="tsd-signature-symbol">?:</span> ...<span class="tsd-signature-symbol">;</span><br/>                        <span class="tsd-kind-property">disabled</span><span class="tsd-signature-symbol">?:</span> ...<span class="tsd-signature-symbol">;</span><br/>                        <span class="tsd-kind-property">maxValues</span><span class="tsd-signature-symbol">?:</span> ...<span class="tsd-signature-symbol">;</span><br/>                        <span class="tsd-kind-property">minValues</span><span class="tsd-signature-symbol">?:</span> ...<span class="tsd-signature-symbol">;</span><br/>                        <span class="tsd-kind-property">placeholder</span><span class="tsd-signature-symbol">?:</span> ...<span class="tsd-signature-symbol">;</span><br/>                        <span class="tsd-kind-property">type</span><span class="tsd-signature-symbol">:</span> ...<span class="tsd-signature-symbol">;</span><br/>                    <span class="tsd-signature-symbol">}</span><br/>                )<span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-kind-property">type</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">ActionRow</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-signature-symbol">}</span><br/>            <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-symbol">{</span><br/>                <span class="tsd-kind-property">file</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>                    <span class="tsd-kind-property">contentType</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>                    <span class="tsd-kind-property">height</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">null</span><br/>                    <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">;</span><br/>                    <span class="tsd-kind-property">proxyUrl</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>                    <span class="tsd-kind-property">url</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>                    <span class="tsd-kind-property">width</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">null</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-kind-property">id</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-kind-property">spoiler</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-kind-property">type</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">File</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-signature-symbol">}</span><br/>            <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-symbol">{</span><br/>                <span class="tsd-kind-property">id</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-kind-property">items</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>                    <span class="tsd-kind-property">description</span><span class="tsd-signature-symbol">?:</span> (...)<br/>                    <span class="tsd-signature-symbol">|</span> (...)<span class="tsd-signature-symbol">;</span><br/>                    <span class="tsd-kind-property">media</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>                        <span class="tsd-kind-property">contentType</span><span class="tsd-signature-symbol">?:</span> ...<span class="tsd-signature-symbol">;</span><br/>                        <span class="tsd-kind-property">height</span><span class="tsd-signature-symbol">?:</span> ...<span class="tsd-signature-symbol">;</span><br/>                        <span class="tsd-kind-property">proxyUrl</span><span class="tsd-signature-symbol">?:</span> ...<span class="tsd-signature-symbol">;</span><br/>                        <span class="tsd-kind-property">url</span><span class="tsd-signature-symbol">:</span> ...<span class="tsd-signature-symbol">;</span><br/>                        <span class="tsd-kind-property">width</span><span class="tsd-signature-symbol">?:</span> ...<span class="tsd-signature-symbol">;</span><br/>                    <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>                    <span class="tsd-kind-property">spoiler</span><span class="tsd-signature-symbol">?:</span> (...)<br/>                    <span class="tsd-signature-symbol">|</span> (...)<br/>                    <span class="tsd-signature-symbol">|</span> (...)<span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-kind-property">type</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">MediaGallery</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-signature-symbol">}</span><br/>            <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-symbol">{</span><br/>                <span class="tsd-kind-property">accessory</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-symbol">{</span><br/>                    <span class="tsd-kind-property">description</span><span class="tsd-signature-symbol">?:</span> (...)<br/>                    <span class="tsd-signature-symbol">|</span> (...)<span class="tsd-signature-symbol">;</span><br/>                    <span class="tsd-kind-property">id</span><span class="tsd-signature-symbol">?:</span> (...) <span class="tsd-signature-symbol">|</span> (...)<span class="tsd-signature-symbol">;</span><br/>                    <span class="tsd-kind-property">media</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>                        <span class="tsd-kind-property">contentType</span><span class="tsd-signature-symbol">?:</span> ...<span class="tsd-signature-symbol">;</span><br/>                        <span class="tsd-kind-property">height</span><span class="tsd-signature-symbol">?:</span> ...<span class="tsd-signature-symbol">;</span><br/>                        <span class="tsd-kind-property">proxyUrl</span><span class="tsd-signature-symbol">?:</span> ...<span class="tsd-signature-symbol">;</span><br/>                        <span class="tsd-kind-property">url</span><span class="tsd-signature-symbol">:</span> ...<span class="tsd-signature-symbol">;</span><br/>                        <span class="tsd-kind-property">width</span><span class="tsd-signature-symbol">?:</span> ...<span class="tsd-signature-symbol">;</span><br/>                    <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>                    <span class="tsd-kind-property">spoiler</span><span class="tsd-signature-symbol">?:</span> (...)<br/>                    <span class="tsd-signature-symbol">|</span> (...)<br/>                    <span class="tsd-signature-symbol">|</span> (...)<span class="tsd-signature-symbol">;</span><br/>                    <span class="tsd-kind-property">type</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Thumbnail</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-signature-symbol">}</span><br/>                <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-symbol">{</span><br/>                    <span class="tsd-kind-property">customId</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>                    <span class="tsd-kind-property">disabled</span><span class="tsd-signature-symbol">?:</span> (...)<br/>                    <span class="tsd-signature-symbol">|</span> (...)<br/>                    <span class="tsd-signature-symbol">|</span> (...)<span class="tsd-signature-symbol">;</span><br/>                    <span class="tsd-kind-property">emoji</span><span class="tsd-signature-symbol">?:</span> (...) <span class="tsd-signature-symbol">|</span> (...)<span class="tsd-signature-symbol">;</span><br/>                    <span class="tsd-kind-property">label</span><span class="tsd-signature-symbol">?:</span> (...) <span class="tsd-signature-symbol">|</span> (...)<span class="tsd-signature-symbol">;</span><br/>                    <span class="tsd-kind-property">style</span><span class="tsd-signature-symbol">:</span> (...) <span class="tsd-signature-symbol">|</span> (...) <span class="tsd-signature-symbol">|</span> (...) <span class="tsd-signature-symbol">|</span> (...)<span class="tsd-signature-symbol">;</span><br/>                    <span class="tsd-kind-property">type</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Button</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-signature-symbol">}</span><br/>                <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-symbol">{</span><br/>                    <span class="tsd-kind-property">disabled</span><span class="tsd-signature-symbol">?:</span> (...)<br/>                    <span class="tsd-signature-symbol">|</span> (...)<br/>                    <span class="tsd-signature-symbol">|</span> (...)<span class="tsd-signature-symbol">;</span><br/>                    <span class="tsd-kind-property">skuId</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>                    <span class="tsd-kind-property">style</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Premium</span><span class="tsd-signature-symbol">;</span><br/>                    <span class="tsd-kind-property">type</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Button</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-signature-symbol">}</span><br/>                <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-symbol">{</span><br/>                    <span class="tsd-kind-property">disabled</span><span class="tsd-signature-symbol">?:</span> (...)<br/>                    <span class="tsd-signature-symbol">|</span> (...)<br/>                    <span class="tsd-signature-symbol">|</span> (...)<span class="tsd-signature-symbol">;</span><br/>                    <span class="tsd-kind-property">emoji</span><span class="tsd-signature-symbol">?:</span> (...) <span class="tsd-signature-symbol">|</span> (...)<span class="tsd-signature-symbol">;</span><br/>                    <span class="tsd-kind-property">label</span><span class="tsd-signature-symbol">?:</span> (...) <span class="tsd-signature-symbol">|</span> (...)<span class="tsd-signature-symbol">;</span><br/>                    <span class="tsd-kind-property">style</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Link</span><span class="tsd-signature-symbol">;</span><br/>                    <span class="tsd-kind-property">type</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Button</span><span class="tsd-signature-symbol">;</span><br/>                    <span class="tsd-kind-property">url</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-kind-property">components</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span> <span class="tsd-kind-property">content</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span> <span class="tsd-kind-property">id</span><span class="tsd-signature-symbol">?:</span> (...)<br/>                <span class="tsd-signature-symbol">|</span> (...)<span class="tsd-signature-symbol">;</span> <span class="tsd-kind-property">type</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">TextDisplay</span> <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-kind-property">id</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-kind-property">type</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Section</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-signature-symbol">}</span><br/>            <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-symbol">{</span> <span class="tsd-kind-property">divider</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">;</span> <span class="tsd-kind-property">id</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">;</span> <span class="tsd-kind-property">spacing</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">Spacing</span><span class="tsd-signature-symbol">;</span> <span class="tsd-kind-property">type</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Separator</span> <span class="tsd-signature-symbol">}</span><br/>            <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-symbol">{</span> <span class="tsd-kind-property">content</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span> <span class="tsd-kind-property">id</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">;</span> <span class="tsd-kind-property">type</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">TextDisplay</span> <span class="tsd-signature-symbol">}</span><br/>        )<span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-property">content</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-property">editedTimestamp</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">null</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-property">embeds</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>            <span class="tsd-kind-property">author</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-symbol">{</span><br/>                <span class="tsd-kind-property">iconUrl</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-kind-property">name</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-kind-property">proxyIconUrl</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-kind-property">url</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">color</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">description</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">fields</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-symbol">{</span> <span class="tsd-kind-property">inline</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">;</span> <span class="tsd-kind-property">name</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span> <span class="tsd-kind-property">value</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span> <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">footer</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-symbol">{</span> <span class="tsd-kind-property">iconUrl</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span> <span class="tsd-kind-property">proxyIconUrl</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span> <span class="tsd-kind-property">text</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span> <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">image</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-symbol">{</span><br/>                <span class="tsd-kind-property">height</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-kind-property">proxyUrl</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-kind-property">url</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-kind-property">width</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">provider</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-symbol">{</span> <span class="tsd-kind-property">name</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span> <span class="tsd-kind-property">url</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">string</span> <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">thumbnail</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-symbol">{</span><br/>                <span class="tsd-kind-property">height</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-kind-property">proxyUrl</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-kind-property">url</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-kind-property">width</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">timestamp</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">title</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">type</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">EmbedType</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">url</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">video</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-symbol">{</span><br/>                <span class="tsd-kind-property">height</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-kind-property">proxyUrl</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-kind-property">url</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-kind-property">width</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-property">flags</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">MessageFlags</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-property">mentionRoles</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-property">mentions</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>            <span class="tsd-kind-property">accentColor</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">null</span><br/>            <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">avatar</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">null</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">avatarDecorationData</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">null</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-symbol">{</span> <span class="tsd-kind-property">asset</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span> <span class="tsd-kind-property">skuId</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span> <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">banner</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">null</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">bot</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">discriminator</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">email</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">null</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">flags</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">UserFlags</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">globalName</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">null</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">id</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">locale</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">mfaEnabled</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">premiumType</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">UserPremiumType</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">publicFlags</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">UserFlags</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">system</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">username</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">verified</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-property">stickerItems</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-symbol">{</span> <span class="tsd-kind-property">formatType</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">StickerFormatType</span><span class="tsd-signature-symbol">;</span> <span class="tsd-kind-property">id</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span> <span class="tsd-kind-property">name</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span> <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-property">timestamp</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-property">type</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">MessageType</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/><span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">[]</span></div><aside class="tsd-sources"><p>Inherited from <a href="BaseMessage.html">BaseMessage</a>.<a href="BaseMessage.html#messagesnapshots">messageSnapshots</a></p></aside></section><section class="tsd-panel tsd-member tsd-is-inherited"><h3 class="tsd-anchor-link" id="nonce"><code class="tsd-tag">Optional</code><span>nonce</span><a href="#nonce" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">nonce</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">string</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">number</span></div><aside class="tsd-sources"><p>Inherited from <a href="BaseMessage.html">BaseMessage</a>.<a href="BaseMessage.html#nonce">nonce</a></p></aside></section><section class="tsd-panel tsd-member tsd-is-inherited"><h3 class="tsd-anchor-link" id="pinned"><span>pinned</span><a href="#pinned" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">pinned</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">boolean</span></div><aside class="tsd-sources"><p>Inherited from <a href="BaseMessage.html">BaseMessage</a>.<a href="BaseMessage.html#pinned">pinned</a></p></aside></section><section class="tsd-panel tsd-member tsd-is-inherited"><h3 class="tsd-anchor-link" id="poll"><code class="tsd-tag">Optional</code><span>poll</span><a href="#poll" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">poll</span><span class="tsd-signature-symbol">?:</span> <a href="Poll.html" class="tsd-signature-type tsd-kind-class">Poll</a></div><aside class="tsd-sources"><p>Inherited from <a href="BaseMessage.html">BaseMessage</a>.<a href="BaseMessage.html#poll">poll</a></p><ul><li>Defined in <a href="https://github.com/tiramisulabs/seyfert//blob/9ae4290ef220ffc96449d469d16d76b2031eca69/src/structures/Message.ts#L39">src/structures/Message.ts:39</a></li></ul></aside></section><section class="tsd-panel tsd-member tsd-is-inherited"><h3 class="tsd-anchor-link" id="position"><code class="tsd-tag">Optional</code><span>position</span><a href="#position" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">position</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">number</span></div><aside class="tsd-sources"><p>Inherited from <a href="BaseMessage.html">BaseMessage</a>.<a href="BaseMessage.html#position">position</a></p></aside></section><section class="tsd-panel tsd-member tsd-is-inherited"><h3 class="tsd-anchor-link" id="reactions"><code class="tsd-tag">Optional</code><span>reactions</span><a href="#reactions" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">reactions</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-symbol">{</span><br/>    <span class="tsd-kind-property">burstColors</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-property">count</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-property">countDetails</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span> <span class="tsd-kind-property">burst</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">;</span> <span class="tsd-kind-property">normal</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">number</span> <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-property">emoji</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span> <span class="tsd-kind-property">animated</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">;</span> <span class="tsd-kind-property">id</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">null</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span> <span class="tsd-kind-property">name</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">null</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">string</span> <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-property">me</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-property">meBurst</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">;</span><br/><span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">[]</span></div><aside class="tsd-sources"><p>Inherited from <a href="BaseMessage.html">BaseMessage</a>.<a href="BaseMessage.html#reactions">reactions</a></p></aside></section><section class="tsd-panel tsd-member tsd-is-inherited"><h3 class="tsd-anchor-link" id="referencedmessage"><code class="tsd-tag">Optional</code><span>referenced<wbr/>Message</span><a href="#referencedmessage" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">referencedMessage</span><span class="tsd-signature-symbol">?:</span><br/>    <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">null</span><br/>    <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-symbol">{</span><br/>        <span class="tsd-kind-property">activity</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">APIMessageActivity</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-property">application</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">Partial</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIApplication</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-property">applicationId</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-property">attachments</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>            <span class="tsd-kind-property">contentType</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">description</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">durationSecs</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">ephemeral</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">filename</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">flags</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">AttachmentFlags</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">height</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">null</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">id</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">proxyUrl</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">size</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">title</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">url</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">waveform</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">width</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">null</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-property">author</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>            <span class="tsd-kind-property">accentColor</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">null</span><br/>            <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">avatar</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">null</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">avatarDecorationData</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">null</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-symbol">{</span> <span class="tsd-kind-property">asset</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span> <span class="tsd-kind-property">skuId</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span> <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">banner</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">null</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">bot</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">discriminator</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">email</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">null</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">flags</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">UserFlags</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">globalName</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">null</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">id</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">locale</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">mfaEnabled</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">premiumType</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">UserPremiumType</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">publicFlags</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">UserFlags</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">system</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">username</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">verified</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-property">call</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">APIMessageCall</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-property">channelId</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-property">components</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">APITopLevelComponent</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-property">content</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-property">editedTimestamp</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">null</span><br/>        <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-property">embeds</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>            <span class="tsd-kind-property">author</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-symbol">{</span><br/>                <span class="tsd-kind-property">iconUrl</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-kind-property">name</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-kind-property">proxyIconUrl</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-kind-property">url</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">color</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">description</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">fields</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-symbol">{</span> <span class="tsd-kind-property">inline</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">;</span> <span class="tsd-kind-property">name</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span> <span class="tsd-kind-property">value</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span> <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">footer</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-symbol">{</span> <span class="tsd-kind-property">iconUrl</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span> <span class="tsd-kind-property">proxyIconUrl</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span> <span class="tsd-kind-property">text</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span> <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">image</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-symbol">{</span><br/>                <span class="tsd-kind-property">height</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-kind-property">proxyUrl</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-kind-property">url</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-kind-property">width</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">provider</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-symbol">{</span> <span class="tsd-kind-property">name</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span> <span class="tsd-kind-property">url</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">string</span> <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">thumbnail</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-symbol">{</span><br/>                <span class="tsd-kind-property">height</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-kind-property">proxyUrl</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-kind-property">url</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-kind-property">width</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">timestamp</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">title</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">type</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">EmbedType</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">url</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">video</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-symbol">{</span><br/>                <span class="tsd-kind-property">height</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-kind-property">proxyUrl</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-kind-property">url</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-kind-property">width</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-property">flags</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">MessageFlags</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-property">id</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-property">interactionMetadata</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">APIMessageInteractionMetadata</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-property">mentionChannels</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">APIChannelMention</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-property">mentionEveryone</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-property">mentionRoles</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-property">mentions</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>            <span class="tsd-kind-property">accentColor</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">null</span><br/>            <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">avatar</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">null</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">avatarDecorationData</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">null</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-symbol">{</span> <span class="tsd-kind-property">asset</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span> <span class="tsd-kind-property">skuId</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span> <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">banner</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">null</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">bot</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">discriminator</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">email</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">null</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">flags</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">UserFlags</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">globalName</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">null</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">id</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">locale</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">mfaEnabled</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">premiumType</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">UserPremiumType</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">publicFlags</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">UserFlags</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">system</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">username</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">verified</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-property">messageReference</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">APIMessageReference</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-property">messageSnapshots</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">APIMessageSnapshot</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-property">nonce</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">string</span><br/>        <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-property">pinned</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-property">poll</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">APIPoll</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-property">position</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-property">reactions</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">APIReaction</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-property">referencedMessage</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">null</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">APIMessage</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-property">resolved</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">APIInteractionDataResolved</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-property">roleSubscriptionData</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">APIMessageRoleSubscriptionData</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-property">stickerItems</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">APIStickerItem</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-property">thread</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">APIThreadChannel</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-property">timestamp</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-property">tts</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-property">type</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">MessageType</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-property">webhookId</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-signature-symbol">}</span></div><aside class="tsd-sources"><p>Inherited from <a href="BaseMessage.html">BaseMessage</a>.<a href="BaseMessage.html#referencedmessage">referencedMessage</a></p></aside></section><section class="tsd-panel tsd-member tsd-is-inherited"><h3 class="tsd-anchor-link" id="resolved"><code class="tsd-tag">Optional</code><span>resolved</span><a href="#resolved" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">resolved</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-symbol">{</span><br/>    <span class="tsd-kind-property">attachments</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">Record</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">,</span> <span class="tsd-signature-type">APIAttachment</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-property">channels</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">Record</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">,</span> <span class="tsd-signature-type">APIInteractionDataResolvedChannel</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-property">members</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">Record</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">,</span> <span class="tsd-signature-type">APIInteractionDataResolvedGuildMember</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-property">roles</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">Record</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">,</span> <span class="tsd-signature-type">APIRole</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-property">users</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">Record</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">,</span> <span class="tsd-signature-type">APIUser</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/><span class="tsd-signature-symbol">}</span></div><aside class="tsd-sources"><p>Inherited from <a href="BaseMessage.html">BaseMessage</a>.<a href="BaseMessage.html#resolved">resolved</a></p></aside></section><section class="tsd-panel tsd-member tsd-is-inherited"><h3 class="tsd-anchor-link" id="rolesubscriptiondata"><code class="tsd-tag">Optional</code><span>role<wbr/>Subscription<wbr/>Data</span><a href="#rolesubscriptiondata" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">roleSubscriptionData</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-symbol">{</span><br/>    <span class="tsd-kind-property">isRenewal</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-property">roleSubscriptionListingId</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-property">tierName</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-property">totalMonthsSubscribed</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">;</span><br/><span class="tsd-signature-symbol">}</span></div><aside class="tsd-sources"><p>Inherited from <a href="BaseMessage.html">BaseMessage</a>.<a href="BaseMessage.html#rolesubscriptiondata">roleSubscriptionData</a></p></aside></section><section class="tsd-panel tsd-member tsd-is-inherited"><h3 class="tsd-anchor-link" id="stickeritems"><code class="tsd-tag">Optional</code><span>sticker<wbr/>Items</span><a href="#stickeritems" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">stickerItems</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-symbol">{</span> <span class="tsd-kind-property">formatType</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">StickerFormatType</span><span class="tsd-signature-symbol">;</span> <span class="tsd-kind-property">id</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span> <span class="tsd-kind-property">name</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span> <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">[]</span></div><aside class="tsd-sources"><p>Inherited from <a href="BaseMessage.html">BaseMessage</a>.<a href="BaseMessage.html#stickeritems">stickerItems</a></p></aside></section><section class="tsd-panel tsd-member tsd-is-inherited"><h3 class="tsd-anchor-link" id="thread"><code class="tsd-tag">Optional</code><span>thread</span><a href="#thread" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">thread</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-symbol">{</span><br/>    <span class="tsd-kind-property">appliedTags</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-property">flags</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">ChannelFlags</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-property">guildId</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-property">id</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-property">lastMessageId</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">null</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-property">lastPinTimestamp</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">null</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-property">member</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">APIThreadMember</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-property">memberCount</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-property">messageCount</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-property">name</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-property">nsfw</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-property">ownerId</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-property">parentId</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-property">permissionOverwrites</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">APIOverwrite</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-property">position</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-property">rateLimitPerUser</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-property">threadMetadata</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">APIThreadMetadata</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-property">totalMessageSent</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-property">type</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">ThreadChannelType</span><span class="tsd-signature-symbol">;</span><br/><span class="tsd-signature-symbol">}</span></div><aside class="tsd-sources"><p>Inherited from <a href="BaseMessage.html">BaseMessage</a>.<a href="BaseMessage.html#thread">thread</a></p></aside></section><section class="tsd-panel tsd-member tsd-is-inherited"><h3 class="tsd-anchor-link" id="timestamp"><code class="tsd-tag">Optional</code><span>timestamp</span><a href="#timestamp" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">timestamp</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">number</span></div><aside class="tsd-sources"><p>Inherited from <a href="BaseMessage.html">BaseMessage</a>.<a href="BaseMessage.html#timestamp">timestamp</a></p><ul><li>Defined in <a href="https://github.com/tiramisulabs/seyfert//blob/9ae4290ef220ffc96449d469d16d76b2031eca69/src/structures/Message.ts#L34">src/structures/Message.ts:34</a></li></ul></aside></section><section class="tsd-panel tsd-member tsd-is-inherited"><h3 class="tsd-anchor-link" id="tts"><span>tts</span><a href="#tts" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">tts</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">boolean</span></div><aside class="tsd-sources"><p>Inherited from <a href="BaseMessage.html">BaseMessage</a>.<a href="BaseMessage.html#tts">tts</a></p></aside></section><section class="tsd-panel tsd-member tsd-is-inherited"><h3 class="tsd-anchor-link" id="type"><span>type</span><a href="#type" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">type</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">MessageType</span></div><aside class="tsd-sources"><p>Inherited from <a href="BaseMessage.html">BaseMessage</a>.<a href="BaseMessage.html#type">type</a></p></aside></section><section class="tsd-panel tsd-member tsd-is-inherited"><h3 class="tsd-anchor-link" id="webhookid"><code class="tsd-tag">Optional</code><span>webhook<wbr/>Id</span><a href="#webhookid" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">webhookId</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">string</span></div><aside class="tsd-sources"><p>Inherited from <a href="BaseMessage.html">BaseMessage</a>.<a href="BaseMessage.html#webhookid">webhookId</a></p></aside></section></section></details><details class="tsd-panel-group tsd-member-group tsd-accordion" open><summary class="tsd-accordion-summary" data-key="section-Accessors"><svg width="20" height="20" viewBox="0 0 24 24" fill="none" aria-hidden="true"><use href="../assets/icons.svg#icon-chevronDown"></use></svg><h2>Accessors</h2></summary><section><section class="tsd-panel tsd-member tsd-is-inherited"><h3 class="tsd-anchor-link" id="createdat"><span>created<wbr/>At</span><a href="#createdat" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><ul class="tsd-signatures tsd-is-inherited"><li><div class="tsd-signature" id="createdatcreatedat"><span class="tsd-signature-keyword">get</span> <span class="tsd-kind-get-signature">createdAt</span><span class="tsd-signature-symbol">()</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Date</span></div><div class="tsd-description"><div class="tsd-comment tsd-typography"><p>createdAt gets the creation Date instace of the current object.</p>
</div><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Date</span></h4><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><p>Inherited from BaseMessage.createdAt</p><ul><li>Defined in <a href="https://github.com/tiramisulabs/seyfert//blob/9ae4290ef220ffc96449d469d16d76b2031eca69/src/structures/extra/DiscordBase.ts#L27">src/structures/extra/DiscordBase.ts:27</a></li></ul></aside></div></li></ul></section><section class="tsd-panel tsd-member tsd-is-inherited"><h3 class="tsd-anchor-link" id="createdtimestamp"><span>created<wbr/>Timestamp</span><a href="#createdtimestamp" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><ul class="tsd-signatures tsd-is-inherited"><li><div class="tsd-signature" id="createdtimestampcreatedtimestamp"><span class="tsd-signature-keyword">get</span> <span class="tsd-kind-get-signature">createdTimestamp</span><span class="tsd-signature-symbol">()</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">number</span></div><div class="tsd-description"><div class="tsd-comment tsd-typography"><p>Create a timestamp for the current object.</p>
</div><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">number</span></h4><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><p>Inherited from BaseMessage.createdTimestamp</p><ul><li>Defined in <a href="https://github.com/tiramisulabs/seyfert//blob/9ae4290ef220ffc96449d469d16d76b2031eca69/src/structures/extra/DiscordBase.ts#L20">src/structures/extra/DiscordBase.ts:20</a></li></ul></aside></div></li></ul></section><section class="tsd-panel tsd-member tsd-is-inherited"><h3 class="tsd-anchor-link" id="url"><span>url</span><a href="#url" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><ul class="tsd-signatures tsd-is-inherited"><li><div class="tsd-signature" id="urlurl"><span class="tsd-signature-keyword">get</span> <span class="tsd-kind-get-signature">url</span><span class="tsd-signature-symbol">()</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">`</span><span class="tsd-signature-type">https://discord.com/channels/</span><span class="tsd-signature-symbol">${</span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">}</span><span class="tsd-signature-type">/</span><span class="tsd-signature-symbol">${</span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">}</span><span class="tsd-signature-type">/</span><span class="tsd-signature-symbol">${</span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">`</span></div><div class="tsd-description"><h4 class="tsd-returns-title">Returns <span class="tsd-signature-symbol">`</span><span class="tsd-signature-type">https://discord.com/channels/</span><span class="tsd-signature-symbol">${</span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">}</span><span class="tsd-signature-type">/</span><span class="tsd-signature-symbol">${</span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">}</span><span class="tsd-signature-type">/</span><span class="tsd-signature-symbol">${</span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">`</span></h4><aside class="tsd-sources"><p>Inherited from BaseMessage.url</p><ul><li>Defined in <a href="https://github.com/tiramisulabs/seyfert//blob/9ae4290ef220ffc96449d469d16d76b2031eca69/src/structures/Message.ts#L69">src/structures/Message.ts:69</a></li></ul></aside></div></li></ul></section><section class="tsd-panel tsd-member tsd-is-inherited"><h3 class="tsd-anchor-link" id="user"><span>user</span><a href="#user" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><ul class="tsd-signatures tsd-is-inherited"><li><div class="tsd-signature" id="useruser"><span class="tsd-signature-keyword">get</span> <span class="tsd-kind-get-signature">user</span><span class="tsd-signature-symbol">()</span><span class="tsd-signature-symbol">:</span> <a href="User.html" class="tsd-signature-type tsd-kind-class">User</a></div><div class="tsd-description"><h4 class="tsd-returns-title">Returns <a href="User.html" class="tsd-signature-type tsd-kind-class">User</a></h4><aside class="tsd-sources"><p>Inherited from BaseMessage.user</p><ul><li>Defined in <a href="https://github.com/tiramisulabs/seyfert//blob/9ae4290ef220ffc96449d469d16d76b2031eca69/src/structures/Message.ts#L61">src/structures/Message.ts:61</a></li></ul></aside></div></li></ul></section></section></details><details class="tsd-panel-group tsd-member-group tsd-accordion" open><summary class="tsd-accordion-summary" data-key="section-Methods"><svg width="20" height="20" viewBox="0 0 24 24" fill="none" aria-hidden="true"><use href="../assets/icons.svg#icon-chevronDown"></use></svg><h2>Methods</h2></summary><section><section class="tsd-panel tsd-member tsd-is-inherited"><h3 class="tsd-anchor-link" id="channel"><span>channel</span><a href="#channel" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><ul class="tsd-signatures tsd-is-inherited"><li class="tsd-is-inherited"><div class="tsd-signature tsd-anchor-link" id="channel-1"><span class="tsd-kind-call-signature">channel</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">mode</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">&quot;rest&quot;</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;flow&quot;</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../types/AllChannels.html" class="tsd-signature-type tsd-kind-type-alias">AllChannels</a><span class="tsd-signature-symbol">&gt;</span><a href="#channel-1" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></div><div class="tsd-description"><div class="tsd-parameters"><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameter-list"><li><span><code class="tsd-tag">Optional</code><span class="tsd-kind-parameter">mode</span>: <span class="tsd-signature-type">&quot;rest&quot;</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;flow&quot;</span></span></li></ul></div><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../types/AllChannels.html" class="tsd-signature-type tsd-kind-type-alias">AllChannels</a><span class="tsd-signature-symbol">&gt;</span></h4><aside class="tsd-sources"><p>Inherited from <a href="BaseMessage.html">BaseMessage</a>.<a href="BaseMessage.html#channel">channel</a></p><ul><li>Defined in <a href="https://github.com/tiramisulabs/seyfert//blob/9ae4290ef220ffc96449d469d16d76b2031eca69/src/structures/Message.ts#L91">src/structures/Message.ts:91</a></li></ul></aside></div></li><li class="tsd-is-inherited"><div class="tsd-signature tsd-anchor-link" id="channel-2"><span class="tsd-kind-call-signature">channel</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">mode</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">&quot;cache&quot;</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <a href="../types/ReturnCache.html" class="tsd-signature-type tsd-kind-type-alias">ReturnCache</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">undefined</span> <span class="tsd-signature-symbol">|</span> <a href="../types/AllChannels.html" class="tsd-signature-type tsd-kind-type-alias">AllChannels</a><span class="tsd-signature-symbol">&gt;</span><a href="#channel-2" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></div><div class="tsd-description"><div class="tsd-parameters"><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameter-list"><li><span><span class="tsd-kind-parameter">mode</span>: <span class="tsd-signature-type">&quot;cache&quot;</span></span></li></ul></div><h4 class="tsd-returns-title">Returns <a href="../types/ReturnCache.html" class="tsd-signature-type tsd-kind-type-alias">ReturnCache</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">undefined</span> <span class="tsd-signature-symbol">|</span> <a href="../types/AllChannels.html" class="tsd-signature-type tsd-kind-type-alias">AllChannels</a><span class="tsd-signature-symbol">&gt;</span></h4><aside class="tsd-sources"><p>Inherited from <a href="BaseMessage.html">BaseMessage</a>.<a href="BaseMessage.html#channel">channel</a></p><ul><li>Defined in <a href="https://github.com/tiramisulabs/seyfert//blob/9ae4290ef220ffc96449d469d16d76b2031eca69/src/structures/Message.ts#L92">src/structures/Message.ts:92</a></li></ul></aside></div></li></ul></section><section class="tsd-panel tsd-member tsd-is-inherited"><h3 class="tsd-anchor-link" id="createcomponentcollector"><span>create<wbr/>Component<wbr/>Collector</span><a href="#createcomponentcollector" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><ul class="tsd-signatures tsd-is-inherited"><li class="tsd-is-inherited"><div class="tsd-signature tsd-anchor-link" id="createcomponentcollector-1"><span class="tsd-kind-call-signature">createComponentCollector</span><span class="tsd-signature-symbol">(</span><br/>    <span class="tsd-kind-parameter">options</span><span class="tsd-signature-symbol">?:</span> <a href="../interfaces/ListenerOptions.html" class="tsd-signature-type tsd-kind-interface">ListenerOptions</a><span class="tsd-signature-symbol">,</span><br/><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">CreateComponentCollectorResult</span><a href="#createcomponentcollector-1" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></div><div class="tsd-description"><div class="tsd-parameters"><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameter-list"><li><span><code class="tsd-tag">Optional</code><span class="tsd-kind-parameter">options</span>: <a href="../interfaces/ListenerOptions.html" class="tsd-signature-type tsd-kind-interface">ListenerOptions</a></span></li></ul></div><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">CreateComponentCollectorResult</span></h4><aside class="tsd-sources"><p>Inherited from <a href="BaseMessage.html">BaseMessage</a>.<a href="BaseMessage.html#createcomponentcollector">createComponentCollector</a></p><ul><li>Defined in <a href="https://github.com/tiramisulabs/seyfert//blob/9ae4290ef220ffc96449d469d16d76b2031eca69/src/structures/Message.ts#L65">src/structures/Message.ts:65</a></li></ul></aside></div></li></ul></section><section class="tsd-panel tsd-member"><h3 class="tsd-anchor-link" id="crosspost"><span>crosspost</span><a href="#crosspost" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><ul class="tsd-signatures"><li class=""><div class="tsd-signature tsd-anchor-link" id="crosspost-1"><span class="tsd-kind-call-signature">crosspost</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">reason</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="" class="tsd-signature-type tsd-kind-class">Message</a><span class="tsd-signature-symbol">&gt;</span><a href="#crosspost-1" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></div><div class="tsd-description"><div class="tsd-parameters"><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameter-list"><li><span><code class="tsd-tag">Optional</code><span class="tsd-kind-parameter">reason</span>: <span class="tsd-signature-type">string</span></span></li></ul></div><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="" class="tsd-signature-type tsd-kind-class">Message</a><span class="tsd-signature-symbol">&gt;</span></h4><aside class="tsd-sources"><ul><li>Defined in <a href="https://github.com/tiramisulabs/seyfert//blob/9ae4290ef220ffc96449d469d16d76b2031eca69/src/structures/Message.ts#L181">src/structures/Message.ts:181</a></li></ul></aside></div></li></ul></section><section class="tsd-panel tsd-member"><h3 class="tsd-anchor-link" id="delete"><span>delete</span><a href="#delete" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><ul class="tsd-signatures"><li class=""><div class="tsd-signature tsd-anchor-link" id="delete-1"><span class="tsd-kind-call-signature">delete</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">reason</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">void</span><span class="tsd-signature-symbol">&gt;</span><a href="#delete-1" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></div><div class="tsd-description"><div class="tsd-parameters"><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameter-list"><li><span><code class="tsd-tag">Optional</code><span class="tsd-kind-parameter">reason</span>: <span class="tsd-signature-type">string</span></span></li></ul></div><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">void</span><span class="tsd-signature-symbol">&gt;</span></h4><aside class="tsd-sources"><ul><li>Defined in <a href="https://github.com/tiramisulabs/seyfert//blob/9ae4290ef220ffc96449d469d16d76b2031eca69/src/structures/Message.ts#L177">src/structures/Message.ts:177</a></li></ul></aside></div></li></ul></section><section class="tsd-panel tsd-member"><h3 class="tsd-anchor-link" id="edit"><span>edit</span><a href="#edit" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><ul class="tsd-signatures"><li class=""><div class="tsd-signature tsd-anchor-link" id="edit-1"><span class="tsd-kind-call-signature">edit</span><span class="tsd-signature-symbol">(</span><br/>    <span class="tsd-kind-parameter">body</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Omit</span><span class="tsd-signature-symbol">&lt;</span><br/>        <span class="tsd-signature-type">RESTPatchAPIChannelMessageJSONBody</span><span class="tsd-signature-symbol">,</span><br/>        <span class="tsd-signature-type">&quot;components&quot;</span><br/>        <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;content&quot;</span><br/>        <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;embeds&quot;</span><span class="tsd-signature-symbol">,</span><br/>    <span class="tsd-signature-symbol">&gt;</span> <span class="tsd-signature-symbol">&amp;</span> <span class="tsd-signature-type">ResolverProps</span><span class="tsd-signature-symbol">,</span><br/><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="" class="tsd-signature-type tsd-kind-class">Message</a><span class="tsd-signature-symbol">&gt;</span><a href="#edit-1" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></div><div class="tsd-description"><div class="tsd-parameters"><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameter-list"><li><span><span class="tsd-kind-parameter">body</span>: <span class="tsd-signature-type">Omit</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTPatchAPIChannelMessageJSONBody</span><span class="tsd-signature-symbol">,</span> <span class="tsd-signature-type">&quot;components&quot;</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;content&quot;</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;embeds&quot;</span><span class="tsd-signature-symbol">&gt;</span> <span class="tsd-signature-symbol">&amp;</span> <span class="tsd-signature-type">ResolverProps</span></span></li></ul></div><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="" class="tsd-signature-type tsd-kind-class">Message</a><span class="tsd-signature-symbol">&gt;</span></h4><aside class="tsd-sources"><ul><li>Defined in <a href="https://github.com/tiramisulabs/seyfert//blob/9ae4290ef220ffc96449d469d16d76b2031eca69/src/structures/Message.ts#L169">src/structures/Message.ts:169</a></li></ul></aside></div></li></ul></section><section class="tsd-panel tsd-member"><h3 class="tsd-anchor-link" id="fetch"><span>fetch</span><a href="#fetch" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><ul class="tsd-signatures"><li class=""><div class="tsd-signature tsd-anchor-link" id="fetch-1"><span class="tsd-kind-call-signature">fetch</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">force</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="" class="tsd-signature-type tsd-kind-class">Message</a><span class="tsd-signature-symbol">&gt;</span><a href="#fetch-1" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></div><div class="tsd-description"><div class="tsd-parameters"><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameter-list"><li><span><span class="tsd-kind-parameter">force</span>: <span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol"> = false</span></span></li></ul></div><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="" class="tsd-signature-type tsd-kind-class">Message</a><span class="tsd-signature-symbol">&gt;</span></h4><aside class="tsd-sources"><ul><li>Defined in <a href="https://github.com/tiramisulabs/seyfert//blob/9ae4290ef220ffc96449d469d16d76b2031eca69/src/structures/Message.ts#L153">src/structures/Message.ts:153</a></li></ul></aside></div></li></ul></section><section class="tsd-panel tsd-member tsd-is-inherited"><h3 class="tsd-anchor-link" id="guild"><span>guild</span><a href="#guild" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><ul class="tsd-signatures tsd-is-inherited"><li class="tsd-is-inherited"><div class="tsd-signature tsd-anchor-link" id="guild-1"><span class="tsd-kind-call-signature">guild</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">mode</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">&quot;rest&quot;</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;flow&quot;</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">undefined</span> <span class="tsd-signature-symbol">|</span> <a href="Guild.html" class="tsd-signature-type tsd-kind-class">Guild</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">&quot;cached&quot;</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;api&quot;</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">&gt;</span><a href="#guild-1" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></div><div class="tsd-description"><div class="tsd-parameters"><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameter-list"><li><span><code class="tsd-tag">Optional</code><span class="tsd-kind-parameter">mode</span>: <span class="tsd-signature-type">&quot;rest&quot;</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;flow&quot;</span></span></li></ul></div><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">undefined</span> <span class="tsd-signature-symbol">|</span> <a href="Guild.html" class="tsd-signature-type tsd-kind-class">Guild</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">&quot;cached&quot;</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;api&quot;</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">&gt;</span></h4><aside class="tsd-sources"><p>Inherited from <a href="BaseMessage.html">BaseMessage</a>.<a href="BaseMessage.html#guild">guild</a></p><ul><li>Defined in <a href="https://github.com/tiramisulabs/seyfert//blob/9ae4290ef220ffc96449d469d16d76b2031eca69/src/structures/Message.ts#L73">src/structures/Message.ts:73</a></li></ul></aside></div></li><li class="tsd-is-inherited"><div class="tsd-signature tsd-anchor-link" id="guild-2"><span class="tsd-kind-call-signature">guild</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">mode</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">&quot;cache&quot;</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <a href="../types/ReturnCache.html" class="tsd-signature-type tsd-kind-type-alias">ReturnCache</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">undefined</span> <span class="tsd-signature-symbol">|</span> <a href="Guild.html" class="tsd-signature-type tsd-kind-class">Guild</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">&quot;cached&quot;</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">&gt;</span><a href="#guild-2" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></div><div class="tsd-description"><div class="tsd-parameters"><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameter-list"><li><span><span class="tsd-kind-parameter">mode</span>: <span class="tsd-signature-type">&quot;cache&quot;</span></span></li></ul></div><h4 class="tsd-returns-title">Returns <a href="../types/ReturnCache.html" class="tsd-signature-type tsd-kind-type-alias">ReturnCache</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">undefined</span> <span class="tsd-signature-symbol">|</span> <a href="Guild.html" class="tsd-signature-type tsd-kind-class">Guild</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">&quot;cached&quot;</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">&gt;</span></h4><aside class="tsd-sources"><p>Inherited from <a href="BaseMessage.html">BaseMessage</a>.<a href="BaseMessage.html#guild">guild</a></p><ul><li>Defined in <a href="https://github.com/tiramisulabs/seyfert//blob/9ae4290ef220ffc96449d469d16d76b2031eca69/src/structures/Message.ts#L74">src/structures/Message.ts:74</a></li></ul></aside></div></li></ul></section><section class="tsd-panel tsd-member tsd-is-inherited"><h3 class="tsd-anchor-link" id="react"><span>react</span><a href="#react" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><ul class="tsd-signatures tsd-is-inherited"><li class="tsd-is-inherited"><div class="tsd-signature tsd-anchor-link" id="react-1"><span class="tsd-kind-call-signature">react</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">emoji</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">EmojiResolvable</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">void</span><span class="tsd-signature-symbol">&gt;</span><a href="#react-1" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></div><div class="tsd-description"><div class="tsd-parameters"><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameter-list"><li><span><span class="tsd-kind-parameter">emoji</span>: <span class="tsd-signature-type">EmojiResolvable</span></span></li></ul></div><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">void</span><span class="tsd-signature-symbol">&gt;</span></h4><aside class="tsd-sources"><p>Inherited from <a href="BaseMessage.html">BaseMessage</a>.<a href="BaseMessage.html#react">react</a></p><ul><li>Defined in <a href="https://github.com/tiramisulabs/seyfert//blob/9ae4290ef220ffc96449d469d16d76b2031eca69/src/structures/Message.ts#L105">src/structures/Message.ts:105</a></li></ul></aside></div></li></ul></section><section class="tsd-panel tsd-member"><h3 class="tsd-anchor-link" id="reply"><span>reply</span><a href="#reply" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><ul class="tsd-signatures"><li class=""><div class="tsd-signature tsd-anchor-link" id="reply-1"><span class="tsd-kind-call-signature">reply</span><span class="tsd-signature-symbol">(</span><br/>    <span class="tsd-kind-parameter">body</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Omit</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">MessageCreateBodyRequest</span><span class="tsd-signature-symbol">,</span> <span class="tsd-signature-type">&quot;message_reference&quot;</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>    <span class="tsd-kind-parameter">fail</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">,</span><br/><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="" class="tsd-signature-type tsd-kind-class">Message</a><span class="tsd-signature-symbol">&gt;</span><a href="#reply-1" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></div><div class="tsd-description"><div class="tsd-parameters"><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameter-list"><li><span><span class="tsd-kind-parameter">body</span>: <span class="tsd-signature-type">Omit</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">MessageCreateBodyRequest</span><span class="tsd-signature-symbol">,</span> <span class="tsd-signature-type">&quot;message_reference&quot;</span><span class="tsd-signature-symbol">&gt;</span></span></li><li><span><span class="tsd-kind-parameter">fail</span>: <span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol"> = true</span></span></li></ul></div><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="" class="tsd-signature-type tsd-kind-class">Message</a><span class="tsd-signature-symbol">&gt;</span></h4><aside class="tsd-sources"><ul><li>Defined in <a href="https://github.com/tiramisulabs/seyfert//blob/9ae4290ef220ffc96449d469d16d76b2031eca69/src/structures/Message.ts#L157">src/structures/Message.ts:157</a></li></ul></aside></div></li></ul></section><section class="tsd-panel tsd-member"><h3 class="tsd-anchor-link" id="write"><span>write</span><a href="#write" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><ul class="tsd-signatures"><li class=""><div class="tsd-signature tsd-anchor-link" id="write-1"><span class="tsd-kind-call-signature">write</span><span class="tsd-signature-symbol">(</span><br/>    <span class="tsd-kind-parameter">body</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Omit</span><span class="tsd-signature-symbol">&lt;</span><br/>        <span class="tsd-signature-type">RESTPostAPIChannelMessageJSONBody</span><span class="tsd-signature-symbol">,</span><br/>        <span class="tsd-signature-type">&quot;components&quot;</span><br/>        <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;content&quot;</span><br/>        <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;embeds&quot;</span><br/>        <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;poll&quot;</span><span class="tsd-signature-symbol">,</span><br/>    <span class="tsd-signature-symbol">&gt;</span> <span class="tsd-signature-symbol">&amp;</span> <span class="tsd-signature-type">SendResolverProps</span><span class="tsd-signature-symbol">,</span><br/><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="" class="tsd-signature-type tsd-kind-class">Message</a><span class="tsd-signature-symbol">&gt;</span><a href="#write-1" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></div><div class="tsd-description"><div class="tsd-parameters"><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameter-list"><li><span><span class="tsd-kind-parameter">body</span>: <span class="tsd-signature-type">Omit</span><span class="tsd-signature-symbol">&lt;</span><br/>    <span class="tsd-signature-type">RESTPostAPIChannelMessageJSONBody</span><span class="tsd-signature-symbol">,</span><br/>    <span class="tsd-signature-type">&quot;components&quot;</span><br/>    <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;content&quot;</span><br/>    <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;embeds&quot;</span><br/>    <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;poll&quot;</span><span class="tsd-signature-symbol">,</span><br/><span class="tsd-signature-symbol">&gt;</span> <span class="tsd-signature-symbol">&amp;</span> <span class="tsd-signature-type">SendResolverProps</span></span></li></ul></div><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="" class="tsd-signature-type tsd-kind-class">Message</a><span class="tsd-signature-symbol">&gt;</span></h4><aside class="tsd-sources"><ul><li>Defined in <a href="https://github.com/tiramisulabs/seyfert//blob/9ae4290ef220ffc96449d469d16d76b2031eca69/src/structures/Message.ts#L173">src/structures/Message.ts:173</a></li></ul></aside></div></li></ul></section></section></details></div><div class="col-sidebar"><div class="page-menu"><div class="tsd-navigation settings"><details class="tsd-accordion"><summary class="tsd-accordion-summary"><svg width="20" height="20" viewBox="0 0 24 24" fill="none" aria-hidden="true"><use href="../assets/icons.svg#icon-chevronDown"></use></svg><h3>Settings</h3></summary><div class="tsd-accordion-details"><div class="tsd-filter-visibility"><span class="settings-label">Member Visibility</span><ul id="tsd-filter-options"><li class="tsd-filter-item"><label class="tsd-filter-input"><input type="checkbox" id="tsd-filter-protected" name="protected"/><svg width="32" height="32" viewBox="0 0 32 32" aria-hidden="true"><rect class="tsd-checkbox-background" width="30" height="30" x="1" y="1" rx="6" fill="none"></rect><path class="tsd-checkbox-checkmark" d="M8.35422 16.8214L13.2143 21.75L24.6458 10.25" stroke="none" stroke-width="3.5" stroke-linejoin="round" fill="none"></path></svg><span>Protected</span></label></li><li class="tsd-filter-item"><label class="tsd-filter-input"><input type="checkbox" id="tsd-filter-inherited" name="inherited" checked/><svg width="32" height="32" viewBox="0 0 32 32" aria-hidden="true"><rect class="tsd-checkbox-background" width="30" height="30" x="1" y="1" rx="6" fill="none"></rect><path class="tsd-checkbox-checkmark" d="M8.35422 16.8214L13.2143 21.75L24.6458 10.25" stroke="none" stroke-width="3.5" stroke-linejoin="round" fill="none"></path></svg><span>Inherited</span></label></li><li class="tsd-filter-item"><label class="tsd-filter-input"><input type="checkbox" id="tsd-filter-external" name="external"/><svg width="32" height="32" viewBox="0 0 32 32" aria-hidden="true"><rect class="tsd-checkbox-background" width="30" height="30" x="1" y="1" rx="6" fill="none"></rect><path class="tsd-checkbox-checkmark" d="M8.35422 16.8214L13.2143 21.75L24.6458 10.25" stroke="none" stroke-width="3.5" stroke-linejoin="round" fill="none"></path></svg><span>External</span></label></li></ul></div><div class="tsd-theme-toggle"><label class="settings-label" for="tsd-theme">Theme</label><select id="tsd-theme"><option value="os">OS</option><option value="light">Light</option><option value="dark">Dark</option></select></div></div></details></div><details open class="tsd-accordion tsd-page-navigation"><summary class="tsd-accordion-summary"><svg width="20" height="20" viewBox="0 0 24 24" fill="none" aria-hidden="true"><use href="../assets/icons.svg#icon-chevronDown"></use></svg><h3>On This Page</h3></summary><div class="tsd-accordion-details"><details open class="tsd-accordion tsd-page-navigation-section"><summary class="tsd-accordion-summary" data-key="section-Constructors"><svg width="20" height="20" viewBox="0 0 24 24" fill="none" aria-hidden="true"><use href="../assets/icons.svg#icon-chevronDown"></use></svg>Constructors</summary><div><a href="#constructor"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Constructor"><use href="../assets/icons.svg#icon-512"></use></svg><span>constructor</span></a></div></details><details open class="tsd-accordion tsd-page-navigation-section"><summary class="tsd-accordion-summary" data-key="section-Properties"><svg width="20" height="20" viewBox="0 0 24 24" fill="none" aria-hidden="true"><use href="../assets/icons.svg#icon-chevronDown"></use></svg>Properties</summary><div><a href="#activity" class="tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>activity</span></a><a href="#application" class="tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>application</span></a><a href="#applicationid" class="tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>application<wbr/>Id</span></a><a href="#attachments" class="tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>attachments</span></a><a href="#author" class="tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>author</span></a><a href="#call" class="tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>call</span></a><a href="#channelid" class="tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>channel<wbr/>Id</span></a><a href="#client" class="tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>client</span></a><a href="#components" class="tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>components</span></a><a href="#content" class="tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>content</span></a><a href="#editedtimestamp" class="tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>edited<wbr/>Timestamp</span></a><a href="#embeds" class="tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>embeds</span></a><a href="#flags" class="tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>flags</span></a><a href="#guildid" class="tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>guild<wbr/>Id</span></a><a href="#id" class="tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>id</span></a><a href="#interactionmetadata" class="tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>interaction<wbr/>Metadata</span></a><a href="#member" class="tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>member</span></a><a href="#mentionchannels" class="tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>mention<wbr/>Channels</span></a><a href="#mentioneveryone" class="tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>mention<wbr/>Everyone</span></a><a href="#mentionroles" class="tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>mention<wbr/>Roles</span></a><a href="#mentions" class="tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>mentions</span></a><a href="#messagereference" class="tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>message<wbr/>Reference</span></a><a href="#messagesnapshots" class="tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>message<wbr/>Snapshots</span></a><a href="#nonce" class="tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>nonce</span></a><a href="#pinned" class="tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>pinned</span></a><a href="#poll" class="tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>poll</span></a><a href="#position" class="tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>position</span></a><a href="#reactions" class="tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>reactions</span></a><a href="#referencedmessage" class="tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>referenced<wbr/>Message</span></a><a href="#resolved" class="tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>resolved</span></a><a href="#rolesubscriptiondata" class="tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>role<wbr/>Subscription<wbr/>Data</span></a><a href="#stickeritems" class="tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>sticker<wbr/>Items</span></a><a href="#thread" class="tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>thread</span></a><a href="#timestamp" class="tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>timestamp</span></a><a href="#tts" class="tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>tts</span></a><a href="#type" class="tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>type</span></a><a href="#webhookid" class="tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>webhook<wbr/>Id</span></a></div></details><details open class="tsd-accordion tsd-page-navigation-section"><summary class="tsd-accordion-summary" data-key="section-Accessors"><svg width="20" height="20" viewBox="0 0 24 24" fill="none" aria-hidden="true"><use href="../assets/icons.svg#icon-chevronDown"></use></svg>Accessors</summary><div><a href="#createdat" class="tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Accessor"><use href="../assets/icons.svg#icon-262144"></use></svg><span>created<wbr/>At</span></a><a href="#createdtimestamp" class="tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Accessor"><use href="../assets/icons.svg#icon-262144"></use></svg><span>created<wbr/>Timestamp</span></a><a href="#url" class="tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Accessor"><use href="../assets/icons.svg#icon-262144"></use></svg><span>url</span></a><a href="#user" class="tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Accessor"><use href="../assets/icons.svg#icon-262144"></use></svg><span>user</span></a></div></details><details open class="tsd-accordion tsd-page-navigation-section"><summary class="tsd-accordion-summary" data-key="section-Methods"><svg width="20" height="20" viewBox="0 0 24 24" fill="none" aria-hidden="true"><use href="../assets/icons.svg#icon-chevronDown"></use></svg>Methods</summary><div><a href="#channel" class="tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Method"><use href="../assets/icons.svg#icon-2048"></use></svg><span>channel</span></a><a href="#createcomponentcollector" class="tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Method"><use href="../assets/icons.svg#icon-2048"></use></svg><span>create<wbr/>Component<wbr/>Collector</span></a><a href="#crosspost"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Method"><use href="../assets/icons.svg#icon-2048"></use></svg><span>crosspost</span></a><a href="#delete"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Method"><use href="../assets/icons.svg#icon-2048"></use></svg><span>delete</span></a><a href="#edit"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Method"><use href="../assets/icons.svg#icon-2048"></use></svg><span>edit</span></a><a href="#fetch"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Method"><use href="../assets/icons.svg#icon-2048"></use></svg><span>fetch</span></a><a href="#guild" class="tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Method"><use href="../assets/icons.svg#icon-2048"></use></svg><span>guild</span></a><a href="#react" class="tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Method"><use href="../assets/icons.svg#icon-2048"></use></svg><span>react</span></a><a href="#reply"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Method"><use href="../assets/icons.svg#icon-2048"></use></svg><span>reply</span></a><a href="#write"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Method"><use href="../assets/icons.svg#icon-2048"></use></svg><span>write</span></a></div></details></div></details></div><div class="site-menu"><nav class="tsd-navigation"><a href="../modules.html">seyfert</a><ul class="tsd-small-nested-navigation" id="tsd-nav-container"><li>Loading...</li></ul></nav></div></div></div><footer><p class="tsd-generator">Generated using <a href="https://typedoc.org/" target="_blank">TypeDoc</a></p></footer><div class="overlay"></div></body></html>
