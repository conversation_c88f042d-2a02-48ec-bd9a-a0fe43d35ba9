import { Transformers, type VoiceStateStructure } from '../../client/transformers';
import type { UsingClient } from '../../commands';
import { toCamelCase } from '../../common';
import type {
	GatewayVoiceChannelEffectSendDispachData,
	GatewayVoiceServerUpdateDispatchData,
	GatewayVoiceStateUpdateDispatchData,
} from '../../types';

export const VOICE_SERVER_UPDATE = (_self: UsingClient, data: GatewayVoiceServerUpdateDispatchData) => {
	return toCamelCase(data);
};

export const VOICE_STATE_UPDATE = async (
	self: UsingClient,
	data: GatewayVoiceStateUpdateDispatchData,
): Promise<[state: VoiceStateStructure] | [state: VoiceStateStructure, old?: VoiceStateStructure]> => {
	if (!data.guild_id) return [Transformers.VoiceState(self, data)];
	return [Transformers.VoiceState(self, data), await self.cache.voiceStates?.get(data.user_id, data.guild_id)];
};

export const VOICE_CHANNEL_EFFECT_SEND = (_self: UsingClient, data: GatewayVoiceChannelEffectSendDispachData) => {
	return toCamelCase(data);
};
