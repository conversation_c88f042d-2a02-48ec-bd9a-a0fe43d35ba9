/**
 * Shared types, interfaces, and utilities for Discord API interactions.
 *
 * This module centralizes common type definitions and configuration interfaces used throughout
 * the API layer, including handler options, request configurations, file upload specifications,
 * and HTTP method types. It serves as the foundation for type-safe API interactions and provides
 * standardized interfaces for authentication, request routing, file handling, and debugging
 * capabilities across all API operations.
 */
import type { MakeRequired } from '../common';

export * from './api';
export * from './utils/constants';
export * from './utils/types';
export { calculateUserDefaultAvatarIndex } from './utils/utils';

export interface ApiHandlerOptions {
	baseUrl?: string;
	domain?: string;
	token: string;
	debug?: boolean;
	agent?: string;
	smartBucket?: boolean;
	workerProxy?: boolean;
	type?: 'Bearer' | 'Bot';
}

export interface ApiHandlerInternalOptions extends MakeRequired<ApiHandlerOptions, 'baseUrl' | 'domain' | 'type'> {
	userAgent: string;
}

export interface RawFile {
	contentType?: string;
	data: Array<PERSON>uffer | Buffer | Uint8Array | Uint8ClampedArray | boolean | number | string;
	key?: string;
	filename: string;
}

export interface ApiRequestOptions {
	body?: Record<string, any>;
	query?: Record<string, any>;
	files?: RawFile[];
	auth?: boolean;
	reason?: string;
	route?: `/${string}`;
	unshift?: boolean;
	appendToFormData?: boolean;
	token?: string;
}

export type HttpMethods = 'GET' | 'DELETE' | 'PUT' | 'POST' | 'PATCH';
