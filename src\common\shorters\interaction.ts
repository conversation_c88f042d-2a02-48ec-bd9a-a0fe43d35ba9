import { BaseInteraction, Modal, type ReplyInteractionBody, resolveFiles } from '../..';
import { Transformers, type WebhookMessageStructure } from '../../client/transformers';
import type { RESTPostAPIWebhookWithTokenWaitResult } from '../../types';
import type { InteractionMessageUpdateBodyRequest, MessageWebhookCreateBodyRequest } from '../types/write';
import { BaseShorter } from './base';

export class InteractionShorter extends BaseShorter {
	async reply(id: string, token: string, body: ReplyInteractionBody, withResponse = false) {
		//@ts-expect-error
		const { files, ...rest } = body.data ?? {};
		//@ts-expect-error
		const data = body.data instanceof Modal ? body.data : rest;
		const parsedFiles = files ? await resolveFiles(files) : undefined;
		return this.client.proxy
			.interactions(id)(token)
			.callback.post({
				body: BaseInteraction.transformBodyRequest(
					{
						type: body.type,
						data,
					},
					parsedFiles,
					this.client,
				),
				files: parsedFiles,
				query: { with_response: withResponse },
			});
	}

	fetchResponse(token: string, messageId: string): Promise<WebhookMessageStructure> {
		return this.client.webhooks.fetchMessage(this.client.applicationId, token, messageId);
	}

	fetchOriginal(token: string): Promise<WebhookMessageStructure> {
		return this.fetchResponse(token, '@original');
	}

	async editMessage(
		token: string,
		messageId: string,
		body: InteractionMessageUpdateBodyRequest,
	): Promise<WebhookMessageStructure> {
		const { files, ...data } = body;
		const parsedFiles = files ? await resolveFiles(files) : undefined;
		const apiMessage = await this.client.proxy
			.webhooks(this.client.applicationId)(token)
			.messages(messageId)
			.patch({
				body: BaseInteraction.transformBody(data, parsedFiles, this.client),
				files: parsedFiles,
			});
		return Transformers.WebhookMessage(this.client, apiMessage, this.client.applicationId, token);
	}

	editOriginal(token: string, body: InteractionMessageUpdateBodyRequest): Promise<WebhookMessageStructure> {
		return this.editMessage(token, '@original', body);
	}

	deleteResponse(token: string, messageId: string) {
		return this.client.proxy
			.webhooks(this.client.applicationId)(token)
			.messages(messageId)
			.delete()
			.then(() => this.client.components.deleteValue(messageId, 'messageDelete'));
	}

	deleteOriginal(token: string) {
		return this.deleteResponse(token, '@original');
	}

	async followup(token: string, { files, ...body }: MessageWebhookCreateBodyRequest): Promise<WebhookMessageStructure> {
		const parsedFiles = files ? await resolveFiles(files) : undefined;
		const apiMessage = (await this.client.proxy
			.webhooks(this.client.applicationId)(token)
			.post({
				body: BaseInteraction.transformBody(body, parsedFiles, this.client),
				files: parsedFiles,
			})) as RESTPostAPIWebhookWithTokenWaitResult;
		return Transformers.WebhookMessage(this.client, apiMessage, this.client.applicationId, token);
	}
}
