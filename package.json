{"name": "<PERSON><PERSON><PERSON><PERSON>", "version": "3.2.4", "description": "The most advanced framework for discord bots", "main": "./lib/index.js", "module": "./lib/index.js", "types": "./lib/index.d.ts", "files": ["lib/**"], "scripts": {"build": "tsc --outDir ./lib", "prepublishOnly": "npm run build", "prepare": "npm run build && husky", "lint": "biome lint --write ./src", "format": "biome format --write ./src", "check-h": "biome check --write ./src", "check": "biome check --verbose --write --no-errors-on-unmatched ./src", "test": "vitest run --config ./tests/vitest.config.mts ./tests/"}, "author": "MARCROCK22", "license": "MIT", "devDependencies": {"@biomejs/biome": "2.0.0", "@changesets/cli": "^2.29.4", "@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "@types/node": "^24.0.3", "husky": "^9.1.7", "typescript": "^5.8.3", "vitest": "^3.2.4"}, "homepage": "https://seyfert.dev", "repository": {"type": "git", "url": "git+https://github.com/tiramisulabs/seyfert.git"}, "bugs": {"url": "https://github.com/tiramisulabs/seyfert"}, "keywords": ["api", "discord", "bots", "typescript", "botdev"], "publishConfig": {"access": "public"}, "maintainers": [{"name": "socram03", "url": "https://github.com/socram03"}], "contributors": [{"name": "Free 公園", "url": "https://github.com/FreeAoi"}, {"name": "<PERSON>", "url": "https://github.com/Drylozu"}], "pnpm": {"onlyBuiltDependencies": ["@biomejs/biome", "esbuild"]}}