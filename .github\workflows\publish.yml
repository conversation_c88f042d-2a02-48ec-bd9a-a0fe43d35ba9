name: Publish

on:
  push:
    branches:
      - build

jobs:
  build:
    name: Publish
    runs-on: ubuntu-latest
    permissions:
      id-token: write
    steps:
      - name: check out code
        uses: actions/checkout@v4

      - name: Install Node
        uses: actions/setup-node@v4
        with:
          node-version: '20.x'
          registry-url: 'https://registry.npmjs.org'

      - uses: pnpm/action-setup@v4
        with:
          version: 9

      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: Create Release Pull Request
        uses: changesets/action@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          NODE_AUTH_TOKEN: ${{ secrets.NPM_TOKEN }}
        with:
          commit: "chore: release packages"
          publish: npm publish --provenance
          title: "chore: release packages"
