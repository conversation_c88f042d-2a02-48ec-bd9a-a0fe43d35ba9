<!DOCTYPE html><html class="default" lang="en" data-base="../"><head><meta charset="utf-8"/><meta http-equiv="x-ua-compatible" content="IE=edge"/><title>GuildTemplate | seyfert</title><meta name="description" content="Documentation for seyfert"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="stylesheet" href="../assets/style.css"/><link rel="stylesheet" href="../assets/highlight.css"/><script defer src="../assets/main.js"></script><script async src="../assets/icons.js" id="tsd-icons-script"></script><script async src="../assets/search.js" id="tsd-search-script"></script><script async src="../assets/navigation.js" id="tsd-nav-script"></script><script async src="../assets/hierarchy.js" id="tsd-hierarchy-script"></script></head><body><script>document.documentElement.dataset.theme = localStorage.getItem("tsd-theme") || "os";document.body.style.display="none";setTimeout(() => window.app?app.showPage():document.body.style.removeProperty("display"),500)</script><header class="tsd-page-toolbar"><div class="tsd-toolbar-contents container"><a href="../index.html" class="title">seyfert</a><div id="tsd-toolbar-links"></div><button id="tsd-search-trigger" class="tsd-widget" aria-label="Search"><svg width="16" height="16" viewBox="0 0 16 16" fill="none" aria-hidden="true"><use href="../assets/icons.svg#icon-search"></use></svg></button><dialog id="tsd-search" aria-label="Search"><input role="combobox" id="tsd-search-input" aria-controls="tsd-search-results" aria-autocomplete="list" aria-expanded="true" autocapitalize="off" autocomplete="off" placeholder="Search the docs" maxLength="100"/><ul role="listbox" id="tsd-search-results"></ul><div id="tsd-search-status" aria-live="polite" aria-atomic="true"><div>Preparing search index...</div></div></dialog><a href="#" class="tsd-widget menu" id="tsd-toolbar-menu-trigger" data-toggle="menu" aria-label="Menu"><svg width="16" height="16" viewBox="0 0 16 16" fill="none" aria-hidden="true"><use href="../assets/icons.svg#icon-menu"></use></svg></a></div></header><div class="container container-main"><div class="col-content"><div class="tsd-page-title"><ul class="tsd-breadcrumb" aria-label="Breadcrumb"><li><a href="" aria-current="page">GuildTemplate</a></li></ul><h1>Class GuildTemplate</h1></div><section class="tsd-panel tsd-hierarchy" data-refl="17221"><h4>Hierarchy</h4><ul class="tsd-hierarchy"><li class="tsd-hierarchy-item"><span class="tsd-signature-type">Base</span></li><li class="tsd-hierarchy-item"><span class="tsd-signature-type">ObjectToLower</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APITemplate</span><span class="tsd-signature-symbol">&gt;</span><ul class="tsd-hierarchy"><li class="tsd-hierarchy-item"><span class="tsd-hierarchy-target">GuildTemplate</span></li></ul></li></ul></section><aside class="tsd-sources"><ul><li>Defined in <a href="https://github.com/tiramisulabs/seyfert//blob/9ae4290ef220ffc96449d469d16d76b2031eca69/src/structures/GuildTemplate.ts#L8">src/structures/GuildTemplate.ts:8</a></li><li>Defined in <a href="https://github.com/tiramisulabs/seyfert//blob/9ae4290ef220ffc96449d469d16d76b2031eca69/src/structures/GuildTemplate.ts#L10">src/structures/GuildTemplate.ts:10</a></li></ul></aside><section class="tsd-panel-group tsd-index-group"><section class="tsd-panel tsd-index-panel"><details class="tsd-index-content tsd-accordion" open><summary class="tsd-accordion-summary tsd-index-summary"><svg width="20" height="20" viewBox="0 0 24 24" fill="none" aria-hidden="true"><use href="../assets/icons.svg#icon-chevronDown"></use></svg><h5 class="tsd-index-heading uppercase">Index</h5></summary><div class="tsd-accordion-details"><section class="tsd-index-section"><h3 class="tsd-index-heading">Constructors</h3><div class="tsd-index-list"><a href="#constructor" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Constructor"><use href="../assets/icons.svg#icon-512"></use></svg><span>constructor</span></a>
</div></section><section class="tsd-index-section"><h3 class="tsd-index-heading">Properties</h3><div class="tsd-index-list"><a href="#client" class="tsd-index-link tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>client</span></a>
<a href="#code" class="tsd-index-link tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>code</span></a>
<a href="#createdat" class="tsd-index-link tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>created<wbr/>At</span></a>
<a href="#creator" class="tsd-index-link tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>creator</span></a>
<a href="#creatorid" class="tsd-index-link tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>creator<wbr/>Id</span></a>
<a href="#description" class="tsd-index-link tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>description</span></a>
<a href="#isdirty" class="tsd-index-link tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>is<wbr/>Dirty</span></a>
<a href="#name" class="tsd-index-link tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>name</span></a>
<a href="#serializedsourceguild" class="tsd-index-link tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>serialized<wbr/>Source<wbr/>Guild</span></a>
<a href="#sourceguildid" class="tsd-index-link tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>source<wbr/>Guild<wbr/>Id</span></a>
<a href="#updatedat" class="tsd-index-link tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>updated<wbr/>At</span></a>
<a href="#usagecount" class="tsd-index-link tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>usage<wbr/>Count</span></a>
</div></section><section class="tsd-index-section"><h3 class="tsd-index-heading">Methods</h3><div class="tsd-index-list"><a href="#delete" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Method"><use href="../assets/icons.svg#icon-2048"></use></svg><span>delete</span></a>
<a href="#edit" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Method"><use href="../assets/icons.svg#icon-2048"></use></svg><span>edit</span></a>
<a href="#fetch" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Method"><use href="../assets/icons.svg#icon-2048"></use></svg><span>fetch</span></a>
<a href="#guild" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Method"><use href="../assets/icons.svg#icon-2048"></use></svg><span>guild</span></a>
<a href="#sync" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Method"><use href="../assets/icons.svg#icon-2048"></use></svg><span>sync</span></a>
<a href="#methods" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Method"><use href="../assets/icons.svg#icon-2048"></use></svg><span>methods</span></a>
</div></section></div></details></section></section><details class="tsd-panel-group tsd-member-group tsd-accordion" open><summary class="tsd-accordion-summary" data-key="section-Constructors"><svg width="20" height="20" viewBox="0 0 24 24" fill="none" aria-hidden="true"><use href="../assets/icons.svg#icon-chevronDown"></use></svg><h2>Constructors</h2></summary><section><section class="tsd-panel tsd-member"><h3 class="tsd-anchor-link" id="constructor"><span>constructor</span><a href="#constructor" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><ul class="tsd-signatures"><li class=""><div class="tsd-signature tsd-anchor-link" id="constructorguildtemplate"><span class="tsd-signature-keyword">new</span> <span class="tsd-kind-constructor-signature">GuildTemplate</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">client</span><span class="tsd-signature-symbol">:</span> <a href="../interfaces/UsingClient.html" class="tsd-signature-type tsd-kind-interface">UsingClient</a><span class="tsd-signature-symbol">,</span> <span class="tsd-kind-parameter">data</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">APITemplate</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <a href="" class="tsd-signature-type tsd-kind-class">GuildTemplate</a><a href="#constructorguildtemplate" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></div><div class="tsd-description"><div class="tsd-parameters"><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameter-list"><li><span><span class="tsd-kind-parameter">client</span>: <a href="../interfaces/UsingClient.html" class="tsd-signature-type tsd-kind-interface">UsingClient</a></span></li><li><span><span class="tsd-kind-parameter">data</span>: <span class="tsd-signature-type">APITemplate</span></span></li></ul></div><h4 class="tsd-returns-title">Returns <a href="" class="tsd-signature-type tsd-kind-class">GuildTemplate</a></h4><aside class="tsd-sources"><p>Inherited from Base.constructor</p><ul><li>Defined in <a href="https://github.com/tiramisulabs/seyfert//blob/9ae4290ef220ffc96449d469d16d76b2031eca69/src/structures/GuildTemplate.ts#L11">src/structures/GuildTemplate.ts:11</a></li></ul></aside></div></li></ul></section></section></details><details class="tsd-panel-group tsd-member-group tsd-accordion" open><summary class="tsd-accordion-summary" data-key="section-Properties"><svg width="20" height="20" viewBox="0 0 24 24" fill="none" aria-hidden="true"><use href="../assets/icons.svg#icon-chevronDown"></use></svg><h2>Properties</h2></summary><section><section class="tsd-panel tsd-member tsd-is-inherited"><h3 class="tsd-anchor-link" id="client"><code class="tsd-tag">Readonly</code><span>client</span><a href="#client" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">client</span><span class="tsd-signature-symbol">:</span> <a href="../interfaces/UsingClient.html" class="tsd-signature-type tsd-kind-interface">UsingClient</a></div><aside class="tsd-sources"><p>Inherited from Base.client</p><ul><li>Defined in <a href="https://github.com/tiramisulabs/seyfert//blob/9ae4290ef220ffc96449d469d16d76b2031eca69/src/structures/extra/Base.ts#L41">src/structures/extra/Base.ts:41</a></li></ul></aside></section><section class="tsd-panel tsd-member tsd-is-inherited"><h3 class="tsd-anchor-link" id="code"><span>code</span><a href="#code" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">code</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span></div><aside class="tsd-sources"><p>Inherited from ObjectToLower.code</p></aside></section><section class="tsd-panel tsd-member tsd-is-inherited"><h3 class="tsd-anchor-link" id="createdat"><span>created<wbr/>At</span><a href="#createdat" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">createdAt</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span></div><aside class="tsd-sources"><p>Inherited from ObjectToLower.createdAt</p></aside></section><section class="tsd-panel tsd-member tsd-is-inherited"><h3 class="tsd-anchor-link" id="creator"><span>creator</span><a href="#creator" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">creator</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>    <span class="tsd-kind-property">accentColor</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">null</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-property">avatar</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">null</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-property">avatarDecorationData</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">null</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-symbol">{</span> <span class="tsd-kind-property">asset</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span> <span class="tsd-kind-property">skuId</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span> <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-property">banner</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">null</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-property">bot</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-property">discriminator</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-property">email</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">null</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-property">flags</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">UserFlags</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-property">globalName</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">null</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-property">id</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-property">locale</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-property">mfaEnabled</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-property">premiumType</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">UserPremiumType</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-property">publicFlags</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">UserFlags</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-property">system</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-property">username</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-property">verified</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">;</span><br/><span class="tsd-signature-symbol">}</span></div><aside class="tsd-sources"><p>Inherited from ObjectToLower.creator</p></aside></section><section class="tsd-panel tsd-member tsd-is-inherited"><h3 class="tsd-anchor-link" id="creatorid"><span>creator<wbr/>Id</span><a href="#creatorid" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">creatorId</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span></div><aside class="tsd-sources"><p>Inherited from ObjectToLower.creatorId</p></aside></section><section class="tsd-panel tsd-member tsd-is-inherited"><h3 class="tsd-anchor-link" id="description"><span>description</span><a href="#description" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">description</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">null</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">string</span></div><aside class="tsd-sources"><p>Inherited from ObjectToLower.description</p></aside></section><section class="tsd-panel tsd-member tsd-is-inherited"><h3 class="tsd-anchor-link" id="isdirty"><span>is<wbr/>Dirty</span><a href="#isdirty" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">isDirty</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">null</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">boolean</span></div><aside class="tsd-sources"><p>Inherited from ObjectToLower.isDirty</p></aside></section><section class="tsd-panel tsd-member tsd-is-inherited"><h3 class="tsd-anchor-link" id="name"><span>name</span><a href="#name" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">name</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span></div><aside class="tsd-sources"><p>Inherited from ObjectToLower.name</p></aside></section><section class="tsd-panel tsd-member tsd-is-inherited"><h3 class="tsd-anchor-link" id="serializedsourceguild"><span>serialized<wbr/>Source<wbr/>Guild</span><a href="#serializedsourceguild" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">serializedSourceGuild</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>    <span class="tsd-kind-property">afkChannelId</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">null</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">string</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-property">afkTimeout</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">1800</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">3600</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">60</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">300</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">900</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-property">channels</span><span class="tsd-signature-symbol">?:</span> (<br/>        <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-symbol">{</span><br/>            <span class="tsd-kind-property">flags</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">AddUndefinedToPossiblyUndefinedPropertiesOfInterface</span><span class="tsd-signature-symbol">&lt;</span><br/>                <span class="tsd-signature-type">undefined</span><br/>                <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">ChannelFlags</span><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">id</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">string</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">name</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">nsfw</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">AddUndefinedToPossiblyUndefinedPropertiesOfInterface</span><span class="tsd-signature-symbol">&lt;</span><br/>                <span class="tsd-signature-type">undefined</span><br/>                <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">parentId</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">null</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">string</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">permissionOverwrites</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-symbol">{</span><br/>                <span class="tsd-kind-property">allow</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">null</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-kind-property">deny</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">null</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-kind-property">id</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-kind-property">type</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">OverwriteType</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">position</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">AddUndefinedToPossiblyUndefinedPropertiesOfInterface</span><span class="tsd-signature-symbol">&lt;</span><br/>                <span class="tsd-signature-type">undefined</span><br/>                <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">type</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">AddUndefinedToPossiblyUndefinedPropertiesOfInterface</span><span class="tsd-signature-symbol">&lt;</span><br/>                <span class="tsd-signature-type">undefined</span><br/>                <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">GuildCategory</span><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-signature-symbol">}</span><br/>        <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-symbol">{</span><br/>            <span class="tsd-kind-property">availableTags</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-symbol">{</span><br/>                <span class="tsd-kind-property">emojiId</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">AddUndefinedToPossiblyUndefinedPropertiesOfInterface</span><span class="tsd-signature-symbol">&lt;</span><br/>                    <span class="tsd-signature-type">null</span><br/>                    <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">,</span><br/>                <span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-kind-property">emojiName</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">AddUndefinedToPossiblyUndefinedPropertiesOfInterface</span><span class="tsd-signature-symbol">&lt;</span><br/>                    <span class="tsd-signature-type">null</span><br/>                    <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">,</span><br/>                <span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-kind-property">id</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-kind-property">moderated</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">AddUndefinedToPossiblyUndefinedPropertiesOfInterface</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-kind-property">name</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">defaultAutoArchiveDuration</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">AddUndefinedToPossiblyUndefinedPropertiesOfInterface</span><span class="tsd-signature-symbol">&lt;</span><br/>                <span class="tsd-signature-type">undefined</span><br/>                <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">ThreadAutoArchiveDuration</span><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">defaultForumLayout</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">AddUndefinedToPossiblyUndefinedPropertiesOfInterface</span><span class="tsd-signature-symbol">&lt;</span><br/>                <span class="tsd-signature-type">undefined</span><br/>                <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">ForumLayoutType</span><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">defaultReactionEmoji</span><span class="tsd-signature-symbol">?:</span><br/>                <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">null</span><br/>                <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-symbol">{</span><br/>                    <span class="tsd-kind-property">emojiId</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">AddUndefinedToPossiblyUndefinedPropertiesOfInterface</span><span class="tsd-signature-symbol">&lt;</span><br/>                        <span class="tsd-signature-type">null</span><br/>                        <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">,</span><br/>                    <span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>                    <span class="tsd-kind-property">emojiName</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">AddUndefinedToPossiblyUndefinedPropertiesOfInterface</span><span class="tsd-signature-symbol">&lt;</span><br/>                        <span class="tsd-signature-type">null</span><br/>                        <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">,</span><br/>                    <span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">defaultSortOrder</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">AddUndefinedToPossiblyUndefinedPropertiesOfInterface</span><span class="tsd-signature-symbol">&lt;</span><br/>                <span class="tsd-signature-type">undefined</span><br/>                <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">null</span><br/>                <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">SortOrderType</span><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">defaultThreadRateLimitPerUser</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">AddUndefinedToPossiblyUndefinedPropertiesOfInterface</span><span class="tsd-signature-symbol">&lt;</span><br/>                <span class="tsd-signature-type">undefined</span><br/>                <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">flags</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">AddUndefinedToPossiblyUndefinedPropertiesOfInterface</span><span class="tsd-signature-symbol">&lt;</span><br/>                <span class="tsd-signature-type">undefined</span><br/>                <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">ChannelFlags</span><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">id</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">string</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">name</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">nsfw</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">AddUndefinedToPossiblyUndefinedPropertiesOfInterface</span><span class="tsd-signature-symbol">&lt;</span><br/>                <span class="tsd-signature-type">undefined</span><br/>                <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">parentId</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">null</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">string</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">permissionOverwrites</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-symbol">{</span><br/>                <span class="tsd-kind-property">allow</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">null</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-kind-property">deny</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">null</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-kind-property">id</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-kind-property">type</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">OverwriteType</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">position</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">AddUndefinedToPossiblyUndefinedPropertiesOfInterface</span><span class="tsd-signature-symbol">&lt;</span><br/>                <span class="tsd-signature-type">undefined</span><br/>                <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">rateLimitPerUser</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">AddUndefinedToPossiblyUndefinedPropertiesOfInterface</span><span class="tsd-signature-symbol">&lt;</span><br/>                <span class="tsd-signature-type">undefined</span><br/>                <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">topic</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">AddUndefinedToPossiblyUndefinedPropertiesOfInterface</span><span class="tsd-signature-symbol">&lt;</span><br/>                <span class="tsd-signature-type">undefined</span><br/>                <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">null</span><br/>                <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">type</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">AddUndefinedToPossiblyUndefinedPropertiesOfInterface</span><span class="tsd-signature-symbol">&lt;</span><br/>                <span class="tsd-signature-type">undefined</span><br/>                <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">GuildForum</span><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-signature-symbol">}</span><br/>        <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-symbol">{</span><br/>            <span class="tsd-kind-property">availableTags</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-symbol">{</span><br/>                <span class="tsd-kind-property">emojiId</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">AddUndefinedToPossiblyUndefinedPropertiesOfInterface</span><span class="tsd-signature-symbol">&lt;</span><br/>                    <span class="tsd-signature-type">null</span><br/>                    <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">,</span><br/>                <span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-kind-property">emojiName</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">AddUndefinedToPossiblyUndefinedPropertiesOfInterface</span><span class="tsd-signature-symbol">&lt;</span><br/>                    <span class="tsd-signature-type">null</span><br/>                    <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">,</span><br/>                <span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-kind-property">id</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-kind-property">moderated</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">AddUndefinedToPossiblyUndefinedPropertiesOfInterface</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-kind-property">name</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">defaultAutoArchiveDuration</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">AddUndefinedToPossiblyUndefinedPropertiesOfInterface</span><span class="tsd-signature-symbol">&lt;</span><br/>                <span class="tsd-signature-type">undefined</span><br/>                <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">ThreadAutoArchiveDuration</span><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">defaultReactionEmoji</span><span class="tsd-signature-symbol">?:</span><br/>                <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">null</span><br/>                <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-symbol">{</span><br/>                    <span class="tsd-kind-property">emojiId</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">AddUndefinedToPossiblyUndefinedPropertiesOfInterface</span><span class="tsd-signature-symbol">&lt;</span><br/>                        <span class="tsd-signature-type">null</span><br/>                        <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">,</span><br/>                    <span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>                    <span class="tsd-kind-property">emojiName</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">AddUndefinedToPossiblyUndefinedPropertiesOfInterface</span><span class="tsd-signature-symbol">&lt;</span><br/>                        <span class="tsd-signature-type">null</span><br/>                        <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">,</span><br/>                    <span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">defaultSortOrder</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">AddUndefinedToPossiblyUndefinedPropertiesOfInterface</span><span class="tsd-signature-symbol">&lt;</span><br/>                <span class="tsd-signature-type">undefined</span><br/>                <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">null</span><br/>                <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">SortOrderType</span><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">defaultThreadRateLimitPerUser</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">AddUndefinedToPossiblyUndefinedPropertiesOfInterface</span><span class="tsd-signature-symbol">&lt;</span><br/>                <span class="tsd-signature-type">undefined</span><br/>                <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">flags</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">AddUndefinedToPossiblyUndefinedPropertiesOfInterface</span><span class="tsd-signature-symbol">&lt;</span><br/>                <span class="tsd-signature-type">undefined</span><br/>                <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">ChannelFlags</span><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">id</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">string</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">name</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">nsfw</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">AddUndefinedToPossiblyUndefinedPropertiesOfInterface</span><span class="tsd-signature-symbol">&lt;</span><br/>                <span class="tsd-signature-type">undefined</span><br/>                <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">parentId</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">null</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">string</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">permissionOverwrites</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-symbol">{</span><br/>                <span class="tsd-kind-property">allow</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">null</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-kind-property">deny</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">null</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-kind-property">id</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-kind-property">type</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">OverwriteType</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">position</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">AddUndefinedToPossiblyUndefinedPropertiesOfInterface</span><span class="tsd-signature-symbol">&lt;</span><br/>                <span class="tsd-signature-type">undefined</span><br/>                <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">rateLimitPerUser</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">AddUndefinedToPossiblyUndefinedPropertiesOfInterface</span><span class="tsd-signature-symbol">&lt;</span><br/>                <span class="tsd-signature-type">undefined</span><br/>                <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">topic</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">AddUndefinedToPossiblyUndefinedPropertiesOfInterface</span><span class="tsd-signature-symbol">&lt;</span><br/>                <span class="tsd-signature-type">undefined</span><br/>                <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">null</span><br/>                <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">type</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">AddUndefinedToPossiblyUndefinedPropertiesOfInterface</span><span class="tsd-signature-symbol">&lt;</span><br/>                <span class="tsd-signature-type">undefined</span><br/>                <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">GuildMedia</span><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-signature-symbol">}</span><br/>        <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-symbol">{</span><br/>            <span class="tsd-kind-property">bitrate</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">AddUndefinedToPossiblyUndefinedPropertiesOfInterface</span><span class="tsd-signature-symbol">&lt;</span><br/>                <span class="tsd-signature-type">undefined</span><br/>                <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">flags</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">AddUndefinedToPossiblyUndefinedPropertiesOfInterface</span><span class="tsd-signature-symbol">&lt;</span><br/>                <span class="tsd-signature-type">undefined</span><br/>                <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">ChannelFlags</span><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">id</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">string</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">name</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">nsfw</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">AddUndefinedToPossiblyUndefinedPropertiesOfInterface</span><span class="tsd-signature-symbol">&lt;</span><br/>                <span class="tsd-signature-type">undefined</span><br/>                <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">parentId</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">null</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">string</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">permissionOverwrites</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-symbol">{</span><br/>                <span class="tsd-kind-property">allow</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">null</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-kind-property">deny</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">null</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-kind-property">id</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-kind-property">type</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">OverwriteType</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">position</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">AddUndefinedToPossiblyUndefinedPropertiesOfInterface</span><span class="tsd-signature-symbol">&lt;</span><br/>                <span class="tsd-signature-type">undefined</span><br/>                <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">rateLimitPerUser</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">AddUndefinedToPossiblyUndefinedPropertiesOfInterface</span><span class="tsd-signature-symbol">&lt;</span><br/>                <span class="tsd-signature-type">undefined</span><br/>                <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">rtcRegion</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">AddUndefinedToPossiblyUndefinedPropertiesOfInterface</span><span class="tsd-signature-symbol">&lt;</span><br/>                <span class="tsd-signature-type">undefined</span><br/>                <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">null</span><br/>                <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">type</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">AddUndefinedToPossiblyUndefinedPropertiesOfInterface</span><span class="tsd-signature-symbol">&lt;</span><br/>                <span class="tsd-signature-type">undefined</span><br/>                <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">GuildStageVoice</span><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">userLimit</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">AddUndefinedToPossiblyUndefinedPropertiesOfInterface</span><span class="tsd-signature-symbol">&lt;</span><br/>                <span class="tsd-signature-type">undefined</span><br/>                <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">videoQualityMode</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">AddUndefinedToPossiblyUndefinedPropertiesOfInterface</span><span class="tsd-signature-symbol">&lt;</span><br/>                <span class="tsd-signature-type">undefined</span><br/>                <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">VideoQualityMode</span><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-signature-symbol">}</span><br/>        <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-symbol">{</span><br/>            <span class="tsd-kind-property">bitrate</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">AddUndefinedToPossiblyUndefinedPropertiesOfInterface</span><span class="tsd-signature-symbol">&lt;</span><br/>                <span class="tsd-signature-type">undefined</span><br/>                <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">flags</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">AddUndefinedToPossiblyUndefinedPropertiesOfInterface</span><span class="tsd-signature-symbol">&lt;</span><br/>                <span class="tsd-signature-type">undefined</span><br/>                <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">ChannelFlags</span><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">id</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">string</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">name</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">nsfw</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">AddUndefinedToPossiblyUndefinedPropertiesOfInterface</span><span class="tsd-signature-symbol">&lt;</span><br/>                <span class="tsd-signature-type">undefined</span><br/>                <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">parentId</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">null</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">string</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">permissionOverwrites</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-symbol">{</span><br/>                <span class="tsd-kind-property">allow</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">null</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-kind-property">deny</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">null</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-kind-property">id</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-kind-property">type</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">OverwriteType</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">position</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">AddUndefinedToPossiblyUndefinedPropertiesOfInterface</span><span class="tsd-signature-symbol">&lt;</span><br/>                <span class="tsd-signature-type">undefined</span><br/>                <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">rateLimitPerUser</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">AddUndefinedToPossiblyUndefinedPropertiesOfInterface</span><span class="tsd-signature-symbol">&lt;</span><br/>                <span class="tsd-signature-type">undefined</span><br/>                <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">rtcRegion</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">AddUndefinedToPossiblyUndefinedPropertiesOfInterface</span><span class="tsd-signature-symbol">&lt;</span><br/>                <span class="tsd-signature-type">undefined</span><br/>                <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">null</span><br/>                <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">type</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">AddUndefinedToPossiblyUndefinedPropertiesOfInterface</span><span class="tsd-signature-symbol">&lt;</span><br/>                <span class="tsd-signature-type">undefined</span><br/>                <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">GuildVoice</span><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">userLimit</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">AddUndefinedToPossiblyUndefinedPropertiesOfInterface</span><span class="tsd-signature-symbol">&lt;</span><br/>                <span class="tsd-signature-type">undefined</span><br/>                <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">videoQualityMode</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">AddUndefinedToPossiblyUndefinedPropertiesOfInterface</span><span class="tsd-signature-symbol">&lt;</span><br/>                <span class="tsd-signature-type">undefined</span><br/>                <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">VideoQualityMode</span><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-signature-symbol">}</span><br/>        <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-symbol">{</span><br/>            <span class="tsd-kind-property">defaultAutoArchiveDuration</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">AddUndefinedToPossiblyUndefinedPropertiesOfInterface</span><span class="tsd-signature-symbol">&lt;</span><br/>                <span class="tsd-signature-type">undefined</span><br/>                <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">ThreadAutoArchiveDuration</span><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">defaultThreadRateLimitPerUser</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">AddUndefinedToPossiblyUndefinedPropertiesOfInterface</span><span class="tsd-signature-symbol">&lt;</span><br/>                <span class="tsd-signature-type">undefined</span><br/>                <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">flags</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">AddUndefinedToPossiblyUndefinedPropertiesOfInterface</span><span class="tsd-signature-symbol">&lt;</span><br/>                <span class="tsd-signature-type">undefined</span><br/>                <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">ChannelFlags</span><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">id</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">string</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">name</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">nsfw</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">AddUndefinedToPossiblyUndefinedPropertiesOfInterface</span><span class="tsd-signature-symbol">&lt;</span><br/>                <span class="tsd-signature-type">undefined</span><br/>                <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">parentId</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">null</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">string</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">permissionOverwrites</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-symbol">{</span><br/>                <span class="tsd-kind-property">allow</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">null</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-kind-property">deny</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">null</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-kind-property">id</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-kind-property">type</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">OverwriteType</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">position</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">AddUndefinedToPossiblyUndefinedPropertiesOfInterface</span><span class="tsd-signature-symbol">&lt;</span><br/>                <span class="tsd-signature-type">undefined</span><br/>                <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">rateLimitPerUser</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">AddUndefinedToPossiblyUndefinedPropertiesOfInterface</span><span class="tsd-signature-symbol">&lt;</span><br/>                <span class="tsd-signature-type">undefined</span><br/>                <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">topic</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">AddUndefinedToPossiblyUndefinedPropertiesOfInterface</span><span class="tsd-signature-symbol">&lt;</span><br/>                <span class="tsd-signature-type">undefined</span><br/>                <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">null</span><br/>                <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">type</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">AddUndefinedToPossiblyUndefinedPropertiesOfInterface</span><span class="tsd-signature-symbol">&lt;</span><br/>                <span class="tsd-signature-type">undefined</span><br/>                <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">GuildAnnouncement</span><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-signature-symbol">}</span><br/>        <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-symbol">{</span><br/>            <span class="tsd-kind-property">defaultAutoArchiveDuration</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">AddUndefinedToPossiblyUndefinedPropertiesOfInterface</span><span class="tsd-signature-symbol">&lt;</span><br/>                <span class="tsd-signature-type">undefined</span><br/>                <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">ThreadAutoArchiveDuration</span><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">defaultThreadRateLimitPerUser</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">AddUndefinedToPossiblyUndefinedPropertiesOfInterface</span><span class="tsd-signature-symbol">&lt;</span><br/>                <span class="tsd-signature-type">undefined</span><br/>                <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">flags</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">AddUndefinedToPossiblyUndefinedPropertiesOfInterface</span><span class="tsd-signature-symbol">&lt;</span><br/>                <span class="tsd-signature-type">undefined</span><br/>                <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">ChannelFlags</span><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">id</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">string</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">name</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">nsfw</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">AddUndefinedToPossiblyUndefinedPropertiesOfInterface</span><span class="tsd-signature-symbol">&lt;</span><br/>                <span class="tsd-signature-type">undefined</span><br/>                <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">parentId</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">null</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">string</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">permissionOverwrites</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-symbol">{</span><br/>                <span class="tsd-kind-property">allow</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">null</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-kind-property">deny</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">null</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-kind-property">id</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-kind-property">type</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">OverwriteType</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">position</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">AddUndefinedToPossiblyUndefinedPropertiesOfInterface</span><span class="tsd-signature-symbol">&lt;</span><br/>                <span class="tsd-signature-type">undefined</span><br/>                <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">rateLimitPerUser</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">AddUndefinedToPossiblyUndefinedPropertiesOfInterface</span><span class="tsd-signature-symbol">&lt;</span><br/>                <span class="tsd-signature-type">undefined</span><br/>                <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">topic</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">AddUndefinedToPossiblyUndefinedPropertiesOfInterface</span><span class="tsd-signature-symbol">&lt;</span><br/>                <span class="tsd-signature-type">undefined</span><br/>                <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">null</span><br/>                <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">type</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">AddUndefinedToPossiblyUndefinedPropertiesOfInterface</span><span class="tsd-signature-symbol">&lt;</span><br/>                <span class="tsd-signature-type">undefined</span><br/>                <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">GuildText</span><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-signature-symbol">}</span><br/>        <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-symbol">{</span><br/>            <span class="tsd-kind-property">flags</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">AddUndefinedToPossiblyUndefinedPropertiesOfInterface</span><span class="tsd-signature-symbol">&lt;</span><br/>                <span class="tsd-signature-type">undefined</span><br/>                <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">ChannelFlags</span><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">id</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">string</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">name</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">nsfw</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">AddUndefinedToPossiblyUndefinedPropertiesOfInterface</span><span class="tsd-signature-symbol">&lt;</span><br/>                <span class="tsd-signature-type">undefined</span><br/>                <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">parentId</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">null</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">string</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">permissionOverwrites</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-symbol">{</span><br/>                <span class="tsd-kind-property">allow</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">null</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-kind-property">deny</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">null</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-kind-property">id</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-kind-property">type</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">OverwriteType</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">position</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">AddUndefinedToPossiblyUndefinedPropertiesOfInterface</span><span class="tsd-signature-symbol">&lt;</span><br/>                <span class="tsd-signature-type">undefined</span><br/>                <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">rateLimitPerUser</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">AddUndefinedToPossiblyUndefinedPropertiesOfInterface</span><span class="tsd-signature-symbol">&lt;</span><br/>                <span class="tsd-signature-type">undefined</span><br/>                <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">type</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">AddUndefinedToPossiblyUndefinedPropertiesOfInterface</span><span class="tsd-signature-symbol">&lt;</span><br/>                <span class="tsd-signature-type">undefined</span><br/>                <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">ThreadChannelType</span><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-signature-symbol">}</span><br/>    )<span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-property">defaultMessageNotifications</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">GuildDefaultMessageNotifications</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-property">description</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">null</span><br/>    <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-property">explicitContentFilter</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">GuildExplicitContentFilter</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-property">iconHash</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">null</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-property">name</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-property">preferredLocale</span><span class="tsd-signature-symbol">:</span><br/>        <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;id&quot;</span><br/>        <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;en-US&quot;</span><br/>        <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;en-GB&quot;</span><br/>        <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;bg&quot;</span><br/>        <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;zh-CN&quot;</span><br/>        <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;zh-TW&quot;</span><br/>        <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;hr&quot;</span><br/>        <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;cs&quot;</span><br/>        <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;da&quot;</span><br/>        <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;nl&quot;</span><br/>        <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;fi&quot;</span><br/>        <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;fr&quot;</span><br/>        <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;de&quot;</span><br/>        <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;el&quot;</span><br/>        <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;hi&quot;</span><br/>        <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;hu&quot;</span><br/>        <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;it&quot;</span><br/>        <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;ja&quot;</span><br/>        <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;ko&quot;</span><br/>        <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;lt&quot;</span><br/>        <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;no&quot;</span><br/>        <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;pl&quot;</span><br/>        <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;pt-BR&quot;</span><br/>        <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;ro&quot;</span><br/>        <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;ru&quot;</span><br/>        <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;es-ES&quot;</span><br/>        <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;es-419&quot;</span><br/>        <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;sv-SE&quot;</span><br/>        <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;th&quot;</span><br/>        <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;tr&quot;</span><br/>        <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;uk&quot;</span><br/>        <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;vi&quot;</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-property">premiumProgressBarEnabled</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-property">region</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-property">roles</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-symbol">{</span><br/>        <span class="tsd-kind-property">color</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">null</span><br/>        <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-property">hoist</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">null</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-property">icon</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">null</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-property">id</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-property">mentionable</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">null</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-property">name</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">null</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-property">permissions</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">null</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-property">unicodeEmoji</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">null</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-property">systemChannelFlags</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">GuildSystemChannelFlags</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-property">systemChannelId</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">null</span><br/>    <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">string</span><br/>    <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-property">verificationLevel</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">GuildVerificationLevel</span><span class="tsd-signature-symbol">;</span><br/><span class="tsd-signature-symbol">}</span></div><aside class="tsd-sources"><p>Inherited from ObjectToLower.serializedSourceGuild</p></aside></section><section class="tsd-panel tsd-member tsd-is-inherited"><h3 class="tsd-anchor-link" id="sourceguildid"><span>source<wbr/>Guild<wbr/>Id</span><a href="#sourceguildid" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">sourceGuildId</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span></div><aside class="tsd-sources"><p>Inherited from ObjectToLower.sourceGuildId</p></aside></section><section class="tsd-panel tsd-member tsd-is-inherited"><h3 class="tsd-anchor-link" id="updatedat"><span>updated<wbr/>At</span><a href="#updatedat" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">updatedAt</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span></div><aside class="tsd-sources"><p>Inherited from ObjectToLower.updatedAt</p></aside></section><section class="tsd-panel tsd-member tsd-is-inherited"><h3 class="tsd-anchor-link" id="usagecount"><span>usage<wbr/>Count</span><a href="#usagecount" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">usageCount</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">number</span></div><aside class="tsd-sources"><p>Inherited from ObjectToLower.usageCount</p></aside></section></section></details><details class="tsd-panel-group tsd-member-group tsd-accordion" open><summary class="tsd-accordion-summary" data-key="section-Methods"><svg width="20" height="20" viewBox="0 0 24 24" fill="none" aria-hidden="true"><use href="../assets/icons.svg#icon-chevronDown"></use></svg><h2>Methods</h2></summary><section><section class="tsd-panel tsd-member"><h3 class="tsd-anchor-link" id="delete"><span>delete</span><a href="#delete" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><ul class="tsd-signatures"><li class=""><div class="tsd-signature tsd-anchor-link" id="delete-1"><span class="tsd-kind-call-signature">delete</span><span class="tsd-signature-symbol">()</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="" class="tsd-signature-type tsd-kind-class">GuildTemplate</a><span class="tsd-signature-symbol">&gt;</span><a href="#delete-1" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></div><div class="tsd-description"><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="" class="tsd-signature-type tsd-kind-class">GuildTemplate</a><span class="tsd-signature-symbol">&gt;</span></h4><aside class="tsd-sources"><ul><li>Defined in <a href="https://github.com/tiramisulabs/seyfert//blob/9ae4290ef220ffc96449d469d16d76b2031eca69/src/structures/GuildTemplate.ts#L42">src/structures/GuildTemplate.ts:42</a></li></ul></aside></div></li></ul></section><section class="tsd-panel tsd-member"><h3 class="tsd-anchor-link" id="edit"><span>edit</span><a href="#edit" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><ul class="tsd-signatures"><li class=""><div class="tsd-signature tsd-anchor-link" id="edit-1"><span class="tsd-kind-call-signature">edit</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">body</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">RESTPatchAPIGuildTemplateJSONBody</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="" class="tsd-signature-type tsd-kind-class">GuildTemplate</a><span class="tsd-signature-symbol">&gt;</span><a href="#edit-1" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></div><div class="tsd-description"><div class="tsd-parameters"><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameter-list"><li><span><span class="tsd-kind-parameter">body</span>: <span class="tsd-signature-type">RESTPatchAPIGuildTemplateJSONBody</span></span></li></ul></div><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="" class="tsd-signature-type tsd-kind-class">GuildTemplate</a><span class="tsd-signature-symbol">&gt;</span></h4><aside class="tsd-sources"><ul><li>Defined in <a href="https://github.com/tiramisulabs/seyfert//blob/9ae4290ef220ffc96449d469d16d76b2031eca69/src/structures/GuildTemplate.ts#L38">src/structures/GuildTemplate.ts:38</a></li></ul></aside></div></li></ul></section><section class="tsd-panel tsd-member"><h3 class="tsd-anchor-link" id="fetch"><span>fetch</span><a href="#fetch" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><ul class="tsd-signatures"><li class=""><div class="tsd-signature tsd-anchor-link" id="fetch-1"><span class="tsd-kind-call-signature">fetch</span><span class="tsd-signature-symbol">()</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="" class="tsd-signature-type tsd-kind-class">GuildTemplate</a><span class="tsd-signature-symbol">&gt;</span><a href="#fetch-1" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></div><div class="tsd-description"><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="" class="tsd-signature-type tsd-kind-class">GuildTemplate</a><span class="tsd-signature-symbol">&gt;</span></h4><aside class="tsd-sources"><ul><li>Defined in <a href="https://github.com/tiramisulabs/seyfert//blob/9ae4290ef220ffc96449d469d16d76b2031eca69/src/structures/GuildTemplate.ts#L30">src/structures/GuildTemplate.ts:30</a></li></ul></aside></div></li></ul></section><section class="tsd-panel tsd-member"><h3 class="tsd-anchor-link" id="guild"><span>guild</span><a href="#guild" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><ul class="tsd-signatures"><li class=""><div class="tsd-signature tsd-anchor-link" id="guild-1"><span class="tsd-kind-call-signature">guild</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">mode</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">&quot;rest&quot;</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;flow&quot;</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="Guild.html" class="tsd-signature-type tsd-kind-class">Guild</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">&quot;cached&quot;</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;api&quot;</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">&gt;</span><a href="#guild-1" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></div><div class="tsd-description"><div class="tsd-parameters"><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameter-list"><li><span><code class="tsd-tag">Optional</code><span class="tsd-kind-parameter">mode</span>: <span class="tsd-signature-type">&quot;rest&quot;</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;flow&quot;</span></span></li></ul></div><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="Guild.html" class="tsd-signature-type tsd-kind-class">Guild</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">&quot;cached&quot;</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;api&quot;</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">&gt;</span></h4><aside class="tsd-sources"><ul><li>Defined in <a href="https://github.com/tiramisulabs/seyfert//blob/9ae4290ef220ffc96449d469d16d76b2031eca69/src/structures/GuildTemplate.ts#L16">src/structures/GuildTemplate.ts:16</a></li></ul></aside></div></li><li class=""><div class="tsd-signature tsd-anchor-link" id="guild-2"><span class="tsd-kind-call-signature">guild</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">mode</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">&quot;cache&quot;</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <a href="../types/ReturnCache.html" class="tsd-signature-type tsd-kind-type-alias">ReturnCache</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">undefined</span> <span class="tsd-signature-symbol">|</span> <a href="Guild.html" class="tsd-signature-type tsd-kind-class">Guild</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">&quot;cached&quot;</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">&gt;</span><a href="#guild-2" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></div><div class="tsd-description"><div class="tsd-parameters"><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameter-list"><li><span><span class="tsd-kind-parameter">mode</span>: <span class="tsd-signature-type">&quot;cache&quot;</span></span></li></ul></div><h4 class="tsd-returns-title">Returns <a href="../types/ReturnCache.html" class="tsd-signature-type tsd-kind-type-alias">ReturnCache</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">undefined</span> <span class="tsd-signature-symbol">|</span> <a href="Guild.html" class="tsd-signature-type tsd-kind-class">Guild</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">&quot;cached&quot;</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">&gt;</span></h4><aside class="tsd-sources"><ul><li>Defined in <a href="https://github.com/tiramisulabs/seyfert//blob/9ae4290ef220ffc96449d469d16d76b2031eca69/src/structures/GuildTemplate.ts#L17">src/structures/GuildTemplate.ts:17</a></li></ul></aside></div></li></ul></section><section class="tsd-panel tsd-member"><h3 class="tsd-anchor-link" id="sync"><span>sync</span><a href="#sync" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><ul class="tsd-signatures"><li class=""><div class="tsd-signature tsd-anchor-link" id="sync-1"><span class="tsd-kind-call-signature">sync</span><span class="tsd-signature-symbol">()</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="" class="tsd-signature-type tsd-kind-class">GuildTemplate</a><span class="tsd-signature-symbol">&gt;</span><a href="#sync-1" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></div><div class="tsd-description"><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="" class="tsd-signature-type tsd-kind-class">GuildTemplate</a><span class="tsd-signature-symbol">&gt;</span></h4><aside class="tsd-sources"><ul><li>Defined in <a href="https://github.com/tiramisulabs/seyfert//blob/9ae4290ef220ffc96449d469d16d76b2031eca69/src/structures/GuildTemplate.ts#L34">src/structures/GuildTemplate.ts:34</a></li></ul></aside></div></li></ul></section><section class="tsd-panel tsd-member"><h3 class="tsd-anchor-link" id="methods"><code class="tsd-tag">Static</code><span>methods</span><a href="#methods" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><ul class="tsd-signatures"><li class=""><div class="tsd-signature tsd-anchor-link" id="methods-1"><span class="tsd-kind-call-signature">methods</span><span class="tsd-signature-symbol">(</span><br/>    <span class="tsd-kind-parameter">ctx</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span> <span class="tsd-kind-property">client</span><span class="tsd-signature-symbol">:</span> <a href="../interfaces/UsingClient.html" class="tsd-signature-type tsd-kind-interface">UsingClient</a><span class="tsd-signature-symbol">;</span> <span class="tsd-kind-property">guildId</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span> <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">,</span><br/><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>    <span class="tsd-kind-property">create</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">(</span><br/>        <span class="tsd-kind-parameter">body</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">RESTPostAPIGuildTemplatesJSONBody</span><span class="tsd-signature-symbol">,</span><br/>    <span class="tsd-signature-symbol">)</span> <span class="tsd-signature-symbol">=&gt;</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="" class="tsd-signature-type tsd-kind-class">GuildTemplate</a><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-property">delete</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">code</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span> <span class="tsd-signature-symbol">=&gt;</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="" class="tsd-signature-type tsd-kind-class">GuildTemplate</a><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-property">edit</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">(</span><br/>        <span class="tsd-kind-parameter">code</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">,</span><br/>        <span class="tsd-kind-parameter">body</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">RESTPatchAPIGuildTemplateJSONBody</span><span class="tsd-signature-symbol">,</span><br/>    <span class="tsd-signature-symbol">)</span> <span class="tsd-signature-symbol">=&gt;</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="" class="tsd-signature-type tsd-kind-class">GuildTemplate</a><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-property">fetch</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">code</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span> <span class="tsd-signature-symbol">=&gt;</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="" class="tsd-signature-type tsd-kind-class">GuildTemplate</a><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-property">list</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">()</span> <span class="tsd-signature-symbol">=&gt;</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="" class="tsd-signature-type tsd-kind-class">GuildTemplate</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-property">sync</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">code</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span> <span class="tsd-signature-symbol">=&gt;</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="" class="tsd-signature-type tsd-kind-class">GuildTemplate</a><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/><span class="tsd-signature-symbol">}</span><a href="#methods-1" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></div><div class="tsd-description"><div class="tsd-parameters"><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameter-list"><li><span><span class="tsd-kind-parameter">ctx</span>: <span class="tsd-signature-symbol">{</span> <span class="tsd-kind-property">client</span><span class="tsd-signature-symbol">:</span> <a href="../interfaces/UsingClient.html" class="tsd-signature-type tsd-kind-interface">UsingClient</a><span class="tsd-signature-symbol">;</span> <span class="tsd-kind-property">guildId</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span> <span class="tsd-signature-symbol">}</span></span></li></ul></div><h4 class="tsd-returns-title">Returns <span class="tsd-signature-symbol">{</span><br/>    <span class="tsd-kind-property">create</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">(</span><br/>        <span class="tsd-kind-parameter">body</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">RESTPostAPIGuildTemplatesJSONBody</span><span class="tsd-signature-symbol">,</span><br/>    <span class="tsd-signature-symbol">)</span> <span class="tsd-signature-symbol">=&gt;</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="" class="tsd-signature-type tsd-kind-class">GuildTemplate</a><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-property">delete</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">code</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span> <span class="tsd-signature-symbol">=&gt;</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="" class="tsd-signature-type tsd-kind-class">GuildTemplate</a><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-property">edit</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">(</span><br/>        <span class="tsd-kind-parameter">code</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">,</span><br/>        <span class="tsd-kind-parameter">body</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">RESTPatchAPIGuildTemplateJSONBody</span><span class="tsd-signature-symbol">,</span><br/>    <span class="tsd-signature-symbol">)</span> <span class="tsd-signature-symbol">=&gt;</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="" class="tsd-signature-type tsd-kind-class">GuildTemplate</a><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-property">fetch</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">code</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span> <span class="tsd-signature-symbol">=&gt;</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="" class="tsd-signature-type tsd-kind-class">GuildTemplate</a><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-property">list</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">()</span> <span class="tsd-signature-symbol">=&gt;</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="" class="tsd-signature-type tsd-kind-class">GuildTemplate</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-property">sync</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">code</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span> <span class="tsd-signature-symbol">=&gt;</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="" class="tsd-signature-type tsd-kind-class">GuildTemplate</a><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/><span class="tsd-signature-symbol">}</span></h4><aside class="tsd-sources"><ul><li>Defined in <a href="https://github.com/tiramisulabs/seyfert//blob/9ae4290ef220ffc96449d469d16d76b2031eca69/src/structures/GuildTemplate.ts#L46">src/structures/GuildTemplate.ts:46</a></li></ul></aside></div></li></ul></section></section></details></div><div class="col-sidebar"><div class="page-menu"><div class="tsd-navigation settings"><details class="tsd-accordion"><summary class="tsd-accordion-summary"><svg width="20" height="20" viewBox="0 0 24 24" fill="none" aria-hidden="true"><use href="../assets/icons.svg#icon-chevronDown"></use></svg><h3>Settings</h3></summary><div class="tsd-accordion-details"><div class="tsd-filter-visibility"><span class="settings-label">Member Visibility</span><ul id="tsd-filter-options"><li class="tsd-filter-item"><label class="tsd-filter-input"><input type="checkbox" id="tsd-filter-protected" name="protected"/><svg width="32" height="32" viewBox="0 0 32 32" aria-hidden="true"><rect class="tsd-checkbox-background" width="30" height="30" x="1" y="1" rx="6" fill="none"></rect><path class="tsd-checkbox-checkmark" d="M8.35422 16.8214L13.2143 21.75L24.6458 10.25" stroke="none" stroke-width="3.5" stroke-linejoin="round" fill="none"></path></svg><span>Protected</span></label></li><li class="tsd-filter-item"><label class="tsd-filter-input"><input type="checkbox" id="tsd-filter-inherited" name="inherited" checked/><svg width="32" height="32" viewBox="0 0 32 32" aria-hidden="true"><rect class="tsd-checkbox-background" width="30" height="30" x="1" y="1" rx="6" fill="none"></rect><path class="tsd-checkbox-checkmark" d="M8.35422 16.8214L13.2143 21.75L24.6458 10.25" stroke="none" stroke-width="3.5" stroke-linejoin="round" fill="none"></path></svg><span>Inherited</span></label></li><li class="tsd-filter-item"><label class="tsd-filter-input"><input type="checkbox" id="tsd-filter-external" name="external"/><svg width="32" height="32" viewBox="0 0 32 32" aria-hidden="true"><rect class="tsd-checkbox-background" width="30" height="30" x="1" y="1" rx="6" fill="none"></rect><path class="tsd-checkbox-checkmark" d="M8.35422 16.8214L13.2143 21.75L24.6458 10.25" stroke="none" stroke-width="3.5" stroke-linejoin="round" fill="none"></path></svg><span>External</span></label></li></ul></div><div class="tsd-theme-toggle"><label class="settings-label" for="tsd-theme">Theme</label><select id="tsd-theme"><option value="os">OS</option><option value="light">Light</option><option value="dark">Dark</option></select></div></div></details></div><details open class="tsd-accordion tsd-page-navigation"><summary class="tsd-accordion-summary"><svg width="20" height="20" viewBox="0 0 24 24" fill="none" aria-hidden="true"><use href="../assets/icons.svg#icon-chevronDown"></use></svg><h3>On This Page</h3></summary><div class="tsd-accordion-details"><details open class="tsd-accordion tsd-page-navigation-section"><summary class="tsd-accordion-summary" data-key="section-Constructors"><svg width="20" height="20" viewBox="0 0 24 24" fill="none" aria-hidden="true"><use href="../assets/icons.svg#icon-chevronDown"></use></svg>Constructors</summary><div><a href="#constructor"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Constructor"><use href="../assets/icons.svg#icon-512"></use></svg><span>constructor</span></a></div></details><details open class="tsd-accordion tsd-page-navigation-section"><summary class="tsd-accordion-summary" data-key="section-Properties"><svg width="20" height="20" viewBox="0 0 24 24" fill="none" aria-hidden="true"><use href="../assets/icons.svg#icon-chevronDown"></use></svg>Properties</summary><div><a href="#client" class="tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>client</span></a><a href="#code" class="tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>code</span></a><a href="#createdat" class="tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>created<wbr/>At</span></a><a href="#creator" class="tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>creator</span></a><a href="#creatorid" class="tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>creator<wbr/>Id</span></a><a href="#description" class="tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>description</span></a><a href="#isdirty" class="tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>is<wbr/>Dirty</span></a><a href="#name" class="tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>name</span></a><a href="#serializedsourceguild" class="tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>serialized<wbr/>Source<wbr/>Guild</span></a><a href="#sourceguildid" class="tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>source<wbr/>Guild<wbr/>Id</span></a><a href="#updatedat" class="tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>updated<wbr/>At</span></a><a href="#usagecount" class="tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>usage<wbr/>Count</span></a></div></details><details open class="tsd-accordion tsd-page-navigation-section"><summary class="tsd-accordion-summary" data-key="section-Methods"><svg width="20" height="20" viewBox="0 0 24 24" fill="none" aria-hidden="true"><use href="../assets/icons.svg#icon-chevronDown"></use></svg>Methods</summary><div><a href="#delete"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Method"><use href="../assets/icons.svg#icon-2048"></use></svg><span>delete</span></a><a href="#edit"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Method"><use href="../assets/icons.svg#icon-2048"></use></svg><span>edit</span></a><a href="#fetch"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Method"><use href="../assets/icons.svg#icon-2048"></use></svg><span>fetch</span></a><a href="#guild"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Method"><use href="../assets/icons.svg#icon-2048"></use></svg><span>guild</span></a><a href="#sync"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Method"><use href="../assets/icons.svg#icon-2048"></use></svg><span>sync</span></a><a href="#methods"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Method"><use href="../assets/icons.svg#icon-2048"></use></svg><span>methods</span></a></div></details></div></details></div><div class="site-menu"><nav class="tsd-navigation"><a href="../modules.html">seyfert</a><ul class="tsd-small-nested-navigation" id="tsd-nav-container"><li>Loading...</li></ul></nav></div></div></div><footer><p class="tsd-generator">Generated using <a href="https://typedoc.org/" target="_blank">TypeDoc</a></p></footer><div class="overlay"></div></body></html>
