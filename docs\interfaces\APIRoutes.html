<!DOCTYPE html><html class="default" lang="en" data-base="../"><head><meta charset="utf-8"/><meta http-equiv="x-ua-compatible" content="IE=edge"/><title>APIRoutes | seyfert</title><meta name="description" content="Documentation for seyfert"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="stylesheet" href="../assets/style.css"/><link rel="stylesheet" href="../assets/highlight.css"/><script defer src="../assets/main.js"></script><script async src="../assets/icons.js" id="tsd-icons-script"></script><script async src="../assets/search.js" id="tsd-search-script"></script><script async src="../assets/navigation.js" id="tsd-nav-script"></script><script async src="../assets/hierarchy.js" id="tsd-hierarchy-script"></script></head><body><script>document.documentElement.dataset.theme = localStorage.getItem("tsd-theme") || "os";document.body.style.display="none";setTimeout(() => window.app?app.showPage():document.body.style.removeProperty("display"),500)</script><header class="tsd-page-toolbar"><div class="tsd-toolbar-contents container"><a href="../index.html" class="title">seyfert</a><div id="tsd-toolbar-links"></div><button id="tsd-search-trigger" class="tsd-widget" aria-label="Search"><svg width="16" height="16" viewBox="0 0 16 16" fill="none" aria-hidden="true"><use href="../assets/icons.svg#icon-search"></use></svg></button><dialog id="tsd-search" aria-label="Search"><input role="combobox" id="tsd-search-input" aria-controls="tsd-search-results" aria-autocomplete="list" aria-expanded="true" autocapitalize="off" autocomplete="off" placeholder="Search the docs" maxLength="100"/><ul role="listbox" id="tsd-search-results"></ul><div id="tsd-search-status" aria-live="polite" aria-atomic="true"><div>Preparing search index...</div></div></dialog><a href="#" class="tsd-widget menu" id="tsd-toolbar-menu-trigger" data-toggle="menu" aria-label="Menu"><svg width="16" height="16" viewBox="0 0 16 16" fill="none" aria-hidden="true"><use href="../assets/icons.svg#icon-menu"></use></svg></a></div></header><div class="container container-main"><div class="col-content"><div class="tsd-page-title"><ul class="tsd-breadcrumb" aria-label="Breadcrumb"><li><a href="" aria-current="page">APIRoutes</a></li></ul><h1>Interface APIRoutes</h1></div><div class="tsd-signature"><span class="tsd-signature-keyword">interface</span> <span class="tsd-kind-interface">APIRoutes</span> <span class="tsd-signature-symbol">{</span><br/>    <a class="tsd-kind-property" href="#applications">applications</a><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">(</span><br/>        <span class="tsd-kind-parameter">id</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">,</span><br/>    <span class="tsd-signature-symbol">)</span> <span class="tsd-signature-symbol">=&gt;</span> <span class="tsd-signature-symbol">{</span><br/>        <span class="tsd-kind-property">&quot;activity-instances&quot;</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">(</span><br/>            <span class="tsd-kind-parameter">id</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">,</span><br/>        <span class="tsd-signature-symbol">)</span> <span class="tsd-signature-symbol">=&gt;</span> <span class="tsd-signature-symbol">{</span><br/>            <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><br/>                <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RestGetAPIApplicationActivityInstanceResult</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-property">commands</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>            <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><br/>                <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTGetAPIApplicationCommandsQuery</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTGetAPIApplicationCommandsResult</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-call-signature">post</span><span class="tsd-signature-symbol">(</span><br/>                <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <a href="../types/RestArguments.html" class="tsd-signature-type tsd-kind-type-alias">RestArguments</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTPostAPIApplicationCommandsJSONBody</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIApplicationCommand</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-call-signature">put</span><span class="tsd-signature-symbol">(</span><br/>                <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArguments.html" class="tsd-signature-type tsd-kind-type-alias">RestArguments</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTPutAPIApplicationCommandsJSONBody</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTPutAPIApplicationCommandsResult</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-signature-symbol">(</span><br/>                <span class="tsd-kind-parameter">id</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>                <span class="tsd-kind-call-signature">delete</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">undefined</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIApplicationCommand</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-kind-call-signature">patch</span><span class="tsd-signature-symbol">(</span><br/>                    <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <a href="../types/RestArguments.html" class="tsd-signature-type tsd-kind-type-alias">RestArguments</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTPatchAPIApplicationCommandJSONBody</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>                <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIApplicationCommand</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-property">emojis</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>            <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><br/>                <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">Pick</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIApplicationEmoji</span><span class="tsd-signature-symbol">,</span> <span class="tsd-signature-type">&quot;id&quot;</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTGetAPIApplicationEmojisResult</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-call-signature">post</span><span class="tsd-signature-symbol">(</span><br/>                <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArguments.html" class="tsd-signature-type tsd-kind-type-alias">RestArguments</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTPostAPIApplicationEmojiJSONBody</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIApplicationEmoji</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-signature-symbol">(</span><br/>                <span class="tsd-kind-parameter">id</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>                <span class="tsd-kind-call-signature">delete</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">undefined</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIApplicationEmoji</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-kind-call-signature">patch</span><span class="tsd-signature-symbol">(</span><br/>                    <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArguments.html" class="tsd-signature-type tsd-kind-type-alias">RestArguments</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTPatchAPIApplicationEmojiJSONBody</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>                <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIApplicationEmoji</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-property">entitlements</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>            <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><br/>                <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTGetAPIEntitlementsQuery</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTGetAPIEntitlementsResult</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-call-signature">post</span><span class="tsd-signature-symbol">(</span><br/>                <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <a href="../types/RestArguments.html" class="tsd-signature-type tsd-kind-type-alias">RestArguments</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTPostAPIEntitlementBody</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">Partial</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">Omit</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIEntitlement</span><span class="tsd-signature-symbol">,</span> <span class="tsd-signature-type">&quot;ends_at&quot;</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;starts_at&quot;</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-signature-symbol">(</span><br/>                <span class="tsd-kind-parameter">id</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>                <span class="tsd-kind-property">consume</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span> <span class="tsd-kind-call-signature">post</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">undefined</span><span class="tsd-signature-symbol">&gt;</span> <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-kind-call-signature">delete</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">undefined</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIEntitlement</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-property">guilds</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">(</span><br/>            <span class="tsd-kind-parameter">id</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">,</span><br/>        <span class="tsd-signature-symbol">)</span> <span class="tsd-signature-symbol">=&gt;</span> <span class="tsd-signature-symbol">{</span><br/>            <span class="tsd-kind-property">commands</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>                <span class="tsd-kind-property">permissions</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>                    <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><br/>                        <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">,</span><br/>                    <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTGetAPIGuildApplicationCommandsPermissionsResult</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><br/>                    <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTGetAPIApplicationCommandsQuery</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>                <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTGetAPIApplicationGuildCommandsResult</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-kind-call-signature">post</span><span class="tsd-signature-symbol">(</span><br/>                    <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <a href="../types/RestArguments.html" class="tsd-signature-type tsd-kind-type-alias">RestArguments</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTPostAPIApplicationGuildCommandsJSONBody</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>                <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTAPIApplicationGuildCommand</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-kind-call-signature">put</span><span class="tsd-signature-symbol">(</span><br/>                    <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArguments.html" class="tsd-signature-type tsd-kind-type-alias">RestArguments</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTPutAPIApplicationGuildCommandsJSONBody</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>                <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTPutAPIApplicationGuildCommandsResult</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-signature-symbol">(</span><br/>                    <span class="tsd-kind-parameter">id</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">,</span><br/>                <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>                    <span class="tsd-kind-property">permissions</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>                        <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><br/>                            <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">,</span><br/>                        <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTGetAPIGuildApplicationCommandsPermissionsResult</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>                        <span class="tsd-kind-call-signature">put</span><span class="tsd-signature-symbol">(</span><br/>                            <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArguments.html" class="tsd-signature-type tsd-kind-type-alias">RestArguments</a><span class="tsd-signature-symbol">&lt;</span><br/>                                <span class="tsd-signature-type">RESTPutAPIApplicationCommandPermissionsJSONBody</span><span class="tsd-signature-symbol">,</span><br/>                            <span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>                        <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTPutAPIGuildApplicationCommandsPermissionsResult</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>                    <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>                    <span class="tsd-kind-call-signature">delete</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">undefined</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>                    <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><br/>                        <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">,</span><br/>                    <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTAPIApplicationGuildCommand</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>                    <span class="tsd-kind-call-signature">patch</span><span class="tsd-signature-symbol">(</span><br/>                        <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <a href="../types/RestArguments.html" class="tsd-signature-type tsd-kind-type-alias">RestArguments</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTPatchAPIApplicationGuildCommandJSONBody</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>                    <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTAPIApplicationGuildCommand</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-property">&quot;role-connections&quot;</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>            <span class="tsd-kind-property">metadata</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>                <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><br/>                    <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">,</span><br/>                <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTGetAPIApplicationRoleConnectionMetadataResult</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-kind-call-signature">put</span><span class="tsd-signature-symbol">(</span><br/>                    <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <a href="../types/RestArguments.html" class="tsd-signature-type tsd-kind-type-alias">RestArguments</a><span class="tsd-signature-symbol">&lt;</span><br/>                        <span class="tsd-signature-type">RESTPutAPIApplicationRoleConnectionMetadataJSONBody</span><span class="tsd-signature-symbol">,</span><br/>                    <span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>                <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTPutAPIApplicationRoleConnectionMetadataResult</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-property">skus</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span> <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTGetAPISKUsResult</span><span class="tsd-signature-symbol">&gt;</span> <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIApplication</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-call-signature">patch</span><span class="tsd-signature-symbol">(</span><br/>            <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <a href="../types/RestArguments.html" class="tsd-signature-type tsd-kind-type-alias">RestArguments</a><span class="tsd-signature-symbol">&lt;</span><br/>                <span class="tsd-signature-type">AddUndefinedToPossiblyUndefinedPropertiesOfInterface</span><span class="tsd-signature-symbol">&lt;</span><br/>                    <span class="tsd-signature-type">Partial</span><span class="tsd-signature-symbol">&lt;</span><br/>                        <span class="tsd-signature-type">Nullable</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">Pick</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIApplication</span><span class="tsd-signature-symbol">,</span> <span class="tsd-signature-type">&quot;icon&quot;</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;cover_image&quot;</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">&gt;</span> <span class="tsd-signature-symbol">&amp;</span> <span class="tsd-signature-type">Pick</span><span class="tsd-signature-symbol">&lt;</span><br/>                            <span class="tsd-signature-type">APIApplication</span><span class="tsd-signature-symbol">,</span><br/>                            <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;description&quot;</span><br/>                            <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;tags&quot;</span><br/>                            <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;flags&quot;</span><br/>                            <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;interactions_endpoint_url&quot;</span><br/>                            <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;role_connections_verification_url&quot;</span><br/>                            <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;install_params&quot;</span><br/>                            <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;integration_types_config&quot;</span><br/>                            <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;custom_install_url&quot;</span><span class="tsd-signature-symbol">,</span><br/>                        <span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>                    <span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>                <span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>        <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIApplication</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>    <a class="tsd-kind-property" href="#gateway">gateway</a><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>        <span class="tsd-kind-property">bot</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span> <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIGatewayBotInfo</span><span class="tsd-signature-symbol">&gt;</span> <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIGatewayInfo</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>    <a class="tsd-kind-property" href="#guilds">guilds</a><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>        <span class="tsd-kind-call-signature">post</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <a href="../types/RestArguments.html" class="tsd-signature-type tsd-kind-type-alias">RestArguments</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTPostAPIGuildsJSONBody</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIGuild</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-call-signature">templates</span><span class="tsd-signature-symbol">(</span><br/>            <span class="tsd-kind-parameter">code</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">,</span><br/>        <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>            <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APITemplate</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-call-signature">post</span><span class="tsd-signature-symbol">(</span><br/>                <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <a href="../types/RestArguments.html" class="tsd-signature-type tsd-kind-type-alias">RestArguments</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTPostAPITemplateCreateGuildJSONBody</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIGuild</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-signature-symbol">(</span><br/>            <span class="tsd-kind-parameter">id</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">,</span><br/>        <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>            <span class="tsd-kind-property">&quot;audit-logs&quot;</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>                <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><br/>                    <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTGetAPIAuditLogQuery</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>                <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIAuditLog</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">&quot;auto-moderation&quot;</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>                <span class="tsd-kind-property">rules</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>                    <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><br/>                        <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">,</span><br/>                    <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTGetAPIAutoModerationRulesResult</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>                    <span class="tsd-kind-call-signature">post</span><span class="tsd-signature-symbol">(</span><br/>                        <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <a href="../types/RestArguments.html" class="tsd-signature-type tsd-kind-type-alias">RestArguments</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTPostAPIAutoModerationRuleJSONBody</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>                    <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIAutoModerationRule</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>                    <span class="tsd-signature-symbol">(</span><br/>                        <span class="tsd-kind-parameter">id</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">,</span><br/>                    <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>                        <span class="tsd-kind-call-signature">delete</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">undefined</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>                        <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIAutoModerationRule</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>                        <span class="tsd-kind-call-signature">patch</span><span class="tsd-signature-symbol">(</span><br/>                            <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <a href="../types/RestArguments.html" class="tsd-signature-type tsd-kind-type-alias">RestArguments</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTPatchAPIAutoModerationRuleJSONBody</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>                        <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIAutoModerationRule</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>                    <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">bans</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>                <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><br/>                    <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTGetAPIGuildBansQuery</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>                <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTGetAPIGuildBansResult</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-signature-symbol">(</span><br/>                    <span class="tsd-kind-parameter">userId</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">,</span><br/>                <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>                    <span class="tsd-kind-call-signature">delete</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">undefined</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>                    <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIBan</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>                    <span class="tsd-kind-call-signature">put</span><span class="tsd-signature-symbol">(</span><br/>                        <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArguments.html" class="tsd-signature-type tsd-kind-type-alias">RestArguments</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">undefined</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">RESTPutAPIGuildBanJSONBody</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>                    <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">undefined</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">&quot;bulk-bans&quot;</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>                <span class="tsd-kind-call-signature">post</span><span class="tsd-signature-symbol">(</span><br/>                    <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <a href="../types/RestArguments.html" class="tsd-signature-type tsd-kind-type-alias">RestArguments</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTPostAPIGuildBulkBanJSONBody</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>                <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTPostAPIGuildBulkBanResult</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">channels</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>                <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><br/>                    <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">,</span><br/>                <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTGetAPIGuildChannelsResult</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-kind-call-signature">patch</span><span class="tsd-signature-symbol">(</span><br/>                    <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <a href="../types/RestArguments.html" class="tsd-signature-type tsd-kind-type-alias">RestArguments</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTPatchAPIGuildChannelPositionsJSONBody</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>                <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">undefined</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-kind-call-signature">post</span><span class="tsd-signature-symbol">(</span><br/>                    <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <a href="../types/RestArguments.html" class="tsd-signature-type tsd-kind-type-alias">RestArguments</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTPostAPIGuildChannelJSONBody</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>                <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIChannel</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">emojis</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>                <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTGetAPIGuildEmojisResult</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-kind-call-signature">post</span><span class="tsd-signature-symbol">(</span><br/>                    <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <a href="../types/RestArguments.html" class="tsd-signature-type tsd-kind-type-alias">RestArguments</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTPostAPIGuildEmojiJSONBody</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>                <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIEmoji</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-signature-symbol">(</span><br/>                    <span class="tsd-kind-parameter">id</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">,</span><br/>                <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>                    <span class="tsd-kind-call-signature">delete</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">undefined</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>                    <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIEmoji</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>                    <span class="tsd-kind-call-signature">patch</span><span class="tsd-signature-symbol">(</span><br/>                        <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <a href="../types/RestArguments.html" class="tsd-signature-type tsd-kind-type-alias">RestArguments</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTPatchAPIGuildEmojiJSONBody</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>                    <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIEmoji</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">integrations</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>                <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><br/>                    <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">,</span><br/>                <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTGetAPIGuildIntegrationsResult</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">id</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span> <span class="tsd-kind-call-signature">delete</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">undefined</span><span class="tsd-signature-symbol">&gt;</span> <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">invites</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>                <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><br/>                    <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">,</span><br/>                <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTGetAPIGuildInvitesResult</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">members</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>                <span class="tsd-kind-property">&quot;@me&quot;</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>                    <span class="tsd-kind-call-signature">patch</span><span class="tsd-signature-symbol">(</span><br/>                        <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <a href="../types/RestArguments.html" class="tsd-signature-type tsd-kind-type-alias">RestArguments</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTPatchAPICurrentGuildMemberJSONBody</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>                    <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIGuildMember</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-kind-property">search</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>                    <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><br/>                        <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTGetAPIGuildMembersSearchQuery</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>                    <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTGetAPIGuildMembersSearchResult</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><br/>                    <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTGetAPIGuildMembersQuery</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>                <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTGetAPIGuildMembersResult</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-signature-symbol">(</span><br/>                    <span class="tsd-kind-parameter">id</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">,</span><br/>                <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>                    <span class="tsd-kind-call-signature">delete</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">undefined</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>                    <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIGuildMember</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>                    <span class="tsd-kind-call-signature">patch</span><span class="tsd-signature-symbol">(</span><br/>                        <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <a href="../types/RestArguments.html" class="tsd-signature-type tsd-kind-type-alias">RestArguments</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTPatchAPIGuildMemberJSONBody</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>                    <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIGuildMember</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>                    <span class="tsd-kind-call-signature">put</span><span class="tsd-signature-symbol">(</span><br/>                        <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArguments.html" class="tsd-signature-type tsd-kind-type-alias">RestArguments</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTPutAPIGuildMemberJSONBody</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>                    <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTPutAPIGuildMemberResult</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>                    <span class="tsd-kind-call-signature">roles</span><span class="tsd-signature-symbol">(</span><br/>                        <span class="tsd-kind-parameter">id</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">,</span><br/>                    <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>                        <span class="tsd-kind-call-signature">delete</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">undefined</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>                        <span class="tsd-kind-call-signature">put</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">undefined</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>                    <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">mfa</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>                <span class="tsd-kind-call-signature">post</span><span class="tsd-signature-symbol">(</span><br/>                    <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <a href="../types/RestArguments.html" class="tsd-signature-type tsd-kind-type-alias">RestArguments</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTPostAPIGuildsMFAJSONBody</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>                <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTPostAPIGuildsMFAJSONBody</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">preview</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span> <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIGuildPreview</span><span class="tsd-signature-symbol">&gt;</span> <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">prune</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>                <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><br/>                    <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTGetAPIGuildPruneCountQuery</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>                <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTGetAPIGuildPruneCountResult</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-kind-call-signature">post</span><span class="tsd-signature-symbol">(</span><br/>                    <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <a href="../types/RestArguments.html" class="tsd-signature-type tsd-kind-type-alias">RestArguments</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTPostAPIGuildPruneJSONBody</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>                <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTPostAPIGuildPruneResult</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">regions</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>                <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><br/>                    <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">,</span><br/>                <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTGetAPIGuildVoiceRegionsResult</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">roles</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>                <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTGetAPIGuildRolesResult</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-kind-call-signature">patch</span><span class="tsd-signature-symbol">(</span><br/>                    <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <a href="../types/RestArguments.html" class="tsd-signature-type tsd-kind-type-alias">RestArguments</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTPatchAPIGuildRolePositionsJSONBody</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>                <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTPatchAPIGuildRolePositionsResult</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-kind-call-signature">post</span><span class="tsd-signature-symbol">(</span><br/>                    <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <a href="../types/RestArguments.html" class="tsd-signature-type tsd-kind-type-alias">RestArguments</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTPostAPIGuildRoleJSONBody</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>                <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIRole</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-signature-symbol">(</span><br/>                    <span class="tsd-kind-parameter">id</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">,</span><br/>                <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>                    <span class="tsd-kind-call-signature">delete</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">undefined</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>                    <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIRole</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>                    <span class="tsd-kind-call-signature">patch</span><span class="tsd-signature-symbol">(</span><br/>                        <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <a href="../types/RestArguments.html" class="tsd-signature-type tsd-kind-type-alias">RestArguments</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTPatchAPIGuildRoleJSONBody</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>                    <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIRole</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">&quot;scheduled-events&quot;</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>                <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><br/>                    <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTGetAPIGuildScheduledEventsQuery</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>                <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTGetAPIGuildScheduledEventsResult</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-kind-call-signature">post</span><span class="tsd-signature-symbol">(</span><br/>                    <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <a href="../types/RestArguments.html" class="tsd-signature-type tsd-kind-type-alias">RestArguments</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTPostAPIGuildScheduledEventJSONBody</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>                <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIGuildScheduledEvent</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-signature-symbol">(</span><br/>                    <span class="tsd-kind-parameter">id</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">,</span><br/>                <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>                    <span class="tsd-kind-property">users</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>                        <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><br/>                            <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">&lt;</span><br/>                                <span class="tsd-signature-type">RESTGetAPIGuildScheduledEventUsersQuery</span><span class="tsd-signature-symbol">,</span><br/>                            <span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>                        <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTGetAPIGuildScheduledEventUsersResult</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>                    <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>                    <span class="tsd-kind-call-signature">delete</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">undefined</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>                    <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><br/>                        <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTGetAPIGuildScheduledEventQuery</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>                    <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIGuildScheduledEvent</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>                    <span class="tsd-kind-call-signature">patch</span><span class="tsd-signature-symbol">(</span><br/>                        <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <a href="../types/RestArguments.html" class="tsd-signature-type tsd-kind-type-alias">RestArguments</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTPatchAPIGuildScheduledEventJSONBody</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>                    <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIGuildScheduledEvent</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">&quot;soundboard-sounds&quot;</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>                <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><br/>                    <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">,</span><br/>                <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTGetAPIGuildSoundboardSoundsResult</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-kind-call-signature">post</span><span class="tsd-signature-symbol">(</span><br/>                    <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <a href="../types/RestArguments.html" class="tsd-signature-type tsd-kind-type-alias">RestArguments</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTPostAPIGuildSoundboardSound</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>                <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APISoundBoard</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-signature-symbol">(</span><br/>                    <span class="tsd-kind-parameter">id</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">,</span><br/>                <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>                    <span class="tsd-kind-call-signature">delete</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">undefined</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>                    <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APISoundBoard</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>                    <span class="tsd-kind-call-signature">patch</span><span class="tsd-signature-symbol">(</span><br/>                        <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArguments.html" class="tsd-signature-type tsd-kind-type-alias">RestArguments</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTPatchAPIGuildSoundboardSound</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>                    <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APISoundBoard</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">stickers</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>                <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><br/>                    <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">,</span><br/>                <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTGetAPIGuildStickersResult</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-kind-call-signature">post</span><span class="tsd-signature-symbol">(</span><br/>                    <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <a href="../types/RestArguments.html" class="tsd-signature-type tsd-kind-type-alias">RestArguments</a><span class="tsd-signature-symbol">&lt;</span><br/>                        <span class="tsd-signature-type">Omit</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTPostAPIGuildStickerFormDataBody</span><span class="tsd-signature-symbol">,</span> <span class="tsd-signature-type">&quot;file&quot;</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>                        (<span class="tsd-signature-type">Omit</span><span class="tsd-signature-symbol">&lt;</span><a href="RawFile.html" class="tsd-signature-type tsd-kind-interface">RawFile</a><span class="tsd-signature-symbol">,</span> <span class="tsd-signature-type">&quot;key&quot;</span><span class="tsd-signature-symbol">&gt;</span> <span class="tsd-signature-symbol">&amp;</span> <span class="tsd-signature-symbol">{</span> <span class="tsd-kind-property">key</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">&quot;file&quot;</span> <span class="tsd-signature-symbol">}</span>)<span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">,</span><br/>                    <span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>                <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APISticker</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-signature-symbol">(</span><br/>                    <span class="tsd-kind-parameter">id</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">,</span><br/>                <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>                    <span class="tsd-kind-call-signature">delete</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">undefined</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>                    <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APISticker</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>                    <span class="tsd-kind-call-signature">patch</span><span class="tsd-signature-symbol">(</span><br/>                        <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <a href="../types/RestArguments.html" class="tsd-signature-type tsd-kind-type-alias">RestArguments</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTPatchAPIGuildStickerJSONBody</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>                    <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APISticker</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">templates</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>                <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><br/>                    <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">,</span><br/>                <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTGetAPIGuildTemplatesResult</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-kind-call-signature">post</span><span class="tsd-signature-symbol">(</span><br/>                    <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <a href="../types/RestArguments.html" class="tsd-signature-type tsd-kind-type-alias">RestArguments</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTPostAPIGuildTemplatesJSONBody</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>                <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APITemplate</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-signature-symbol">(</span><br/>                    <span class="tsd-kind-parameter">code</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">,</span><br/>                <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>                    <span class="tsd-kind-call-signature">delete</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APITemplate</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>                    <span class="tsd-kind-call-signature">patch</span><span class="tsd-signature-symbol">(</span><br/>                        <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <a href="../types/RestArguments.html" class="tsd-signature-type tsd-kind-type-alias">RestArguments</a><span class="tsd-signature-symbol">&lt;</span><br/>                            <span class="tsd-signature-type">AddUndefinedToPossiblyUndefinedPropertiesOfInterface</span><span class="tsd-signature-symbol">&lt;</span><br/>                                <span class="tsd-signature-type">Partial</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTPostAPIGuildTemplatesJSONBody</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>                            <span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>                        <span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>                    <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APITemplate</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>                    <span class="tsd-kind-call-signature">put</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APITemplate</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">threads</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>                <span class="tsd-kind-property">active</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>                    <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><br/>                        <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">,</span><br/>                    <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><br/>                        <span class="tsd-signature-symbol">{</span><br/>                            <span class="tsd-kind-property">members</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">APIThreadMember</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">;</span><br/>                            <span class="tsd-kind-property">threads</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">APIChannel</span><span class="tsd-signature-symbol">[]</span> <span class="tsd-signature-symbol">&amp;</span> <span class="tsd-signature-type">Partial</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIThreadChannel</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>                        <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">,</span><br/>                    <span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">&quot;vanity-url&quot;</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>                <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><br/>                    <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">,</span><br/>                <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTGetAPIGuildVanityUrlResult</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">&quot;voice-states&quot;</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>                <span class="tsd-kind-property">&quot;@me&quot;</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>                    <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIVoiceState</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>                    <span class="tsd-kind-call-signature">patch</span><span class="tsd-signature-symbol">(</span><br/>                        <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <a href="../types/RestArguments.html" class="tsd-signature-type tsd-kind-type-alias">RestArguments</a><span class="tsd-signature-symbol">&lt;</span><br/>                            <span class="tsd-signature-type">RESTPatchAPIGuildVoiceStateCurrentMemberJSONBody</span><span class="tsd-signature-symbol">,</span><br/>                        <span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>                    <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">undefined</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-signature-symbol">(</span><br/>                    <span class="tsd-kind-parameter">userId</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">,</span><br/>                <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>                    <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIVoiceState</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>                    <span class="tsd-kind-call-signature">patch</span><span class="tsd-signature-symbol">(</span><br/>                        <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <a href="../types/RestArguments.html" class="tsd-signature-type tsd-kind-type-alias">RestArguments</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTPatchAPIGuildVoiceStateUserJSONBody</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>                    <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">undefined</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">webhooks</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>                <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><br/>                    <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">,</span><br/>                <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTGetAPIGuildWebhooksResult</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">&quot;welcome-screen&quot;</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>                <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIGuildWelcomeScreen</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-kind-call-signature">patch</span><span class="tsd-signature-symbol">(</span><br/>                    <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <a href="../types/RestArguments.html" class="tsd-signature-type tsd-kind-type-alias">RestArguments</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTPatchAPIGuildWelcomeScreenJSONBody</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>                <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIGuildWelcomeScreen</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">widget</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>                <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><br/>                    <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-symbol">{</span> <span class="tsd-kind-property">style</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">string</span> <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>                <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIGuildWidgetSettings</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-kind-call-signature">patch</span><span class="tsd-signature-symbol">(</span><br/>                    <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <a href="../types/RestArguments.html" class="tsd-signature-type tsd-kind-type-alias">RestArguments</a><span class="tsd-signature-symbol">&lt;</span><br/>                        <span class="tsd-signature-type">AddUndefinedToPossiblyUndefinedPropertiesOfInterface</span><span class="tsd-signature-symbol">&lt;</span><br/>                            <span class="tsd-signature-type">Partial</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIGuildWidgetSettings</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>                        <span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>                    <span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>                <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIGuildWidgetSettings</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">&quot;widget.json&quot;</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>                <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIGuildWidget</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">&quot;widget.png&quot;</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>                <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><br/>                    <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTGetAPIGuildWidgetImageQuery</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>                <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">ArrayBuffer</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-call-signature">delete</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">undefined</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTGetAPIGuildQuery</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIGuild</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-call-signature">patch</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <a href="../types/RestArguments.html" class="tsd-signature-type tsd-kind-type-alias">RestArguments</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTPatchAPIGuildJSONBody</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIGuild</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>    <a class="tsd-kind-property" href="#interactions">interactions</a><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">(</span><br/>        <span class="tsd-kind-parameter">id</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">,</span><br/>    <span class="tsd-signature-symbol">)</span> <span class="tsd-signature-symbol">=&gt;</span> <span class="tsd-signature-symbol">(</span><br/>        <span class="tsd-kind-parameter">token</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">,</span><br/>    <span class="tsd-signature-symbol">)</span> <span class="tsd-signature-symbol">=&gt;</span> <span class="tsd-signature-symbol">{</span><br/>        <span class="tsd-kind-property">callback</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>            <span class="tsd-kind-call-signature">post</span><span class="tsd-signature-symbol">(</span><br/>                <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <a href="../types/RestArguments.html" class="tsd-signature-type tsd-kind-type-alias">RestArguments</a><span class="tsd-signature-symbol">&lt;</span><br/>                    <span class="tsd-signature-type">APIInteractionResponse</span><span class="tsd-signature-symbol">,</span><br/>                    <span class="tsd-signature-type">Omit</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTPostAPIInteractionCallbackQuery</span><span class="tsd-signature-symbol">,</span> <span class="tsd-signature-type">&quot;with_response&quot;</span><span class="tsd-signature-symbol">&gt;</span> <span class="tsd-signature-symbol">&amp;</span> <span class="tsd-signature-symbol">{</span><br/>                        <span class="tsd-kind-property">with_response</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">true</span><span class="tsd-signature-symbol">;</span><br/>                    <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">,</span><br/>                <span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTPostAPIInteractionCallbackResult</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-call-signature">post</span><span class="tsd-signature-symbol">(</span><br/>                <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <a href="../types/RestArguments.html" class="tsd-signature-type tsd-kind-type-alias">RestArguments</a><span class="tsd-signature-symbol">&lt;</span><br/>                    <span class="tsd-signature-type">APIInteractionResponse</span><span class="tsd-signature-symbol">,</span><br/>                    <span class="tsd-signature-type">Omit</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTPostAPIInteractionCallbackQuery</span><span class="tsd-signature-symbol">,</span> <span class="tsd-signature-type">&quot;with_response&quot;</span><span class="tsd-signature-symbol">&gt;</span> <span class="tsd-signature-symbol">&amp;</span> <span class="tsd-signature-symbol">{</span><br/>                        <span class="tsd-kind-property">with_response</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">false</span><span class="tsd-signature-symbol">;</span><br/>                    <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">,</span><br/>                <span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">undefined</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-call-signature">post</span><span class="tsd-signature-symbol">(</span><br/>                <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <a href="../types/RestArguments.html" class="tsd-signature-type tsd-kind-type-alias">RestArguments</a><span class="tsd-signature-symbol">&lt;</span><br/>                    <span class="tsd-signature-type">APIInteractionResponse</span><span class="tsd-signature-symbol">,</span><br/>                    <span class="tsd-signature-type">RESTPostAPIInteractionCallbackQuery</span><span class="tsd-signature-symbol">,</span><br/>                <span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">undefined</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">RESTPostAPIInteractionCallbackResult</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>    <a class="tsd-kind-property" href="#soundboard-default-sounds">&quot;soundboard-default-sounds&quot;</a><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>        <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><br/>            <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">,</span><br/>        <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTGetAPIDefaultsSoundboardSoundsResult</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>    <a class="tsd-kind-property" href="#stage-instances">&quot;stage-instances&quot;</a><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>        <span class="tsd-kind-call-signature">post</span><span class="tsd-signature-symbol">(</span><br/>            <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <a href="../types/RestArguments.html" class="tsd-signature-type tsd-kind-type-alias">RestArguments</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTPostAPIStageInstanceJSONBody</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>        <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIStageInstance</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-signature-symbol">(</span><br/>            <span class="tsd-kind-parameter">id</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">,</span><br/>        <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>            <span class="tsd-kind-call-signature">delete</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">undefined</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIStageInstance</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-call-signature">patch</span><span class="tsd-signature-symbol">(</span><br/>                <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <a href="../types/RestArguments.html" class="tsd-signature-type tsd-kind-type-alias">RestArguments</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTPatchAPIStageInstanceJSONBody</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIStageInstance</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>    <a class="tsd-kind-property" href="#sticker-packs">&quot;sticker-packs&quot;</a><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>        <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTGetStickerPacksResult</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">id</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span> <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIStickerPack</span><span class="tsd-signature-symbol">&gt;</span> <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>    <a class="tsd-kind-property" href="#users">users</a><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>        <span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">id</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span> <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIUser</span><span class="tsd-signature-symbol">&gt;</span> <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-signature-symbol">(</span><br/>            <span class="tsd-kind-parameter">id</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">&quot;@me&quot;</span><span class="tsd-signature-symbol">,</span><br/>        <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>            <span class="tsd-kind-property">channels</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>                <span class="tsd-kind-call-signature">post</span><span class="tsd-signature-symbol">(</span><br/>                    <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <a href="../types/RestArguments.html" class="tsd-signature-type tsd-kind-type-alias">RestArguments</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTPostAPICurrentUserCreateDMChannelJSONBody</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>                <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIDMChannel</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">connections</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>                <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><br/>                    <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">,</span><br/>                <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTGetAPICurrentUserConnectionsResult</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">guilds</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>                <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><br/>                    <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTGetAPICurrentUserGuildsQuery</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>                <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTGetAPICurrentUserGuildsResult</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-signature-symbol">(</span><br/>                    <span class="tsd-kind-parameter">id</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">,</span><br/>                <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>                    <span class="tsd-kind-property">member</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span> <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIGuildMember</span><span class="tsd-signature-symbol">&gt;</span> <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>                    <span class="tsd-kind-call-signature">delete</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">undefined</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-call-signature">applications</span><span class="tsd-signature-symbol">(</span><br/>                <span class="tsd-kind-parameter">applicationId</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>                <span class="tsd-kind-property">&quot;role-connection&quot;</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>                    <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><br/>                        <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">,</span><br/>                    <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIApplicationRoleConnection</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>                    <span class="tsd-kind-call-signature">put</span><span class="tsd-signature-symbol">(</span><br/>                        <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <a href="../types/RestArguments.html" class="tsd-signature-type tsd-kind-type-alias">RestArguments</a><span class="tsd-signature-symbol">&lt;</span><br/>                            <span class="tsd-signature-type">RESTPutAPICurrentUserApplicationRoleConnectionJSONBody</span><span class="tsd-signature-symbol">,</span><br/>                        <span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>                    <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIApplicationRoleConnection</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIUser</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-call-signature">patch</span><span class="tsd-signature-symbol">(</span><br/>                <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <a href="../types/RestArguments.html" class="tsd-signature-type tsd-kind-type-alias">RestArguments</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTPatchAPICurrentUserJSONBody</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIUser</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>    <a class="tsd-kind-property" href="#voice">voice</a><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>        <span class="tsd-kind-property">region</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>            <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTGetAPIVoiceRegionsResult</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>    <a class="tsd-kind-call-signature" href="#channels-1">channels</a><span class="tsd-signature-symbol">(</span><br/>        <span class="tsd-kind-parameter">id</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">,</span><br/>    <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>        <span class="tsd-kind-property">followers</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>            <span class="tsd-kind-call-signature">post</span><span class="tsd-signature-symbol">(</span><br/>                <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <a href="../types/RestArguments.html" class="tsd-signature-type tsd-kind-type-alias">RestArguments</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTPostAPIChannelFollowersJSONBody</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIFollowedChannel</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-property">invites</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>            <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><br/>                <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTGetAPIChannelInvitesResult</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-call-signature">post</span><span class="tsd-signature-symbol">(</span><br/>                <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <a href="../types/RestArguments.html" class="tsd-signature-type tsd-kind-type-alias">RestArguments</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTPostAPIChannelInviteJSONBody</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIExtendedInvite</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-property">messages</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>            <span class="tsd-kind-property">&quot;bulk-delete&quot;</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>                <span class="tsd-kind-call-signature">post</span><span class="tsd-signature-symbol">(</span><br/>                    <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <a href="../types/RestArguments.html" class="tsd-signature-type tsd-kind-type-alias">RestArguments</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTPostAPIChannelMessagesBulkDeleteJSONBody</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>                <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">undefined</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><br/>                <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTGetAPIChannelMessagesQuery</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTGetAPIChannelMessagesResult</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-call-signature">post</span><span class="tsd-signature-symbol">(</span><br/>                <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <a href="../types/RestArguments.html" class="tsd-signature-type tsd-kind-type-alias">RestArguments</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTPostAPIChannelMessageJSONBody</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIMessage</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-signature-symbol">(</span><br/>                <span class="tsd-kind-parameter">id</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>                <span class="tsd-kind-property">crosspost</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span> <span class="tsd-kind-call-signature">post</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIMessage</span><span class="tsd-signature-symbol">&gt;</span> <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-kind-property">reactions</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>                    <span class="tsd-kind-call-signature">delete</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">undefined</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>                    <span class="tsd-signature-symbol">(</span><br/>                        <span class="tsd-kind-parameter">emoji</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">,</span><br/>                    <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>                        <span class="tsd-kind-call-signature">delete</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">undefined</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>                        <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><br/>                            <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">&lt;</span><br/>                                <span class="tsd-signature-type">RESTGetAPIChannelMessageReactionUsersQuery</span><span class="tsd-signature-symbol">,</span><br/>                            <span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>                        <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTGetAPIChannelMessageReactionUsersResult</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>                        <span class="tsd-signature-symbol">(</span><br/>                            <span class="tsd-kind-parameter">id</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">&quot;@me&quot;</span><span class="tsd-signature-symbol">,</span><br/>                        <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>                            <span class="tsd-kind-call-signature">delete</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">undefined</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>                            <span class="tsd-kind-call-signature">put</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">undefined</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>                        <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>                        <span class="tsd-signature-symbol">(</span><br/>                            <span class="tsd-kind-parameter">id</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">,</span><br/>                        <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span> <span class="tsd-kind-call-signature">delete</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">undefined</span><span class="tsd-signature-symbol">&gt;</span> <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>                    <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-kind-property">threads</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>                    <span class="tsd-kind-call-signature">post</span><span class="tsd-signature-symbol">(</span><br/>                        <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <a href="../types/RestArguments.html" class="tsd-signature-type tsd-kind-type-alias">RestArguments</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTPostAPIChannelMessagesThreadsJSONBody</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>                    <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIChannel</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-kind-call-signature">delete</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">undefined</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIMessage</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-kind-call-signature">patch</span><span class="tsd-signature-symbol">(</span><br/>                    <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <a href="../types/RestArguments.html" class="tsd-signature-type tsd-kind-type-alias">RestArguments</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTPatchAPIChannelMessageJSONBody</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>                <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIMessage</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-property">permissions</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">(</span><br/>            <span class="tsd-kind-parameter">id</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">,</span><br/>        <span class="tsd-signature-symbol">)</span> <span class="tsd-signature-symbol">=&gt;</span> <span class="tsd-signature-symbol">{</span><br/>            <span class="tsd-kind-call-signature">delete</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">undefined</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-call-signature">put</span><span class="tsd-signature-symbol">(</span><br/>                <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArguments.html" class="tsd-signature-type tsd-kind-type-alias">RestArguments</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTPutAPIChannelPermissionJSONBody</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">undefined</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-property">pins</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>            <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTGetAPIChannelPinsResult</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-signature-symbol">(</span><br/>                <span class="tsd-kind-parameter">id</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>                <span class="tsd-kind-call-signature">delete</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">undefined</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-kind-call-signature">put</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">undefined</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-property">recipients</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">(</span><br/>            <span class="tsd-kind-parameter">id</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">,</span><br/>        <span class="tsd-signature-symbol">)</span> <span class="tsd-signature-symbol">=&gt;</span> <span class="tsd-signature-symbol">{</span><br/>            <span class="tsd-kind-call-signature">delete</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">unknown</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-call-signature">put</span><span class="tsd-signature-symbol">(</span><br/>                <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArguments.html" class="tsd-signature-type tsd-kind-type-alias">RestArguments</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTPutAPIChannelRecipientJSONBody</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">unknown</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-property">&quot;send-soundboard-sound&quot;</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>            <span class="tsd-kind-call-signature">post</span><span class="tsd-signature-symbol">(</span><br/>                <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <a href="../types/RestArguments.html" class="tsd-signature-type tsd-kind-type-alias">RestArguments</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTPostAPISendSoundboardSound</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">undefined</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-property">&quot;thread-members&quot;</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>            <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><br/>                <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTGetAPIChannelThreadMembersQuery</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTGetAPIChannelThreadMembersResult</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-signature-symbol">(</span><br/>                <span class="tsd-kind-parameter">id</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">&quot;@me&quot;</span><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>                <span class="tsd-kind-call-signature">delete</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">undefined</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-kind-call-signature">put</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">undefined</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-signature-symbol">(</span><br/>                <span class="tsd-kind-parameter">id</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>                <span class="tsd-kind-call-signature">delete</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">undefined</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><br/>                    <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTGetAPIChannelThreadMemberQuery</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>                <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIThreadMember</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-kind-call-signature">put</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">undefined</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-property">threads</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>            <span class="tsd-kind-property">archived</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>                <span class="tsd-kind-property">private</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>                    <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><br/>                        <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTGetAPIChannelThreadsArchivedQuery</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>                    <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTGetAPIChannelUsersThreadsArchivedResult</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-kind-property">public</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>                    <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><br/>                        <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTGetAPIChannelThreadsArchivedQuery</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>                    <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTGetAPIChannelUsersThreadsArchivedResult</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-call-signature">post</span><span class="tsd-signature-symbol">(</span><br/>                <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <a href="../types/RestArguments.html" class="tsd-signature-type tsd-kind-type-alias">RestArguments</a><span class="tsd-signature-symbol">&lt;</span><br/>                    <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">RESTPostAPIGuildForumThreadsJSONBody</span><br/>                    <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">RESTPostAPIChannelThreadsJSONBody</span><span class="tsd-signature-symbol">,</span><br/>                <span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIChannel</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-property">typing</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span> <span class="tsd-kind-call-signature">post</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">undefined</span><span class="tsd-signature-symbol">&gt;</span> <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-property">users</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">(</span><br/>            <span class="tsd-kind-parameter">id</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">&quot;@me&quot;</span><span class="tsd-signature-symbol">,</span><br/>        <span class="tsd-signature-symbol">)</span> <span class="tsd-signature-symbol">=&gt;</span> <span class="tsd-signature-symbol">{</span><br/>            <span class="tsd-kind-property">threads</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>                <span class="tsd-kind-property">archived</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>                    <span class="tsd-kind-property">private</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>                        <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><br/>                            <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTGetAPIChannelThreadsArchivedQuery</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>                        <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTGetAPIChannelUsersThreadsArchivedResult</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>                    <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-property">&quot;voice-status&quot;</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>            <span class="tsd-kind-call-signature">put</span><span class="tsd-signature-symbol">(</span><br/>                <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <a href="../types/RestArguments.html" class="tsd-signature-type tsd-kind-type-alias">RestArguments</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-symbol">{</span> <span class="tsd-kind-property">status</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">null</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">string</span> <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">undefined</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-property">webhooks</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>            <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><br/>                <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTGetAPIGuildWebhooksResult</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-call-signature">post</span><span class="tsd-signature-symbol">(</span><br/>                <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <a href="../types/RestArguments.html" class="tsd-signature-type tsd-kind-type-alias">RestArguments</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTPostAPIChannelWebhookJSONBody</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIWebhook</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-call-signature">delete</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIChannel</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIChannel</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-call-signature">patch</span><span class="tsd-signature-symbol">(</span><br/>            <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <a href="../types/RestArguments.html" class="tsd-signature-type tsd-kind-type-alias">RestArguments</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTPatchAPIChannelJSONBody</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>        <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIChannel</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-call-signature">polls</span><span class="tsd-signature-symbol">(</span><br/>            <span class="tsd-kind-parameter">messageId</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">,</span><br/>        <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>            <span class="tsd-kind-property">expire</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span> <span class="tsd-kind-call-signature">post</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIMessage</span><span class="tsd-signature-symbol">&gt;</span> <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-call-signature">answers</span><span class="tsd-signature-symbol">(</span><br/>                <span class="tsd-kind-parameter">id</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">ValidAnswerId</span><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>                <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><br/>                    <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTGetAPIPollAnswerVotersQuery</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>                <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTGetAPIPollAnswerVotersResult</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>    <a class="tsd-kind-call-signature" href="#invites-1">invites</a><span class="tsd-signature-symbol">(</span><br/>        <span class="tsd-kind-parameter">id</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">,</span><br/>    <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>        <span class="tsd-kind-call-signature">delete</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIInvite</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArguments.html" class="tsd-signature-type tsd-kind-type-alias">RestArguments</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTGetAPIInviteQuery</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIInvite</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>    <a class="tsd-kind-call-signature" href="#stickers-1">stickers</a><span class="tsd-signature-symbol">(</span><br/>        <span class="tsd-kind-parameter">id</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">,</span><br/>    <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span> <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APISticker</span><span class="tsd-signature-symbol">&gt;</span> <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>    <a class="tsd-kind-call-signature" href="#webhooks-1">webhooks</a><span class="tsd-signature-symbol">(</span><br/>        <span class="tsd-kind-parameter">id</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">,</span><br/>    <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>        <span class="tsd-kind-call-signature">delete</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">undefined</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIWebhook</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-call-signature">patch</span><span class="tsd-signature-symbol">(</span><br/>            <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <a href="../types/RestArguments.html" class="tsd-signature-type tsd-kind-type-alias">RestArguments</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTPatchAPIWebhookJSONBody</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>        <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIWebhook</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-signature-symbol">(</span><br/>            <span class="tsd-kind-parameter">token</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">,</span><br/>        <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>            <span class="tsd-kind-property">github</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>                <span class="tsd-kind-call-signature">post</span><span class="tsd-signature-symbol">(</span><br/>                    <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <a href="../types/RestArguments.html" class="tsd-signature-type tsd-kind-type-alias">RestArguments</a><span class="tsd-signature-symbol">&lt;</span><br/>                        <span class="tsd-signature-type">RESTPostAPIWebhookWithTokenJSONBody</span><span class="tsd-signature-symbol">,</span><br/>                        <span class="tsd-signature-type">RESTPostAPIWebhookWithTokenGitHubQuery</span><span class="tsd-signature-symbol">,</span><br/>                    <span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>                <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">undefined</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">APIMessage</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">messages</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">(</span><br/>                <span class="tsd-kind-parameter">id</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">)</span> <span class="tsd-signature-symbol">=&gt;</span> <span class="tsd-signature-symbol">{</span><br/>                <span class="tsd-kind-call-signature">delete</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">undefined</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><br/>                    <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-symbol">{</span> <span class="tsd-kind-property">thread_id</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span> <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>                <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIMessage</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-kind-call-signature">patch</span><span class="tsd-signature-symbol">(</span><br/>                    <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <a href="../types/RestArguments.html" class="tsd-signature-type tsd-kind-type-alias">RestArguments</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTPatchAPIWebhookWithTokenMessageJSONBody</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>                <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIMessage</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">slack</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>                <span class="tsd-kind-call-signature">post</span><span class="tsd-signature-symbol">(</span><br/>                    <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <a href="../types/RestArguments.html" class="tsd-signature-type tsd-kind-type-alias">RestArguments</a><span class="tsd-signature-symbol">&lt;</span><br/>                        <span class="tsd-signature-type">RESTPostAPIWebhookWithTokenJSONBody</span><span class="tsd-signature-symbol">,</span><br/>                        <span class="tsd-signature-type">RESTPostAPIWebhookWithTokenSlackQuery</span><span class="tsd-signature-symbol">,</span><br/>                    <span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>                <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">undefined</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">APIMessage</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-call-signature">delete</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">undefined</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><br/>                <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTGetAPIWebhookWithTokenResult</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-call-signature">patch</span><span class="tsd-signature-symbol">(</span><br/>                <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <a href="../types/RestArguments.html" class="tsd-signature-type tsd-kind-type-alias">RestArguments</a><span class="tsd-signature-symbol">&lt;</span><br/>                    <span class="tsd-signature-type">RESTPatchAPIWebhookWithTokenJSONBody</span><span class="tsd-signature-symbol">,</span><br/>                    <span class="tsd-signature-type">RESTPatchAPIWebhookWithTokenMessageQuery</span><span class="tsd-signature-symbol">,</span><br/>                <span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTGetAPIWebhookWithTokenResult</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-call-signature">post</span><span class="tsd-signature-symbol">(</span><br/>                <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <a href="../types/RestArguments.html" class="tsd-signature-type tsd-kind-type-alias">RestArguments</a><span class="tsd-signature-symbol">&lt;</span><br/>                    <span class="tsd-signature-type">RESTPostAPIWebhookWithTokenJSONBody</span><span class="tsd-signature-symbol">,</span><br/>                    <span class="tsd-signature-type">RESTPostAPIWebhookWithTokenQuery</span><span class="tsd-signature-symbol">,</span><br/>                <span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">undefined</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">APIMessage</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/><span class="tsd-signature-symbol">}</span></div><section class="tsd-panel tsd-hierarchy" data-refl="1101"><h4>Hierarchy</h4><ul class="tsd-hierarchy"><li class="tsd-hierarchy-item"><span class="tsd-signature-type">ApplicationRoutes</span></li><li class="tsd-hierarchy-item"><span class="tsd-signature-type">ChannelRoutes</span></li><li class="tsd-hierarchy-item"><span class="tsd-signature-type">GatewayRoutes</span></li><li class="tsd-hierarchy-item"><span class="tsd-signature-type">GuildRoutes</span></li><li class="tsd-hierarchy-item"><span class="tsd-signature-type">InteractionRoutes</span></li><li class="tsd-hierarchy-item"><span class="tsd-signature-type">InviteRoutes</span></li><li class="tsd-hierarchy-item"><span class="tsd-signature-type">StageInstanceRoutes</span></li><li class="tsd-hierarchy-item"><span class="tsd-signature-type">StickerRoutes</span></li><li class="tsd-hierarchy-item"><span class="tsd-signature-type">UserRoutes</span></li><li class="tsd-hierarchy-item"><span class="tsd-signature-type">VoiceRoutes</span></li><li class="tsd-hierarchy-item"><span class="tsd-signature-type">WebhookRoutes</span></li><li class="tsd-hierarchy-item"><span class="tsd-signature-type">SoundboardRoutes</span><ul class="tsd-hierarchy"><li class="tsd-hierarchy-item"><span class="tsd-hierarchy-target">APIRoutes</span></li></ul></li></ul></section><aside class="tsd-sources"><ul><li>Defined in <a href="https://github.com/tiramisulabs/seyfert//blob/9ae4290ef220ffc96449d469d16d76b2031eca69/src/api/Routes/index.ts#L16">src/api/Routes/index.ts:16</a></li></ul></aside><section class="tsd-panel-group tsd-index-group"><section class="tsd-panel tsd-index-panel"><details class="tsd-index-content tsd-accordion" open><summary class="tsd-accordion-summary tsd-index-summary"><svg width="20" height="20" viewBox="0 0 24 24" fill="none" aria-hidden="true"><use href="../assets/icons.svg#icon-chevronDown"></use></svg><h5 class="tsd-index-heading uppercase">Index</h5></summary><div class="tsd-accordion-details"><section class="tsd-index-section"><h3 class="tsd-index-heading">Properties</h3><div class="tsd-index-list"><a href="#applications" class="tsd-index-link tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>applications</span></a>
<a href="#gateway" class="tsd-index-link tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>gateway</span></a>
<a href="#guilds" class="tsd-index-link tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>guilds</span></a>
<a href="#interactions" class="tsd-index-link tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>interactions</span></a>
<a href="#soundboard-default-sounds" class="tsd-index-link tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>soundboard-<wbr/>default-<wbr/>sounds</span></a>
<a href="#stage-instances" class="tsd-index-link tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>stage-<wbr/>instances</span></a>
<a href="#sticker-packs" class="tsd-index-link tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>sticker-<wbr/>packs</span></a>
<a href="#users" class="tsd-index-link tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>users</span></a>
<a href="#voice" class="tsd-index-link tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>voice</span></a>
</div></section><section class="tsd-index-section"><h3 class="tsd-index-heading">Methods</h3><div class="tsd-index-list"><a href="#channels" class="tsd-index-link tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Method"><use href="../assets/icons.svg#icon-2048"></use></svg><span>channels</span></a>
<a href="#invites" class="tsd-index-link tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Method"><use href="../assets/icons.svg#icon-2048"></use></svg><span>invites</span></a>
<a href="#stickers" class="tsd-index-link tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Method"><use href="../assets/icons.svg#icon-2048"></use></svg><span>stickers</span></a>
<a href="#webhooks" class="tsd-index-link tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Method"><use href="../assets/icons.svg#icon-2048"></use></svg><span>webhooks</span></a>
</div></section></div></details></section></section><details class="tsd-panel-group tsd-member-group tsd-accordion" open><summary class="tsd-accordion-summary" data-key="section-Properties"><svg width="20" height="20" viewBox="0 0 24 24" fill="none" aria-hidden="true"><use href="../assets/icons.svg#icon-chevronDown"></use></svg><h2>Properties</h2></summary><section><section class="tsd-panel tsd-member tsd-is-inherited"><h3 class="tsd-anchor-link" id="applications"><span>applications</span><a href="#applications" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">applications</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">(</span><br/>    <span class="tsd-kind-parameter">id</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">,</span><br/><span class="tsd-signature-symbol">)</span> <span class="tsd-signature-symbol">=&gt;</span> <span class="tsd-signature-symbol">{</span><br/>    <span class="tsd-kind-property">&quot;activity-instances&quot;</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">(</span><br/>        <span class="tsd-kind-parameter">id</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">,</span><br/>    <span class="tsd-signature-symbol">)</span> <span class="tsd-signature-symbol">=&gt;</span> <span class="tsd-signature-symbol">{</span><br/>        <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><br/>            <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">,</span><br/>        <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RestGetAPIApplicationActivityInstanceResult</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-property">commands</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>        <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><br/>            <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTGetAPIApplicationCommandsQuery</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>        <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTGetAPIApplicationCommandsResult</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-call-signature">post</span><span class="tsd-signature-symbol">(</span><br/>            <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <a href="../types/RestArguments.html" class="tsd-signature-type tsd-kind-type-alias">RestArguments</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTPostAPIApplicationCommandsJSONBody</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>        <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIApplicationCommand</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-call-signature">put</span><span class="tsd-signature-symbol">(</span><br/>            <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArguments.html" class="tsd-signature-type tsd-kind-type-alias">RestArguments</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTPutAPIApplicationCommandsJSONBody</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>        <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTPutAPIApplicationCommandsResult</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-signature-symbol">(</span><br/>            <span class="tsd-kind-parameter">id</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">,</span><br/>        <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>            <span class="tsd-kind-call-signature">delete</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">undefined</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIApplicationCommand</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-call-signature">patch</span><span class="tsd-signature-symbol">(</span><br/>                <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <a href="../types/RestArguments.html" class="tsd-signature-type tsd-kind-type-alias">RestArguments</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTPatchAPIApplicationCommandJSONBody</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIApplicationCommand</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-property">emojis</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>        <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><br/>            <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">Pick</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIApplicationEmoji</span><span class="tsd-signature-symbol">,</span> <span class="tsd-signature-type">&quot;id&quot;</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>        <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTGetAPIApplicationEmojisResult</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-call-signature">post</span><span class="tsd-signature-symbol">(</span><br/>            <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArguments.html" class="tsd-signature-type tsd-kind-type-alias">RestArguments</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTPostAPIApplicationEmojiJSONBody</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>        <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIApplicationEmoji</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-signature-symbol">(</span><br/>            <span class="tsd-kind-parameter">id</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">,</span><br/>        <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>            <span class="tsd-kind-call-signature">delete</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">undefined</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIApplicationEmoji</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-call-signature">patch</span><span class="tsd-signature-symbol">(</span><br/>                <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArguments.html" class="tsd-signature-type tsd-kind-type-alias">RestArguments</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTPatchAPIApplicationEmojiJSONBody</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIApplicationEmoji</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-property">entitlements</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>        <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><br/>            <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTGetAPIEntitlementsQuery</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>        <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTGetAPIEntitlementsResult</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-call-signature">post</span><span class="tsd-signature-symbol">(</span><br/>            <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <a href="../types/RestArguments.html" class="tsd-signature-type tsd-kind-type-alias">RestArguments</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTPostAPIEntitlementBody</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>        <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">Partial</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">Omit</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIEntitlement</span><span class="tsd-signature-symbol">,</span> <span class="tsd-signature-type">&quot;ends_at&quot;</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;starts_at&quot;</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-signature-symbol">(</span><br/>            <span class="tsd-kind-parameter">id</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">,</span><br/>        <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>            <span class="tsd-kind-property">consume</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span> <span class="tsd-kind-call-signature">post</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">undefined</span><span class="tsd-signature-symbol">&gt;</span> <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-call-signature">delete</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">undefined</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIEntitlement</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-property">guilds</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">(</span><br/>        <span class="tsd-kind-parameter">id</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">,</span><br/>    <span class="tsd-signature-symbol">)</span> <span class="tsd-signature-symbol">=&gt;</span> <span class="tsd-signature-symbol">{</span><br/>        <span class="tsd-kind-property">commands</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>            <span class="tsd-kind-property">permissions</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>                <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><br/>                    <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">,</span><br/>                <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTGetAPIGuildApplicationCommandsPermissionsResult</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><br/>                <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTGetAPIApplicationCommandsQuery</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTGetAPIApplicationGuildCommandsResult</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-call-signature">post</span><span class="tsd-signature-symbol">(</span><br/>                <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <a href="../types/RestArguments.html" class="tsd-signature-type tsd-kind-type-alias">RestArguments</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTPostAPIApplicationGuildCommandsJSONBody</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTAPIApplicationGuildCommand</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-call-signature">put</span><span class="tsd-signature-symbol">(</span><br/>                <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArguments.html" class="tsd-signature-type tsd-kind-type-alias">RestArguments</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTPutAPIApplicationGuildCommandsJSONBody</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTPutAPIApplicationGuildCommandsResult</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-signature-symbol">(</span><br/>                <span class="tsd-kind-parameter">id</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>                <span class="tsd-kind-property">permissions</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>                    <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><br/>                        <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">,</span><br/>                    <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTGetAPIGuildApplicationCommandsPermissionsResult</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>                    <span class="tsd-kind-call-signature">put</span><span class="tsd-signature-symbol">(</span><br/>                        <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArguments.html" class="tsd-signature-type tsd-kind-type-alias">RestArguments</a><span class="tsd-signature-symbol">&lt;</span><br/>                            <span class="tsd-signature-type">RESTPutAPIApplicationCommandPermissionsJSONBody</span><span class="tsd-signature-symbol">,</span><br/>                        <span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>                    <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTPutAPIGuildApplicationCommandsPermissionsResult</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-kind-call-signature">delete</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">undefined</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTAPIApplicationGuildCommand</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-kind-call-signature">patch</span><span class="tsd-signature-symbol">(</span><br/>                    <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <a href="../types/RestArguments.html" class="tsd-signature-type tsd-kind-type-alias">RestArguments</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTPatchAPIApplicationGuildCommandJSONBody</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>                <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTAPIApplicationGuildCommand</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-property">&quot;role-connections&quot;</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>        <span class="tsd-kind-property">metadata</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>            <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><br/>                <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTGetAPIApplicationRoleConnectionMetadataResult</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-call-signature">put</span><span class="tsd-signature-symbol">(</span><br/>                <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <a href="../types/RestArguments.html" class="tsd-signature-type tsd-kind-type-alias">RestArguments</a><span class="tsd-signature-symbol">&lt;</span><br/>                    <span class="tsd-signature-type">RESTPutAPIApplicationRoleConnectionMetadataJSONBody</span><span class="tsd-signature-symbol">,</span><br/>                <span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTPutAPIApplicationRoleConnectionMetadataResult</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-property">skus</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span> <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTGetAPISKUsResult</span><span class="tsd-signature-symbol">&gt;</span> <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIApplication</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-call-signature">patch</span><span class="tsd-signature-symbol">(</span><br/>        <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <a href="../types/RestArguments.html" class="tsd-signature-type tsd-kind-type-alias">RestArguments</a><span class="tsd-signature-symbol">&lt;</span><br/>            <span class="tsd-signature-type">AddUndefinedToPossiblyUndefinedPropertiesOfInterface</span><span class="tsd-signature-symbol">&lt;</span><br/>                <span class="tsd-signature-type">Partial</span><span class="tsd-signature-symbol">&lt;</span><br/>                    <span class="tsd-signature-type">Nullable</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">Pick</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIApplication</span><span class="tsd-signature-symbol">,</span> <span class="tsd-signature-type">&quot;icon&quot;</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;cover_image&quot;</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">&gt;</span> <span class="tsd-signature-symbol">&amp;</span> <span class="tsd-signature-type">Pick</span><span class="tsd-signature-symbol">&lt;</span><br/>                        <span class="tsd-signature-type">APIApplication</span><span class="tsd-signature-symbol">,</span><br/>                        <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;description&quot;</span><br/>                        <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;tags&quot;</span><br/>                        <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;flags&quot;</span><br/>                        <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;interactions_endpoint_url&quot;</span><br/>                        <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;role_connections_verification_url&quot;</span><br/>                        <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;install_params&quot;</span><br/>                        <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;integration_types_config&quot;</span><br/>                        <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">&quot;custom_install_url&quot;</span><span class="tsd-signature-symbol">,</span><br/>                    <span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>                <span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>        <span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>    <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIApplication</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/><span class="tsd-signature-symbol">}</span></div><aside class="tsd-sources"><p>Inherited from ApplicationRoutes.applications</p><ul><li>Defined in <a href="https://github.com/tiramisulabs/seyfert//blob/9ae4290ef220ffc96449d469d16d76b2031eca69/src/api/Routes/applications.ts#L47">src/api/Routes/applications.ts:47</a></li></ul></aside></section><section class="tsd-panel tsd-member tsd-is-inherited"><h3 class="tsd-anchor-link" id="gateway"><span>gateway</span><a href="#gateway" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">gateway</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>    <span class="tsd-kind-property">bot</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span> <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIGatewayBotInfo</span><span class="tsd-signature-symbol">&gt;</span> <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIGatewayInfo</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/><span class="tsd-signature-symbol">}</span></div><aside class="tsd-sources"><p>Inherited from GatewayRoutes.gateway</p><ul><li>Defined in <a href="https://github.com/tiramisulabs/seyfert//blob/9ae4290ef220ffc96449d469d16d76b2031eca69/src/api/Routes/gateway.ts#L5">src/api/Routes/gateway.ts:5</a></li></ul></aside></section><section class="tsd-panel tsd-member tsd-is-inherited"><h3 class="tsd-anchor-link" id="guilds"><span>guilds</span><a href="#guilds" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">guilds</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>    <span class="tsd-kind-call-signature">post</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <a href="../types/RestArguments.html" class="tsd-signature-type tsd-kind-type-alias">RestArguments</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTPostAPIGuildsJSONBody</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIGuild</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-call-signature">templates</span><span class="tsd-signature-symbol">(</span><br/>        <span class="tsd-kind-parameter">code</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">,</span><br/>    <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>        <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APITemplate</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-call-signature">post</span><span class="tsd-signature-symbol">(</span><br/>            <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <a href="../types/RestArguments.html" class="tsd-signature-type tsd-kind-type-alias">RestArguments</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTPostAPITemplateCreateGuildJSONBody</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>        <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIGuild</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-signature-symbol">(</span><br/>        <span class="tsd-kind-parameter">id</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">,</span><br/>    <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>        <span class="tsd-kind-property">&quot;audit-logs&quot;</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>            <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><br/>                <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTGetAPIAuditLogQuery</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIAuditLog</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-property">&quot;auto-moderation&quot;</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>            <span class="tsd-kind-property">rules</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>                <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><br/>                    <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">,</span><br/>                <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTGetAPIAutoModerationRulesResult</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-kind-call-signature">post</span><span class="tsd-signature-symbol">(</span><br/>                    <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <a href="../types/RestArguments.html" class="tsd-signature-type tsd-kind-type-alias">RestArguments</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTPostAPIAutoModerationRuleJSONBody</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>                <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIAutoModerationRule</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-signature-symbol">(</span><br/>                    <span class="tsd-kind-parameter">id</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">,</span><br/>                <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>                    <span class="tsd-kind-call-signature">delete</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">undefined</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>                    <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIAutoModerationRule</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>                    <span class="tsd-kind-call-signature">patch</span><span class="tsd-signature-symbol">(</span><br/>                        <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <a href="../types/RestArguments.html" class="tsd-signature-type tsd-kind-type-alias">RestArguments</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTPatchAPIAutoModerationRuleJSONBody</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>                    <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIAutoModerationRule</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-property">bans</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>            <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><br/>                <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTGetAPIGuildBansQuery</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTGetAPIGuildBansResult</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-signature-symbol">(</span><br/>                <span class="tsd-kind-parameter">userId</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>                <span class="tsd-kind-call-signature">delete</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">undefined</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIBan</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-kind-call-signature">put</span><span class="tsd-signature-symbol">(</span><br/>                    <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArguments.html" class="tsd-signature-type tsd-kind-type-alias">RestArguments</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">undefined</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">RESTPutAPIGuildBanJSONBody</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>                <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">undefined</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-property">&quot;bulk-bans&quot;</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>            <span class="tsd-kind-call-signature">post</span><span class="tsd-signature-symbol">(</span><br/>                <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <a href="../types/RestArguments.html" class="tsd-signature-type tsd-kind-type-alias">RestArguments</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTPostAPIGuildBulkBanJSONBody</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTPostAPIGuildBulkBanResult</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-property">channels</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>            <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><br/>                <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTGetAPIGuildChannelsResult</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-call-signature">patch</span><span class="tsd-signature-symbol">(</span><br/>                <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <a href="../types/RestArguments.html" class="tsd-signature-type tsd-kind-type-alias">RestArguments</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTPatchAPIGuildChannelPositionsJSONBody</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">undefined</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-call-signature">post</span><span class="tsd-signature-symbol">(</span><br/>                <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <a href="../types/RestArguments.html" class="tsd-signature-type tsd-kind-type-alias">RestArguments</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTPostAPIGuildChannelJSONBody</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIChannel</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-property">emojis</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>            <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTGetAPIGuildEmojisResult</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-call-signature">post</span><span class="tsd-signature-symbol">(</span><br/>                <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <a href="../types/RestArguments.html" class="tsd-signature-type tsd-kind-type-alias">RestArguments</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTPostAPIGuildEmojiJSONBody</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIEmoji</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-signature-symbol">(</span><br/>                <span class="tsd-kind-parameter">id</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>                <span class="tsd-kind-call-signature">delete</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">undefined</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIEmoji</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-kind-call-signature">patch</span><span class="tsd-signature-symbol">(</span><br/>                    <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <a href="../types/RestArguments.html" class="tsd-signature-type tsd-kind-type-alias">RestArguments</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTPatchAPIGuildEmojiJSONBody</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>                <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIEmoji</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-property">integrations</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>            <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><br/>                <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTGetAPIGuildIntegrationsResult</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">id</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span> <span class="tsd-kind-call-signature">delete</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">undefined</span><span class="tsd-signature-symbol">&gt;</span> <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-property">invites</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>            <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTGetAPIGuildInvitesResult</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-property">members</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>            <span class="tsd-kind-property">&quot;@me&quot;</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>                <span class="tsd-kind-call-signature">patch</span><span class="tsd-signature-symbol">(</span><br/>                    <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <a href="../types/RestArguments.html" class="tsd-signature-type tsd-kind-type-alias">RestArguments</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTPatchAPICurrentGuildMemberJSONBody</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>                <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIGuildMember</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">search</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>                <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><br/>                    <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTGetAPIGuildMembersSearchQuery</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>                <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTGetAPIGuildMembersSearchResult</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><br/>                <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTGetAPIGuildMembersQuery</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTGetAPIGuildMembersResult</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-signature-symbol">(</span><br/>                <span class="tsd-kind-parameter">id</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>                <span class="tsd-kind-call-signature">delete</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">undefined</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIGuildMember</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-kind-call-signature">patch</span><span class="tsd-signature-symbol">(</span><br/>                    <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <a href="../types/RestArguments.html" class="tsd-signature-type tsd-kind-type-alias">RestArguments</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTPatchAPIGuildMemberJSONBody</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>                <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIGuildMember</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-kind-call-signature">put</span><span class="tsd-signature-symbol">(</span><br/>                    <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArguments.html" class="tsd-signature-type tsd-kind-type-alias">RestArguments</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTPutAPIGuildMemberJSONBody</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>                <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTPutAPIGuildMemberResult</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-kind-call-signature">roles</span><span class="tsd-signature-symbol">(</span><br/>                    <span class="tsd-kind-parameter">id</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">,</span><br/>                <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>                    <span class="tsd-kind-call-signature">delete</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">undefined</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>                    <span class="tsd-kind-call-signature">put</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">undefined</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-property">mfa</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>            <span class="tsd-kind-call-signature">post</span><span class="tsd-signature-symbol">(</span><br/>                <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <a href="../types/RestArguments.html" class="tsd-signature-type tsd-kind-type-alias">RestArguments</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTPostAPIGuildsMFAJSONBody</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTPostAPIGuildsMFAJSONBody</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-property">preview</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span> <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIGuildPreview</span><span class="tsd-signature-symbol">&gt;</span> <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-property">prune</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>            <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><br/>                <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTGetAPIGuildPruneCountQuery</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTGetAPIGuildPruneCountResult</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-call-signature">post</span><span class="tsd-signature-symbol">(</span><br/>                <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <a href="../types/RestArguments.html" class="tsd-signature-type tsd-kind-type-alias">RestArguments</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTPostAPIGuildPruneJSONBody</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTPostAPIGuildPruneResult</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-property">regions</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>            <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><br/>                <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTGetAPIGuildVoiceRegionsResult</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-property">roles</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>            <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTGetAPIGuildRolesResult</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-call-signature">patch</span><span class="tsd-signature-symbol">(</span><br/>                <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <a href="../types/RestArguments.html" class="tsd-signature-type tsd-kind-type-alias">RestArguments</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTPatchAPIGuildRolePositionsJSONBody</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTPatchAPIGuildRolePositionsResult</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-call-signature">post</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <a href="../types/RestArguments.html" class="tsd-signature-type tsd-kind-type-alias">RestArguments</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTPostAPIGuildRoleJSONBody</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIRole</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-signature-symbol">(</span><br/>                <span class="tsd-kind-parameter">id</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>                <span class="tsd-kind-call-signature">delete</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">undefined</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIRole</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-kind-call-signature">patch</span><span class="tsd-signature-symbol">(</span><br/>                    <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <a href="../types/RestArguments.html" class="tsd-signature-type tsd-kind-type-alias">RestArguments</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTPatchAPIGuildRoleJSONBody</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>                <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIRole</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-property">&quot;scheduled-events&quot;</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>            <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><br/>                <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTGetAPIGuildScheduledEventsQuery</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTGetAPIGuildScheduledEventsResult</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-call-signature">post</span><span class="tsd-signature-symbol">(</span><br/>                <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <a href="../types/RestArguments.html" class="tsd-signature-type tsd-kind-type-alias">RestArguments</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTPostAPIGuildScheduledEventJSONBody</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIGuildScheduledEvent</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-signature-symbol">(</span><br/>                <span class="tsd-kind-parameter">id</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>                <span class="tsd-kind-property">users</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>                    <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><br/>                        <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTGetAPIGuildScheduledEventUsersQuery</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>                    <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTGetAPIGuildScheduledEventUsersResult</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-kind-call-signature">delete</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">undefined</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><br/>                    <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTGetAPIGuildScheduledEventQuery</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>                <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIGuildScheduledEvent</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-kind-call-signature">patch</span><span class="tsd-signature-symbol">(</span><br/>                    <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <a href="../types/RestArguments.html" class="tsd-signature-type tsd-kind-type-alias">RestArguments</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTPatchAPIGuildScheduledEventJSONBody</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>                <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIGuildScheduledEvent</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-property">&quot;soundboard-sounds&quot;</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>            <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><br/>                <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTGetAPIGuildSoundboardSoundsResult</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-call-signature">post</span><span class="tsd-signature-symbol">(</span><br/>                <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <a href="../types/RestArguments.html" class="tsd-signature-type tsd-kind-type-alias">RestArguments</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTPostAPIGuildSoundboardSound</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APISoundBoard</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-signature-symbol">(</span><br/>                <span class="tsd-kind-parameter">id</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>                <span class="tsd-kind-call-signature">delete</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">undefined</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APISoundBoard</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-kind-call-signature">patch</span><span class="tsd-signature-symbol">(</span><br/>                    <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArguments.html" class="tsd-signature-type tsd-kind-type-alias">RestArguments</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTPatchAPIGuildSoundboardSound</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>                <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APISoundBoard</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-property">stickers</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>            <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><br/>                <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTGetAPIGuildStickersResult</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-call-signature">post</span><span class="tsd-signature-symbol">(</span><br/>                <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <a href="../types/RestArguments.html" class="tsd-signature-type tsd-kind-type-alias">RestArguments</a><span class="tsd-signature-symbol">&lt;</span><br/>                    <span class="tsd-signature-type">Omit</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTPostAPIGuildStickerFormDataBody</span><span class="tsd-signature-symbol">,</span> <span class="tsd-signature-type">&quot;file&quot;</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>                    (<span class="tsd-signature-type">Omit</span><span class="tsd-signature-symbol">&lt;</span><a href="RawFile.html" class="tsd-signature-type tsd-kind-interface">RawFile</a><span class="tsd-signature-symbol">,</span> <span class="tsd-signature-type">&quot;key&quot;</span><span class="tsd-signature-symbol">&gt;</span> <span class="tsd-signature-symbol">&amp;</span> <span class="tsd-signature-symbol">{</span> <span class="tsd-kind-property">key</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">&quot;file&quot;</span> <span class="tsd-signature-symbol">}</span>)<span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">,</span><br/>                <span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APISticker</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-signature-symbol">(</span><br/>                <span class="tsd-kind-parameter">id</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>                <span class="tsd-kind-call-signature">delete</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">undefined</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APISticker</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-kind-call-signature">patch</span><span class="tsd-signature-symbol">(</span><br/>                    <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <a href="../types/RestArguments.html" class="tsd-signature-type tsd-kind-type-alias">RestArguments</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTPatchAPIGuildStickerJSONBody</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>                <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APISticker</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-property">templates</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>            <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><br/>                <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTGetAPIGuildTemplatesResult</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-call-signature">post</span><span class="tsd-signature-symbol">(</span><br/>                <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <a href="../types/RestArguments.html" class="tsd-signature-type tsd-kind-type-alias">RestArguments</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTPostAPIGuildTemplatesJSONBody</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APITemplate</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-signature-symbol">(</span><br/>                <span class="tsd-kind-parameter">code</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>                <span class="tsd-kind-call-signature">delete</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APITemplate</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-kind-call-signature">patch</span><span class="tsd-signature-symbol">(</span><br/>                    <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <a href="../types/RestArguments.html" class="tsd-signature-type tsd-kind-type-alias">RestArguments</a><span class="tsd-signature-symbol">&lt;</span><br/>                        <span class="tsd-signature-type">AddUndefinedToPossiblyUndefinedPropertiesOfInterface</span><span class="tsd-signature-symbol">&lt;</span><br/>                            <span class="tsd-signature-type">Partial</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTPostAPIGuildTemplatesJSONBody</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>                        <span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>                    <span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>                <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APITemplate</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-kind-call-signature">put</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APITemplate</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-property">threads</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>            <span class="tsd-kind-property">active</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>                <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><br/>                    <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">,</span><br/>                <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><br/>                    <span class="tsd-signature-symbol">{</span><br/>                        <span class="tsd-kind-property">members</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">APIThreadMember</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">;</span><br/>                        <span class="tsd-kind-property">threads</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">APIChannel</span><span class="tsd-signature-symbol">[]</span> <span class="tsd-signature-symbol">&amp;</span> <span class="tsd-signature-type">Partial</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIThreadChannel</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>                    <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">,</span><br/>                <span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-property">&quot;vanity-url&quot;</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>            <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><br/>                <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTGetAPIGuildVanityUrlResult</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-property">&quot;voice-states&quot;</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>            <span class="tsd-kind-property">&quot;@me&quot;</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>                <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIVoiceState</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-kind-call-signature">patch</span><span class="tsd-signature-symbol">(</span><br/>                    <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <a href="../types/RestArguments.html" class="tsd-signature-type tsd-kind-type-alias">RestArguments</a><span class="tsd-signature-symbol">&lt;</span><br/>                        <span class="tsd-signature-type">RESTPatchAPIGuildVoiceStateCurrentMemberJSONBody</span><span class="tsd-signature-symbol">,</span><br/>                    <span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>                <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">undefined</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-signature-symbol">(</span><br/>                <span class="tsd-kind-parameter">userId</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>                <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIVoiceState</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-kind-call-signature">patch</span><span class="tsd-signature-symbol">(</span><br/>                    <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <a href="../types/RestArguments.html" class="tsd-signature-type tsd-kind-type-alias">RestArguments</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTPatchAPIGuildVoiceStateUserJSONBody</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>                <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">undefined</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-property">webhooks</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>            <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><br/>                <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTGetAPIGuildWebhooksResult</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-property">&quot;welcome-screen&quot;</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>            <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIGuildWelcomeScreen</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-call-signature">patch</span><span class="tsd-signature-symbol">(</span><br/>                <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <a href="../types/RestArguments.html" class="tsd-signature-type tsd-kind-type-alias">RestArguments</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTPatchAPIGuildWelcomeScreenJSONBody</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIGuildWelcomeScreen</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-property">widget</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>            <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><br/>                <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-symbol">{</span> <span class="tsd-kind-property">style</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">string</span> <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIGuildWidgetSettings</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-call-signature">patch</span><span class="tsd-signature-symbol">(</span><br/>                <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <a href="../types/RestArguments.html" class="tsd-signature-type tsd-kind-type-alias">RestArguments</a><span class="tsd-signature-symbol">&lt;</span><br/>                    <span class="tsd-signature-type">AddUndefinedToPossiblyUndefinedPropertiesOfInterface</span><span class="tsd-signature-symbol">&lt;</span><br/>                        <span class="tsd-signature-type">Partial</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIGuildWidgetSettings</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>                    <span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>                <span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIGuildWidgetSettings</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-property">&quot;widget.json&quot;</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>            <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIGuildWidget</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-property">&quot;widget.png&quot;</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>            <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><br/>                <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTGetAPIGuildWidgetImageQuery</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">ArrayBuffer</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-call-signature">delete</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">undefined</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTGetAPIGuildQuery</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIGuild</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-call-signature">patch</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <a href="../types/RestArguments.html" class="tsd-signature-type tsd-kind-type-alias">RestArguments</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTPatchAPIGuildJSONBody</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIGuild</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/><span class="tsd-signature-symbol">}</span></div><aside class="tsd-sources"><p>Inherited from GuildRoutes.guilds</p><ul><li>Defined in <a href="https://github.com/tiramisulabs/seyfert//blob/9ae4290ef220ffc96449d469d16d76b2031eca69/src/api/Routes/guilds.ts#L129">src/api/Routes/guilds.ts:129</a></li></ul></aside></section><section class="tsd-panel tsd-member tsd-is-inherited"><h3 class="tsd-anchor-link" id="interactions"><span>interactions</span><a href="#interactions" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">interactions</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">(</span><br/>    <span class="tsd-kind-parameter">id</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">,</span><br/><span class="tsd-signature-symbol">)</span> <span class="tsd-signature-symbol">=&gt;</span> <span class="tsd-signature-symbol">(</span><br/>    <span class="tsd-kind-parameter">token</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">,</span><br/><span class="tsd-signature-symbol">)</span> <span class="tsd-signature-symbol">=&gt;</span> <span class="tsd-signature-symbol">{</span><br/>    <span class="tsd-kind-property">callback</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>        <span class="tsd-kind-call-signature">post</span><span class="tsd-signature-symbol">(</span><br/>            <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <a href="../types/RestArguments.html" class="tsd-signature-type tsd-kind-type-alias">RestArguments</a><span class="tsd-signature-symbol">&lt;</span><br/>                <span class="tsd-signature-type">APIInteractionResponse</span><span class="tsd-signature-symbol">,</span><br/>                <span class="tsd-signature-type">Omit</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTPostAPIInteractionCallbackQuery</span><span class="tsd-signature-symbol">,</span> <span class="tsd-signature-type">&quot;with_response&quot;</span><span class="tsd-signature-symbol">&gt;</span> <span class="tsd-signature-symbol">&amp;</span> <span class="tsd-signature-symbol">{</span><br/>                    <span class="tsd-kind-property">with_response</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">true</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>        <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTPostAPIInteractionCallbackResult</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-call-signature">post</span><span class="tsd-signature-symbol">(</span><br/>            <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <a href="../types/RestArguments.html" class="tsd-signature-type tsd-kind-type-alias">RestArguments</a><span class="tsd-signature-symbol">&lt;</span><br/>                <span class="tsd-signature-type">APIInteractionResponse</span><span class="tsd-signature-symbol">,</span><br/>                <span class="tsd-signature-type">Omit</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTPostAPIInteractionCallbackQuery</span><span class="tsd-signature-symbol">,</span> <span class="tsd-signature-type">&quot;with_response&quot;</span><span class="tsd-signature-symbol">&gt;</span> <span class="tsd-signature-symbol">&amp;</span> <span class="tsd-signature-symbol">{</span><br/>                    <span class="tsd-kind-property">with_response</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">false</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>        <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">undefined</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-call-signature">post</span><span class="tsd-signature-symbol">(</span><br/>            <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <a href="../types/RestArguments.html" class="tsd-signature-type tsd-kind-type-alias">RestArguments</a><span class="tsd-signature-symbol">&lt;</span><br/>                <span class="tsd-signature-type">APIInteractionResponse</span><span class="tsd-signature-symbol">,</span><br/>                <span class="tsd-signature-type">RESTPostAPIInteractionCallbackQuery</span><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>        <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">undefined</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">RESTPostAPIInteractionCallbackResult</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/><span class="tsd-signature-symbol">}</span></div><aside class="tsd-sources"><p>Inherited from InteractionRoutes.interactions</p><ul><li>Defined in <a href="https://github.com/tiramisulabs/seyfert//blob/9ae4290ef220ffc96449d469d16d76b2031eca69/src/api/Routes/interactions.ts#L10">src/api/Routes/interactions.ts:10</a></li></ul></aside></section><section class="tsd-panel tsd-member tsd-is-inherited"><h3 class="tsd-anchor-link" id="soundboard-default-sounds"><span>soundboard-<wbr/>default-<wbr/>sounds</span><a href="#soundboard-default-sounds" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">&quot;soundboard-default-sounds&quot;</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>    <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><br/>        <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">,</span><br/>    <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTGetAPIDefaultsSoundboardSoundsResult</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/><span class="tsd-signature-symbol">}</span></div><aside class="tsd-sources"><p>Inherited from SoundboardRoutes.soundboard-default-sounds</p><ul><li>Defined in <a href="https://github.com/tiramisulabs/seyfert//blob/9ae4290ef220ffc96449d469d16d76b2031eca69/src/api/Routes/soundboard.ts#L5">src/api/Routes/soundboard.ts:5</a></li></ul></aside></section><section class="tsd-panel tsd-member tsd-is-inherited"><h3 class="tsd-anchor-link" id="stage-instances"><span>stage-<wbr/>instances</span><a href="#stage-instances" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">&quot;stage-instances&quot;</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>    <span class="tsd-kind-call-signature">post</span><span class="tsd-signature-symbol">(</span><br/>        <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <a href="../types/RestArguments.html" class="tsd-signature-type tsd-kind-type-alias">RestArguments</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTPostAPIStageInstanceJSONBody</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>    <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIStageInstance</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-signature-symbol">(</span><br/>        <span class="tsd-kind-parameter">id</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">,</span><br/>    <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>        <span class="tsd-kind-call-signature">delete</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">undefined</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIStageInstance</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-call-signature">patch</span><span class="tsd-signature-symbol">(</span><br/>            <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <a href="../types/RestArguments.html" class="tsd-signature-type tsd-kind-type-alias">RestArguments</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTPatchAPIStageInstanceJSONBody</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>        <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIStageInstance</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/><span class="tsd-signature-symbol">}</span></div><aside class="tsd-sources"><p>Inherited from StageInstanceRoutes.stage-instances</p><ul><li>Defined in <a href="https://github.com/tiramisulabs/seyfert//blob/9ae4290ef220ffc96449d469d16d76b2031eca69/src/api/Routes/stage-instances.ts#L12">src/api/Routes/stage-instances.ts:12</a></li></ul></aside></section><section class="tsd-panel tsd-member tsd-is-inherited"><h3 class="tsd-anchor-link" id="sticker-packs"><span>sticker-<wbr/>packs</span><a href="#sticker-packs" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">&quot;sticker-packs&quot;</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>    <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTGetStickerPacksResult</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">id</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span> <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIStickerPack</span><span class="tsd-signature-symbol">&gt;</span> <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/><span class="tsd-signature-symbol">}</span></div><aside class="tsd-sources"><p>Inherited from StickerRoutes.sticker-packs</p><ul><li>Defined in <a href="https://github.com/tiramisulabs/seyfert//blob/9ae4290ef220ffc96449d469d16d76b2031eca69/src/api/Routes/stickers.ts#L8">src/api/Routes/stickers.ts:8</a></li></ul></aside></section><section class="tsd-panel tsd-member tsd-is-inherited"><h3 class="tsd-anchor-link" id="users"><span>users</span><a href="#users" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">users</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>    <span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">id</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span> <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIUser</span><span class="tsd-signature-symbol">&gt;</span> <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-signature-symbol">(</span><br/>        <span class="tsd-kind-parameter">id</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">&quot;@me&quot;</span><span class="tsd-signature-symbol">,</span><br/>    <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>        <span class="tsd-kind-property">channels</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>            <span class="tsd-kind-call-signature">post</span><span class="tsd-signature-symbol">(</span><br/>                <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <a href="../types/RestArguments.html" class="tsd-signature-type tsd-kind-type-alias">RestArguments</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTPostAPICurrentUserCreateDMChannelJSONBody</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIDMChannel</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-property">connections</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>            <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><br/>                <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTGetAPICurrentUserConnectionsResult</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-property">guilds</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>            <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><br/>                <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTGetAPICurrentUserGuildsQuery</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTGetAPICurrentUserGuildsResult</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-signature-symbol">(</span><br/>                <span class="tsd-kind-parameter">id</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>                <span class="tsd-kind-property">member</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span> <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIGuildMember</span><span class="tsd-signature-symbol">&gt;</span> <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-kind-call-signature">delete</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">undefined</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-call-signature">applications</span><span class="tsd-signature-symbol">(</span><br/>            <span class="tsd-kind-parameter">applicationId</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">,</span><br/>        <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>            <span class="tsd-kind-property">&quot;role-connection&quot;</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>                <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><br/>                    <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">,</span><br/>                <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIApplicationRoleConnection</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-kind-call-signature">put</span><span class="tsd-signature-symbol">(</span><br/>                    <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <a href="../types/RestArguments.html" class="tsd-signature-type tsd-kind-type-alias">RestArguments</a><span class="tsd-signature-symbol">&lt;</span><br/>                        <span class="tsd-signature-type">RESTPutAPICurrentUserApplicationRoleConnectionJSONBody</span><span class="tsd-signature-symbol">,</span><br/>                    <span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>                <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIApplicationRoleConnection</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIUser</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-call-signature">patch</span><span class="tsd-signature-symbol">(</span><br/>            <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <a href="../types/RestArguments.html" class="tsd-signature-type tsd-kind-type-alias">RestArguments</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTPatchAPICurrentUserJSONBody</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>        <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIUser</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/><span class="tsd-signature-symbol">}</span></div><aside class="tsd-sources"><p>Inherited from UserRoutes.users</p><ul><li>Defined in <a href="https://github.com/tiramisulabs/seyfert//blob/9ae4290ef220ffc96449d469d16d76b2031eca69/src/api/Routes/users.ts#L20">src/api/Routes/users.ts:20</a></li></ul></aside></section><section class="tsd-panel tsd-member tsd-is-inherited"><h3 class="tsd-anchor-link" id="voice"><span>voice</span><a href="#voice" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">voice</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>    <span class="tsd-kind-property">region</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>        <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTGetAPIVoiceRegionsResult</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/><span class="tsd-signature-symbol">}</span></div><aside class="tsd-sources"><p>Inherited from VoiceRoutes.voice</p><ul><li>Defined in <a href="https://github.com/tiramisulabs/seyfert//blob/9ae4290ef220ffc96449d469d16d76b2031eca69/src/api/Routes/voice.ts#L5">src/api/Routes/voice.ts:5</a></li></ul></aside></section></section></details><details class="tsd-panel-group tsd-member-group tsd-accordion" open><summary class="tsd-accordion-summary" data-key="section-Methods"><svg width="20" height="20" viewBox="0 0 24 24" fill="none" aria-hidden="true"><use href="../assets/icons.svg#icon-chevronDown"></use></svg><h2>Methods</h2></summary><section><section class="tsd-panel tsd-member tsd-is-inherited"><h3 class="tsd-anchor-link" id="channels"><span>channels</span><a href="#channels" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><ul class="tsd-signatures tsd-is-inherited"><li class="tsd-is-inherited"><div class="tsd-signature tsd-anchor-link" id="channels-1"><span class="tsd-kind-call-signature">channels</span><span class="tsd-signature-symbol">(</span><br/>    <span class="tsd-kind-parameter">id</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">,</span><br/><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>    <span class="tsd-kind-property">followers</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>        <span class="tsd-kind-call-signature">post</span><span class="tsd-signature-symbol">(</span><br/>            <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <a href="../types/RestArguments.html" class="tsd-signature-type tsd-kind-type-alias">RestArguments</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTPostAPIChannelFollowersJSONBody</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>        <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIFollowedChannel</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-property">invites</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>        <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><br/>            <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">,</span><br/>        <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTGetAPIChannelInvitesResult</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-call-signature">post</span><span class="tsd-signature-symbol">(</span><br/>            <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <a href="../types/RestArguments.html" class="tsd-signature-type tsd-kind-type-alias">RestArguments</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTPostAPIChannelInviteJSONBody</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>        <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIExtendedInvite</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-property">messages</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>        <span class="tsd-kind-property">&quot;bulk-delete&quot;</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>            <span class="tsd-kind-call-signature">post</span><span class="tsd-signature-symbol">(</span><br/>                <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <a href="../types/RestArguments.html" class="tsd-signature-type tsd-kind-type-alias">RestArguments</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTPostAPIChannelMessagesBulkDeleteJSONBody</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">undefined</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><br/>            <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTGetAPIChannelMessagesQuery</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>        <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTGetAPIChannelMessagesResult</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-call-signature">post</span><span class="tsd-signature-symbol">(</span><br/>            <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <a href="../types/RestArguments.html" class="tsd-signature-type tsd-kind-type-alias">RestArguments</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTPostAPIChannelMessageJSONBody</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>        <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIMessage</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-signature-symbol">(</span><br/>            <span class="tsd-kind-parameter">id</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">,</span><br/>        <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>            <span class="tsd-kind-property">crosspost</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span> <span class="tsd-kind-call-signature">post</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIMessage</span><span class="tsd-signature-symbol">&gt;</span> <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">reactions</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>                <span class="tsd-kind-call-signature">delete</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">undefined</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-signature-symbol">(</span><br/>                    <span class="tsd-kind-parameter">emoji</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">,</span><br/>                <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>                    <span class="tsd-kind-call-signature">delete</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">undefined</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>                    <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><br/>                        <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">&lt;</span><br/>                            <span class="tsd-signature-type">RESTGetAPIChannelMessageReactionUsersQuery</span><span class="tsd-signature-symbol">,</span><br/>                        <span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>                    <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTGetAPIChannelMessageReactionUsersResult</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>                    <span class="tsd-signature-symbol">(</span><br/>                        <span class="tsd-kind-parameter">id</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">&quot;@me&quot;</span><span class="tsd-signature-symbol">,</span><br/>                    <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>                        <span class="tsd-kind-call-signature">delete</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">undefined</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>                        <span class="tsd-kind-call-signature">put</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">undefined</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>                    <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>                    <span class="tsd-signature-symbol">(</span><br/>                        <span class="tsd-kind-parameter">id</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">,</span><br/>                    <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span> <span class="tsd-kind-call-signature">delete</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">undefined</span><span class="tsd-signature-symbol">&gt;</span> <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">threads</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>                <span class="tsd-kind-call-signature">post</span><span class="tsd-signature-symbol">(</span><br/>                    <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <a href="../types/RestArguments.html" class="tsd-signature-type tsd-kind-type-alias">RestArguments</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTPostAPIChannelMessagesThreadsJSONBody</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>                <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIChannel</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-call-signature">delete</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">undefined</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIMessage</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-call-signature">patch</span><span class="tsd-signature-symbol">(</span><br/>                <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <a href="../types/RestArguments.html" class="tsd-signature-type tsd-kind-type-alias">RestArguments</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTPatchAPIChannelMessageJSONBody</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIMessage</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-property">permissions</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">(</span><br/>        <span class="tsd-kind-parameter">id</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">,</span><br/>    <span class="tsd-signature-symbol">)</span> <span class="tsd-signature-symbol">=&gt;</span> <span class="tsd-signature-symbol">{</span><br/>        <span class="tsd-kind-call-signature">delete</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">undefined</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-call-signature">put</span><span class="tsd-signature-symbol">(</span><br/>            <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArguments.html" class="tsd-signature-type tsd-kind-type-alias">RestArguments</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTPutAPIChannelPermissionJSONBody</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>        <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">undefined</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-property">pins</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>        <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTGetAPIChannelPinsResult</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-signature-symbol">(</span><br/>            <span class="tsd-kind-parameter">id</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">,</span><br/>        <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>            <span class="tsd-kind-call-signature">delete</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">undefined</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-call-signature">put</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">undefined</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-property">recipients</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">(</span><br/>        <span class="tsd-kind-parameter">id</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">,</span><br/>    <span class="tsd-signature-symbol">)</span> <span class="tsd-signature-symbol">=&gt;</span> <span class="tsd-signature-symbol">{</span><br/>        <span class="tsd-kind-call-signature">delete</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">unknown</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-call-signature">put</span><span class="tsd-signature-symbol">(</span><br/>            <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArguments.html" class="tsd-signature-type tsd-kind-type-alias">RestArguments</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTPutAPIChannelRecipientJSONBody</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>        <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">unknown</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-property">&quot;send-soundboard-sound&quot;</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>        <span class="tsd-kind-call-signature">post</span><span class="tsd-signature-symbol">(</span><br/>            <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <a href="../types/RestArguments.html" class="tsd-signature-type tsd-kind-type-alias">RestArguments</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTPostAPISendSoundboardSound</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>        <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">undefined</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-property">&quot;thread-members&quot;</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>        <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><br/>            <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTGetAPIChannelThreadMembersQuery</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>        <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTGetAPIChannelThreadMembersResult</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-signature-symbol">(</span><br/>            <span class="tsd-kind-parameter">id</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">&quot;@me&quot;</span><span class="tsd-signature-symbol">,</span><br/>        <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>            <span class="tsd-kind-call-signature">delete</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">undefined</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-call-signature">put</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">undefined</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-signature-symbol">(</span><br/>            <span class="tsd-kind-parameter">id</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">,</span><br/>        <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>            <span class="tsd-kind-call-signature">delete</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">undefined</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><br/>                <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTGetAPIChannelThreadMemberQuery</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIThreadMember</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-call-signature">put</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">undefined</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-property">threads</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>        <span class="tsd-kind-property">archived</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>            <span class="tsd-kind-property">private</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>                <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><br/>                    <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTGetAPIChannelThreadsArchivedQuery</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>                <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTGetAPIChannelUsersThreadsArchivedResult</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">public</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>                <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><br/>                    <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTGetAPIChannelThreadsArchivedQuery</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>                <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTGetAPIChannelUsersThreadsArchivedResult</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-call-signature">post</span><span class="tsd-signature-symbol">(</span><br/>            <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <a href="../types/RestArguments.html" class="tsd-signature-type tsd-kind-type-alias">RestArguments</a><span class="tsd-signature-symbol">&lt;</span><br/>                <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">RESTPostAPIGuildForumThreadsJSONBody</span><br/>                <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">RESTPostAPIChannelThreadsJSONBody</span><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>        <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIChannel</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-property">typing</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span> <span class="tsd-kind-call-signature">post</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">undefined</span><span class="tsd-signature-symbol">&gt;</span> <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-property">users</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">(</span><br/>        <span class="tsd-kind-parameter">id</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">&quot;@me&quot;</span><span class="tsd-signature-symbol">,</span><br/>    <span class="tsd-signature-symbol">)</span> <span class="tsd-signature-symbol">=&gt;</span> <span class="tsd-signature-symbol">{</span><br/>        <span class="tsd-kind-property">threads</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>            <span class="tsd-kind-property">archived</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>                <span class="tsd-kind-property">private</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>                    <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><br/>                        <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTGetAPIChannelThreadsArchivedQuery</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>                    <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTGetAPIChannelUsersThreadsArchivedResult</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-property">&quot;voice-status&quot;</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>        <span class="tsd-kind-call-signature">put</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <a href="../types/RestArguments.html" class="tsd-signature-type tsd-kind-type-alias">RestArguments</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-symbol">{</span> <span class="tsd-kind-property">status</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">null</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">string</span> <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">undefined</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-property">webhooks</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>        <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTGetAPIGuildWebhooksResult</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-call-signature">post</span><span class="tsd-signature-symbol">(</span><br/>            <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <a href="../types/RestArguments.html" class="tsd-signature-type tsd-kind-type-alias">RestArguments</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTPostAPIChannelWebhookJSONBody</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>        <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIWebhook</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-call-signature">delete</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIChannel</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIChannel</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-call-signature">patch</span><span class="tsd-signature-symbol">(</span><br/>        <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <a href="../types/RestArguments.html" class="tsd-signature-type tsd-kind-type-alias">RestArguments</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTPatchAPIChannelJSONBody</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>    <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIChannel</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-call-signature">polls</span><span class="tsd-signature-symbol">(</span><br/>        <span class="tsd-kind-parameter">messageId</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">,</span><br/>    <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>        <span class="tsd-kind-property">expire</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span> <span class="tsd-kind-call-signature">post</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIMessage</span><span class="tsd-signature-symbol">&gt;</span> <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-call-signature">answers</span><span class="tsd-signature-symbol">(</span><br/>            <span class="tsd-kind-parameter">id</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">ValidAnswerId</span><span class="tsd-signature-symbol">,</span><br/>        <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>            <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><br/>                <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTGetAPIPollAnswerVotersQuery</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTGetAPIPollAnswerVotersResult</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/><span class="tsd-signature-symbol">}</span><a href="#channels-1" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></div><div class="tsd-description"><div class="tsd-parameters"><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameter-list"><li><span><span class="tsd-kind-parameter">id</span>: <span class="tsd-signature-type">string</span></span></li></ul></div><h4 class="tsd-returns-title">Returns <span class="tsd-signature-symbol">{</span><br/>    <span class="tsd-kind-property">followers</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>        <span class="tsd-kind-call-signature">post</span><span class="tsd-signature-symbol">(</span><br/>            <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <a href="../types/RestArguments.html" class="tsd-signature-type tsd-kind-type-alias">RestArguments</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTPostAPIChannelFollowersJSONBody</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>        <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIFollowedChannel</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-property">invites</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>        <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><br/>            <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">,</span><br/>        <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTGetAPIChannelInvitesResult</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-call-signature">post</span><span class="tsd-signature-symbol">(</span><br/>            <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <a href="../types/RestArguments.html" class="tsd-signature-type tsd-kind-type-alias">RestArguments</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTPostAPIChannelInviteJSONBody</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>        <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIExtendedInvite</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-property">messages</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>        <span class="tsd-kind-property">&quot;bulk-delete&quot;</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>            <span class="tsd-kind-call-signature">post</span><span class="tsd-signature-symbol">(</span><br/>                <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <a href="../types/RestArguments.html" class="tsd-signature-type tsd-kind-type-alias">RestArguments</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTPostAPIChannelMessagesBulkDeleteJSONBody</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">undefined</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><br/>            <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTGetAPIChannelMessagesQuery</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>        <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTGetAPIChannelMessagesResult</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-call-signature">post</span><span class="tsd-signature-symbol">(</span><br/>            <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <a href="../types/RestArguments.html" class="tsd-signature-type tsd-kind-type-alias">RestArguments</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTPostAPIChannelMessageJSONBody</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>        <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIMessage</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-signature-symbol">(</span><br/>            <span class="tsd-kind-parameter">id</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">,</span><br/>        <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>            <span class="tsd-kind-property">crosspost</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span> <span class="tsd-kind-call-signature">post</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIMessage</span><span class="tsd-signature-symbol">&gt;</span> <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">reactions</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>                <span class="tsd-kind-call-signature">delete</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">undefined</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-signature-symbol">(</span><br/>                    <span class="tsd-kind-parameter">emoji</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">,</span><br/>                <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>                    <span class="tsd-kind-call-signature">delete</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">undefined</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>                    <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><br/>                        <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">&lt;</span><br/>                            <span class="tsd-signature-type">RESTGetAPIChannelMessageReactionUsersQuery</span><span class="tsd-signature-symbol">,</span><br/>                        <span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>                    <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTGetAPIChannelMessageReactionUsersResult</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>                    <span class="tsd-signature-symbol">(</span><br/>                        <span class="tsd-kind-parameter">id</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">&quot;@me&quot;</span><span class="tsd-signature-symbol">,</span><br/>                    <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>                        <span class="tsd-kind-call-signature">delete</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">undefined</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>                        <span class="tsd-kind-call-signature">put</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">undefined</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>                    <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>                    <span class="tsd-signature-symbol">(</span><br/>                        <span class="tsd-kind-parameter">id</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">,</span><br/>                    <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span> <span class="tsd-kind-call-signature">delete</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">undefined</span><span class="tsd-signature-symbol">&gt;</span> <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">threads</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>                <span class="tsd-kind-call-signature">post</span><span class="tsd-signature-symbol">(</span><br/>                    <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <a href="../types/RestArguments.html" class="tsd-signature-type tsd-kind-type-alias">RestArguments</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTPostAPIChannelMessagesThreadsJSONBody</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>                <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIChannel</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-call-signature">delete</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">undefined</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIMessage</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-call-signature">patch</span><span class="tsd-signature-symbol">(</span><br/>                <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <a href="../types/RestArguments.html" class="tsd-signature-type tsd-kind-type-alias">RestArguments</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTPatchAPIChannelMessageJSONBody</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIMessage</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-property">permissions</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">(</span><br/>        <span class="tsd-kind-parameter">id</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">,</span><br/>    <span class="tsd-signature-symbol">)</span> <span class="tsd-signature-symbol">=&gt;</span> <span class="tsd-signature-symbol">{</span><br/>        <span class="tsd-kind-call-signature">delete</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">undefined</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-call-signature">put</span><span class="tsd-signature-symbol">(</span><br/>            <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArguments.html" class="tsd-signature-type tsd-kind-type-alias">RestArguments</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTPutAPIChannelPermissionJSONBody</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>        <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">undefined</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-property">pins</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>        <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTGetAPIChannelPinsResult</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-signature-symbol">(</span><br/>            <span class="tsd-kind-parameter">id</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">,</span><br/>        <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>            <span class="tsd-kind-call-signature">delete</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">undefined</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-call-signature">put</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">undefined</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-property">recipients</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">(</span><br/>        <span class="tsd-kind-parameter">id</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">,</span><br/>    <span class="tsd-signature-symbol">)</span> <span class="tsd-signature-symbol">=&gt;</span> <span class="tsd-signature-symbol">{</span><br/>        <span class="tsd-kind-call-signature">delete</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">unknown</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-call-signature">put</span><span class="tsd-signature-symbol">(</span><br/>            <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArguments.html" class="tsd-signature-type tsd-kind-type-alias">RestArguments</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTPutAPIChannelRecipientJSONBody</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>        <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">unknown</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-property">&quot;send-soundboard-sound&quot;</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>        <span class="tsd-kind-call-signature">post</span><span class="tsd-signature-symbol">(</span><br/>            <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <a href="../types/RestArguments.html" class="tsd-signature-type tsd-kind-type-alias">RestArguments</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTPostAPISendSoundboardSound</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>        <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">undefined</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-property">&quot;thread-members&quot;</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>        <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><br/>            <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTGetAPIChannelThreadMembersQuery</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>        <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTGetAPIChannelThreadMembersResult</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-signature-symbol">(</span><br/>            <span class="tsd-kind-parameter">id</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">&quot;@me&quot;</span><span class="tsd-signature-symbol">,</span><br/>        <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>            <span class="tsd-kind-call-signature">delete</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">undefined</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-call-signature">put</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">undefined</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-signature-symbol">(</span><br/>            <span class="tsd-kind-parameter">id</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">,</span><br/>        <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>            <span class="tsd-kind-call-signature">delete</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">undefined</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><br/>                <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTGetAPIChannelThreadMemberQuery</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIThreadMember</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-call-signature">put</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">undefined</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-property">threads</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>        <span class="tsd-kind-property">archived</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>            <span class="tsd-kind-property">private</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>                <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><br/>                    <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTGetAPIChannelThreadsArchivedQuery</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>                <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTGetAPIChannelUsersThreadsArchivedResult</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-property">public</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>                <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><br/>                    <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTGetAPIChannelThreadsArchivedQuery</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>                <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTGetAPIChannelUsersThreadsArchivedResult</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-call-signature">post</span><span class="tsd-signature-symbol">(</span><br/>            <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <a href="../types/RestArguments.html" class="tsd-signature-type tsd-kind-type-alias">RestArguments</a><span class="tsd-signature-symbol">&lt;</span><br/>                <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">RESTPostAPIGuildForumThreadsJSONBody</span><br/>                <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">RESTPostAPIChannelThreadsJSONBody</span><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>        <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIChannel</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-property">typing</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span> <span class="tsd-kind-call-signature">post</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">undefined</span><span class="tsd-signature-symbol">&gt;</span> <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-property">users</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">(</span><br/>        <span class="tsd-kind-parameter">id</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">&quot;@me&quot;</span><span class="tsd-signature-symbol">,</span><br/>    <span class="tsd-signature-symbol">)</span> <span class="tsd-signature-symbol">=&gt;</span> <span class="tsd-signature-symbol">{</span><br/>        <span class="tsd-kind-property">threads</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>            <span class="tsd-kind-property">archived</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>                <span class="tsd-kind-property">private</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>                    <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><br/>                        <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTGetAPIChannelThreadsArchivedQuery</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>                    <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTGetAPIChannelUsersThreadsArchivedResult</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>                <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-property">&quot;voice-status&quot;</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>        <span class="tsd-kind-call-signature">put</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <a href="../types/RestArguments.html" class="tsd-signature-type tsd-kind-type-alias">RestArguments</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-symbol">{</span> <span class="tsd-kind-property">status</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">null</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">string</span> <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">undefined</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-property">webhooks</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>        <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTGetAPIGuildWebhooksResult</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-call-signature">post</span><span class="tsd-signature-symbol">(</span><br/>            <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <a href="../types/RestArguments.html" class="tsd-signature-type tsd-kind-type-alias">RestArguments</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTPostAPIChannelWebhookJSONBody</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>        <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIWebhook</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-call-signature">delete</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIChannel</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIChannel</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-call-signature">patch</span><span class="tsd-signature-symbol">(</span><br/>        <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <a href="../types/RestArguments.html" class="tsd-signature-type tsd-kind-type-alias">RestArguments</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTPatchAPIChannelJSONBody</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>    <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIChannel</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-call-signature">polls</span><span class="tsd-signature-symbol">(</span><br/>        <span class="tsd-kind-parameter">messageId</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">,</span><br/>    <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>        <span class="tsd-kind-property">expire</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span> <span class="tsd-kind-call-signature">post</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIMessage</span><span class="tsd-signature-symbol">&gt;</span> <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-call-signature">answers</span><span class="tsd-signature-symbol">(</span><br/>            <span class="tsd-kind-parameter">id</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">ValidAnswerId</span><span class="tsd-signature-symbol">,</span><br/>        <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>            <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><br/>                <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTGetAPIPollAnswerVotersQuery</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTGetAPIPollAnswerVotersResult</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/><span class="tsd-signature-symbol">}</span></h4><aside class="tsd-sources"><p>Inherited from ChannelRoutes.channels</p><ul><li>Defined in <a href="https://github.com/tiramisulabs/seyfert//blob/9ae4290ef220ffc96449d469d16d76b2031eca69/src/api/Routes/channels.ts#L63">src/api/Routes/channels.ts:63</a></li></ul></aside></div></li></ul></section><section class="tsd-panel tsd-member tsd-is-inherited"><h3 class="tsd-anchor-link" id="invites"><span>invites</span><a href="#invites" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><ul class="tsd-signatures tsd-is-inherited"><li class="tsd-is-inherited"><div class="tsd-signature tsd-anchor-link" id="invites-1"><span class="tsd-kind-call-signature">invites</span><span class="tsd-signature-symbol">(</span><br/>    <span class="tsd-kind-parameter">id</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">,</span><br/><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>    <span class="tsd-kind-call-signature">delete</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIInvite</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArguments.html" class="tsd-signature-type tsd-kind-type-alias">RestArguments</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTGetAPIInviteQuery</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIInvite</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/><span class="tsd-signature-symbol">}</span><a href="#invites-1" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></div><div class="tsd-description"><div class="tsd-parameters"><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameter-list"><li><span><span class="tsd-kind-parameter">id</span>: <span class="tsd-signature-type">string</span></span></li></ul></div><h4 class="tsd-returns-title">Returns <span class="tsd-signature-symbol">{</span><br/>    <span class="tsd-kind-call-signature">delete</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIInvite</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArguments.html" class="tsd-signature-type tsd-kind-type-alias">RestArguments</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTGetAPIInviteQuery</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIInvite</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/><span class="tsd-signature-symbol">}</span></h4><aside class="tsd-sources"><p>Inherited from InviteRoutes.invites</p><ul><li>Defined in <a href="https://github.com/tiramisulabs/seyfert//blob/9ae4290ef220ffc96449d469d16d76b2031eca69/src/api/Routes/invites.ts#L5">src/api/Routes/invites.ts:5</a></li></ul></aside></div></li></ul></section><section class="tsd-panel tsd-member tsd-is-inherited"><h3 class="tsd-anchor-link" id="stickers"><span>stickers</span><a href="#stickers" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><ul class="tsd-signatures tsd-is-inherited"><li class="tsd-is-inherited"><div class="tsd-signature tsd-anchor-link" id="stickers-1"><span class="tsd-kind-call-signature">stickers</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">id</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span> <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APISticker</span><span class="tsd-signature-symbol">&gt;</span> <span class="tsd-signature-symbol">}</span><a href="#stickers-1" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></div><div class="tsd-description"><div class="tsd-parameters"><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameter-list"><li><span><span class="tsd-kind-parameter">id</span>: <span class="tsd-signature-type">string</span></span></li></ul></div><h4 class="tsd-returns-title">Returns <span class="tsd-signature-symbol">{</span> <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APISticker</span><span class="tsd-signature-symbol">&gt;</span> <span class="tsd-signature-symbol">}</span></h4><aside class="tsd-sources"><p>Inherited from StickerRoutes.stickers</p><ul><li>Defined in <a href="https://github.com/tiramisulabs/seyfert//blob/9ae4290ef220ffc96449d469d16d76b2031eca69/src/api/Routes/stickers.ts#L5">src/api/Routes/stickers.ts:5</a></li></ul></aside></div></li></ul></section><section class="tsd-panel tsd-member tsd-is-inherited"><h3 class="tsd-anchor-link" id="webhooks"><span>webhooks</span><a href="#webhooks" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><ul class="tsd-signatures tsd-is-inherited"><li class="tsd-is-inherited"><div class="tsd-signature tsd-anchor-link" id="webhooks-1"><span class="tsd-kind-call-signature">webhooks</span><span class="tsd-signature-symbol">(</span><br/>    <span class="tsd-kind-parameter">id</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">,</span><br/><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>    <span class="tsd-kind-call-signature">delete</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">undefined</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIWebhook</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-call-signature">patch</span><span class="tsd-signature-symbol">(</span><br/>        <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <a href="../types/RestArguments.html" class="tsd-signature-type tsd-kind-type-alias">RestArguments</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTPatchAPIWebhookJSONBody</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>    <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIWebhook</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-signature-symbol">(</span><br/>        <span class="tsd-kind-parameter">token</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">,</span><br/>    <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>        <span class="tsd-kind-property">github</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>            <span class="tsd-kind-call-signature">post</span><span class="tsd-signature-symbol">(</span><br/>                <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <a href="../types/RestArguments.html" class="tsd-signature-type tsd-kind-type-alias">RestArguments</a><span class="tsd-signature-symbol">&lt;</span><br/>                    <span class="tsd-signature-type">RESTPostAPIWebhookWithTokenJSONBody</span><span class="tsd-signature-symbol">,</span><br/>                    <span class="tsd-signature-type">RESTPostAPIWebhookWithTokenGitHubQuery</span><span class="tsd-signature-symbol">,</span><br/>                <span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">undefined</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">APIMessage</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-property">messages</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">(</span><br/>            <span class="tsd-kind-parameter">id</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">,</span><br/>        <span class="tsd-signature-symbol">)</span> <span class="tsd-signature-symbol">=&gt;</span> <span class="tsd-signature-symbol">{</span><br/>            <span class="tsd-kind-call-signature">delete</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">undefined</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><br/>                <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-symbol">{</span> <span class="tsd-kind-property">thread_id</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span> <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIMessage</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-call-signature">patch</span><span class="tsd-signature-symbol">(</span><br/>                <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <a href="../types/RestArguments.html" class="tsd-signature-type tsd-kind-type-alias">RestArguments</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTPatchAPIWebhookWithTokenMessageJSONBody</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIMessage</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-property">slack</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>            <span class="tsd-kind-call-signature">post</span><span class="tsd-signature-symbol">(</span><br/>                <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <a href="../types/RestArguments.html" class="tsd-signature-type tsd-kind-type-alias">RestArguments</a><span class="tsd-signature-symbol">&lt;</span><br/>                    <span class="tsd-signature-type">RESTPostAPIWebhookWithTokenJSONBody</span><span class="tsd-signature-symbol">,</span><br/>                    <span class="tsd-signature-type">RESTPostAPIWebhookWithTokenSlackQuery</span><span class="tsd-signature-symbol">,</span><br/>                <span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">undefined</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">APIMessage</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-call-signature">delete</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">undefined</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTGetAPIWebhookWithTokenResult</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-call-signature">patch</span><span class="tsd-signature-symbol">(</span><br/>            <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <a href="../types/RestArguments.html" class="tsd-signature-type tsd-kind-type-alias">RestArguments</a><span class="tsd-signature-symbol">&lt;</span><br/>                <span class="tsd-signature-type">RESTPatchAPIWebhookWithTokenJSONBody</span><span class="tsd-signature-symbol">,</span><br/>                <span class="tsd-signature-type">RESTPatchAPIWebhookWithTokenMessageQuery</span><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>        <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTGetAPIWebhookWithTokenResult</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-call-signature">post</span><span class="tsd-signature-symbol">(</span><br/>            <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <a href="../types/RestArguments.html" class="tsd-signature-type tsd-kind-type-alias">RestArguments</a><span class="tsd-signature-symbol">&lt;</span><br/>                <span class="tsd-signature-type">RESTPostAPIWebhookWithTokenJSONBody</span><span class="tsd-signature-symbol">,</span><br/>                <span class="tsd-signature-type">RESTPostAPIWebhookWithTokenQuery</span><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>        <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">undefined</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">APIMessage</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/><span class="tsd-signature-symbol">}</span><a href="#webhooks-1" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></div><div class="tsd-description"><div class="tsd-parameters"><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameter-list"><li><span><span class="tsd-kind-parameter">id</span>: <span class="tsd-signature-type">string</span></span></li></ul></div><h4 class="tsd-returns-title">Returns <span class="tsd-signature-symbol">{</span><br/>    <span class="tsd-kind-call-signature">delete</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">undefined</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIWebhook</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-kind-call-signature">patch</span><span class="tsd-signature-symbol">(</span><br/>        <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <a href="../types/RestArguments.html" class="tsd-signature-type tsd-kind-type-alias">RestArguments</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTPatchAPIWebhookJSONBody</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>    <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIWebhook</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-signature-symbol">(</span><br/>        <span class="tsd-kind-parameter">token</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">,</span><br/>    <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>        <span class="tsd-kind-property">github</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>            <span class="tsd-kind-call-signature">post</span><span class="tsd-signature-symbol">(</span><br/>                <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <a href="../types/RestArguments.html" class="tsd-signature-type tsd-kind-type-alias">RestArguments</a><span class="tsd-signature-symbol">&lt;</span><br/>                    <span class="tsd-signature-type">RESTPostAPIWebhookWithTokenJSONBody</span><span class="tsd-signature-symbol">,</span><br/>                    <span class="tsd-signature-type">RESTPostAPIWebhookWithTokenGitHubQuery</span><span class="tsd-signature-symbol">,</span><br/>                <span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">undefined</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">APIMessage</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-property">messages</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">(</span><br/>            <span class="tsd-kind-parameter">id</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">,</span><br/>        <span class="tsd-signature-symbol">)</span> <span class="tsd-signature-symbol">=&gt;</span> <span class="tsd-signature-symbol">{</span><br/>            <span class="tsd-kind-call-signature">delete</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">undefined</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><br/>                <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-symbol">{</span> <span class="tsd-kind-property">thread_id</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span> <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIMessage</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>            <span class="tsd-kind-call-signature">patch</span><span class="tsd-signature-symbol">(</span><br/>                <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <a href="../types/RestArguments.html" class="tsd-signature-type tsd-kind-type-alias">RestArguments</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTPatchAPIWebhookWithTokenMessageJSONBody</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">APIMessage</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-property">slack</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span><br/>            <span class="tsd-kind-call-signature">post</span><span class="tsd-signature-symbol">(</span><br/>                <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <a href="../types/RestArguments.html" class="tsd-signature-type tsd-kind-type-alias">RestArguments</a><span class="tsd-signature-symbol">&lt;</span><br/>                    <span class="tsd-signature-type">RESTPostAPIWebhookWithTokenJSONBody</span><span class="tsd-signature-symbol">,</span><br/>                    <span class="tsd-signature-type">RESTPostAPIWebhookWithTokenSlackQuery</span><span class="tsd-signature-symbol">,</span><br/>                <span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">undefined</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">APIMessage</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-call-signature">delete</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">undefined</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">?:</span> <a href="../types/RestArgumentsNoBody.html" class="tsd-signature-type tsd-kind-type-alias">RestArgumentsNoBody</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTGetAPIWebhookWithTokenResult</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-call-signature">patch</span><span class="tsd-signature-symbol">(</span><br/>            <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <a href="../types/RestArguments.html" class="tsd-signature-type tsd-kind-type-alias">RestArguments</a><span class="tsd-signature-symbol">&lt;</span><br/>                <span class="tsd-signature-type">RESTPatchAPIWebhookWithTokenJSONBody</span><span class="tsd-signature-symbol">,</span><br/>                <span class="tsd-signature-type">RESTPatchAPIWebhookWithTokenMessageQuery</span><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>        <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RESTGetAPIWebhookWithTokenResult</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>        <span class="tsd-kind-call-signature">post</span><span class="tsd-signature-symbol">(</span><br/>            <span class="tsd-kind-parameter">args</span><span class="tsd-signature-symbol">:</span> <a href="../types/RestArguments.html" class="tsd-signature-type tsd-kind-type-alias">RestArguments</a><span class="tsd-signature-symbol">&lt;</span><br/>                <span class="tsd-signature-type">RESTPostAPIWebhookWithTokenJSONBody</span><span class="tsd-signature-symbol">,</span><br/>                <span class="tsd-signature-type">RESTPostAPIWebhookWithTokenQuery</span><span class="tsd-signature-symbol">,</span><br/>            <span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">,</span><br/>        <span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">undefined</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">APIMessage</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>    <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">;</span><br/><span class="tsd-signature-symbol">}</span></h4><aside class="tsd-sources"><p>Inherited from WebhookRoutes.webhooks</p><ul><li>Defined in <a href="https://github.com/tiramisulabs/seyfert//blob/9ae4290ef220ffc96449d469d16d76b2031eca69/src/api/Routes/webhooks.ts#L29">src/api/Routes/webhooks.ts:29</a></li></ul></aside></div></li></ul></section></section></details></div><div class="col-sidebar"><div class="page-menu"><div class="tsd-navigation settings"><details class="tsd-accordion"><summary class="tsd-accordion-summary"><svg width="20" height="20" viewBox="0 0 24 24" fill="none" aria-hidden="true"><use href="../assets/icons.svg#icon-chevronDown"></use></svg><h3>Settings</h3></summary><div class="tsd-accordion-details"><div class="tsd-filter-visibility"><span class="settings-label">Member Visibility</span><ul id="tsd-filter-options"><li class="tsd-filter-item"><label class="tsd-filter-input"><input type="checkbox" id="tsd-filter-protected" name="protected"/><svg width="32" height="32" viewBox="0 0 32 32" aria-hidden="true"><rect class="tsd-checkbox-background" width="30" height="30" x="1" y="1" rx="6" fill="none"></rect><path class="tsd-checkbox-checkmark" d="M8.35422 16.8214L13.2143 21.75L24.6458 10.25" stroke="none" stroke-width="3.5" stroke-linejoin="round" fill="none"></path></svg><span>Protected</span></label></li><li class="tsd-filter-item"><label class="tsd-filter-input"><input type="checkbox" id="tsd-filter-inherited" name="inherited" checked/><svg width="32" height="32" viewBox="0 0 32 32" aria-hidden="true"><rect class="tsd-checkbox-background" width="30" height="30" x="1" y="1" rx="6" fill="none"></rect><path class="tsd-checkbox-checkmark" d="M8.35422 16.8214L13.2143 21.75L24.6458 10.25" stroke="none" stroke-width="3.5" stroke-linejoin="round" fill="none"></path></svg><span>Inherited</span></label></li><li class="tsd-filter-item"><label class="tsd-filter-input"><input type="checkbox" id="tsd-filter-external" name="external"/><svg width="32" height="32" viewBox="0 0 32 32" aria-hidden="true"><rect class="tsd-checkbox-background" width="30" height="30" x="1" y="1" rx="6" fill="none"></rect><path class="tsd-checkbox-checkmark" d="M8.35422 16.8214L13.2143 21.75L24.6458 10.25" stroke="none" stroke-width="3.5" stroke-linejoin="round" fill="none"></path></svg><span>External</span></label></li></ul></div><div class="tsd-theme-toggle"><label class="settings-label" for="tsd-theme">Theme</label><select id="tsd-theme"><option value="os">OS</option><option value="light">Light</option><option value="dark">Dark</option></select></div></div></details></div><details open class="tsd-accordion tsd-page-navigation"><summary class="tsd-accordion-summary"><svg width="20" height="20" viewBox="0 0 24 24" fill="none" aria-hidden="true"><use href="../assets/icons.svg#icon-chevronDown"></use></svg><h3>On This Page</h3></summary><div class="tsd-accordion-details"><details open class="tsd-accordion tsd-page-navigation-section"><summary class="tsd-accordion-summary" data-key="section-Properties"><svg width="20" height="20" viewBox="0 0 24 24" fill="none" aria-hidden="true"><use href="../assets/icons.svg#icon-chevronDown"></use></svg>Properties</summary><div><a href="#applications" class="tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>applications</span></a><a href="#gateway" class="tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>gateway</span></a><a href="#guilds" class="tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>guilds</span></a><a href="#interactions" class="tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>interactions</span></a><a href="#soundboard-default-sounds" class="tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>soundboard-<wbr/>default-<wbr/>sounds</span></a><a href="#stage-instances" class="tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>stage-<wbr/>instances</span></a><a href="#sticker-packs" class="tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>sticker-<wbr/>packs</span></a><a href="#users" class="tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>users</span></a><a href="#voice" class="tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>voice</span></a></div></details><details open class="tsd-accordion tsd-page-navigation-section"><summary class="tsd-accordion-summary" data-key="section-Methods"><svg width="20" height="20" viewBox="0 0 24 24" fill="none" aria-hidden="true"><use href="../assets/icons.svg#icon-chevronDown"></use></svg>Methods</summary><div><a href="#channels" class="tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Method"><use href="../assets/icons.svg#icon-2048"></use></svg><span>channels</span></a><a href="#invites" class="tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Method"><use href="../assets/icons.svg#icon-2048"></use></svg><span>invites</span></a><a href="#stickers" class="tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Method"><use href="../assets/icons.svg#icon-2048"></use></svg><span>stickers</span></a><a href="#webhooks" class="tsd-is-inherited"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Method"><use href="../assets/icons.svg#icon-2048"></use></svg><span>webhooks</span></a></div></details></div></details></div><div class="site-menu"><nav class="tsd-navigation"><a href="../modules.html">seyfert</a><ul class="tsd-small-nested-navigation" id="tsd-nav-container"><li>Loading...</li></ul></nav></div></div></div><footer><p class="tsd-generator">Generated using <a href="https://typedoc.org/" target="_blank">TypeDoc</a></p></footer><div class="overlay"></div></body></html>
