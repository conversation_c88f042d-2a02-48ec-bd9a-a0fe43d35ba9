/**
 * Base class for all Discord component builders providing common functionality.
 *
 * This module defines the foundational BaseComponentBuilder class that serves as the parent
 * for all Discord component builders. It provides essential functionality including data
 * storage, JSON serialization, and type-safe component construction patterns. The base
 * class ensures consistent behavior across all component types while maintaining flexibility
 * for specialized component implementations through generic typing and extensible architecture.
 */
import type { APIBaseComponent, ComponentType } from '../types';

export abstract class BaseComponentBuilder<
	TYPE extends Partial<APIBaseComponent<ComponentType>> = APIBaseComponent<ComponentType>,
> {
	constructor(public data: Partial<TYPE> = {}) {}

	toJSON(): TYPE {
		return { ...this.data } as TYPE;
	}
}

export type OptionValuesLength = { max: number; min: number };
